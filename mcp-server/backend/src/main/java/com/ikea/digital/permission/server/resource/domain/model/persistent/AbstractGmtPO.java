package com.ikea.digital.permission.server.resource.domain.model.persistent;

import java.util.Date;

import com.ikea.digital.permission.core.context.UserHolder;

import lombok.Data;

@Data
public abstract class AbstractGmtPO {

	protected String creator = UserHolder.getUserInfo() == null ? "" : UserHolder.getUserInfo().getEmail();
	
	protected Date createGmt = new Date();

	protected String modifier;
	
	protected Date modifyGmt;
	
}