package com.ikea.digital.permission.server.feign;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import com.ikea.digital.permission.server.feign.model.CommonAPIResponse;
import com.ikea.digital.permission.server.feign.model.CupidStore;

/**
 * Created by <PERSON> on 2021/9/15
 **/
@FeignClient(name = "cupidStoreClient", url = "${cupid.url}")
public interface CupidStoreClient {

    @GetMapping(value = "/cupid/store/allStore")
    CommonAPIResponse<List<CupidStore>> queryAllStores();
}
