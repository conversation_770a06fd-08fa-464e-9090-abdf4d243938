#!/usr/bin/env node

/**
 * 🧪 Test Token Exchange Configuration
 *
 * Purpose:
 * 1. Test if Keycloak token exchange is properly configured
 * 2. Verify mcp-mcp-odi can exchange tokens for permission-service
 * 3. Test the complete flow with proper configuration
 */

import axios from 'axios';
import { getEnvironmentConfig } from '../dist/utils/env.js';
import { decodeJWTUnsafe } from '../dist/utils/jwt-utils.js';
import fs from 'fs';
import path from 'path';

/**
 * Load configuration
 */
function loadConfiguration() {
  const env = getEnvironmentConfig();
  
  let tokenData = null;
  try {
    const tokenFile = path.join(process.cwd(), 'mcp-mpc-odi.token.json');
    const tokenContent = fs.readFileSync(tokenFile, 'utf8');
    tokenData = JSON.parse(tokenContent);
  } catch (error) {
    console.error('⚠️ Could not load token file:', error.message);
  }

  return {
    JWT_TOKEN: tokenData?.access_token || '',
    REFRESH_TOKEN: tokenData?.refresh_token || '',
    TOKEN_DATA: tokenData,
    
    CLIENT_ID: env.OAUTH2_CLIENT_ID || 'mcp-mcp-odi',
    CLIENT_SECRET: env.OAUTH2_CLIENT_SECRET || '',
    KEYCLOAK_BASE_URL: env.KEYCLOAK_BASE_URL || 'https://keycloak.ingka-dt.cn/auth',
    KEYCLOAK_REALM: env.KEYCLOAK_REALM || 'master',
    
    TARGET_CLIENT_ID: 'permission-service',
    ORDERS_API_BASE: 'https://fe-dev-i.ingka-dt.cn/order-web'
  };
}

const CONFIG = loadConfiguration();

/**
 * Test 1: Basic Token Exchange
 */
async function testBasicTokenExchange() {
  console.log('🔄 ==================== BASIC TOKEN EXCHANGE TEST ====================');
  
  try {
    console.log('🔄 Testing basic token exchange...');
    console.log(`   Source client: ${CONFIG.CLIENT_ID}`);
    console.log(`   Target client: ${CONFIG.TARGET_CLIENT_ID}`);
    console.log(`   Client secret: ${CONFIG.CLIENT_SECRET ? 'Configured ✅' : 'Missing ❌'}`);
    
    const tokenEndpoint = `${CONFIG.KEYCLOAK_BASE_URL}/realms/${CONFIG.KEYCLOAK_REALM}/protocol/openid-connect/token`;
    console.log(`   Token endpoint: ${tokenEndpoint}`);
    
    // Method 1: Using Basic Auth (as shown in Keycloak docs)
    const basicAuth = Buffer.from(`${CONFIG.CLIENT_ID}:${CONFIG.CLIENT_SECRET}`).toString('base64');

    const params = new URLSearchParams({
      grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',
      subject_token: CONFIG.JWT_TOKEN,
      subject_token_type: 'urn:ietf:params:oauth:token-type:access_token',
      requested_token_type: 'urn:ietf:params:oauth:token-type:access_token'
      // Note: No audience parameter in the Keycloak example
    });

    console.log('📋 Request parameters (Method 1 - Basic Auth):');
    console.log(`   grant_type: urn:ietf:params:oauth:grant-type:token-exchange`);
    console.log(`   subject_token: ${CONFIG.JWT_TOKEN.substring(0, 50)}...`);
    console.log(`   Authorization: Basic ${basicAuth.substring(0, 20)}...`);

    const response1 = await axios.post(tokenEndpoint, params, {
      headers: {
        'Authorization': `Basic ${basicAuth}`,
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      timeout: 10000,
      validateStatus: () => true
    });

    console.log(`\n📊 Method 1 Response:`);
    console.log(`   Status: ${response1.status}`);
    console.log(`   Data: ${JSON.stringify(response1.data, null, 2)}`);

    if (response1.data.access_token) {
      console.log('✅ Method 1 (Basic Auth) successful!');
      return {
        success: true,
        method: 'Basic Auth',
        accessToken: response1.data.access_token,
        expiresIn: response1.data.expires_in,
        response: response1.data
      };
    }

    // Method 2: Using client_id/client_secret in body (our previous approach)
    const params2 = new URLSearchParams({
      grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',
      subject_token: CONFIG.JWT_TOKEN,
      subject_token_type: 'urn:ietf:params:oauth:token-type:access_token',
      client_id: CONFIG.CLIENT_ID,
      client_secret: CONFIG.CLIENT_SECRET,
      requested_token_type: 'urn:ietf:params:oauth:token-type:access_token'
    });

    console.log(`\n📋 Request parameters (Method 2 - Body Auth):');
    console.log(`   grant_type: urn:ietf:params:oauth:grant-type:token-exchange`);
    console.log(`   client_id: ${CONFIG.CLIENT_ID}`);

    const response2 = await axios.post(tokenEndpoint, params2, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      timeout: 10000,
      validateStatus: () => true
    });

    console.log(`\n📊 Method 2 Response:`);
    console.log(`   Status: ${response2.status}`);
    console.log(`   Data: ${JSON.stringify(response2.data, null, 2)}`);

    if (response2.data.access_token) {
      console.log('✅ Method 2 (Body Auth) successful!');
      return {
        success: true,
        method: 'Body Auth',
        accessToken: response2.data.access_token,
        expiresIn: response2.data.expires_in,
        response: response2.data
      };
    }

    // Both methods failed
    const response = response1.status !== 200 ? response1 : response2;

    console.log(`\n📊 Response:`);
    console.log(`   Status: ${response.status}`);
    console.log(`   Data: ${JSON.stringify(response.data, null, 2)}`);

    if (response.data.access_token) {
      console.log('✅ Token exchange successful!');
      console.log(`   New token expires in: ${response.data.expires_in}s`);
      console.log(`   Token type: ${response.data.token_type}`);
      
      // Decode and analyze the new token
      try {
        const newPayload = decodeJWTUnsafe(response.data.access_token);
        console.log('📊 New token info:');
        console.log(`     Client (azp): ${newPayload.azp}`);
        console.log(`     Audience: ${newPayload.aud ? newPayload.aud.slice(0, 3).join(', ') : 'N/A'}`);
        console.log(`     Subject: ${newPayload.sub}`);
        console.log(`     Expires: ${new Date(newPayload.exp * 1000).toISOString()}`);
      } catch (decodeError) {
        console.log('⚠️ Could not decode new token');
      }
      
      return {
        success: true,
        accessToken: response.data.access_token,
        expiresIn: response.data.expires_in
      };
    } else {
      console.log('❌ Token exchange failed');
      
      // Analyze the error
      if (response.data.error === 'access_denied') {
        console.log('💡 Error Analysis: access_denied');
        console.log('   This means token exchange is not properly configured');
        console.log('   Check the Keycloak configuration steps below');
      } else if (response.data.error === 'invalid_client') {
        console.log('💡 Error Analysis: invalid_client');
        console.log('   Client credentials are incorrect or client not found');
      } else if (response.data.error === 'unsupported_grant_type') {
        console.log('💡 Error Analysis: unsupported_grant_type');
        console.log('   Token exchange is not enabled for this client');
      }
      
      return { success: false, error: response.data };
    }
    
  } catch (error) {
    console.error('❌ Token exchange request failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Test 2: Alternative Token Exchange Methods
 */
async function testAlternativeTokenExchange() {
  console.log('\n🔄 ==================== ALTERNATIVE TOKEN EXCHANGE METHODS ====================');
  
  const alternatives = [
    {
      name: 'With scope parameter',
      params: {
        grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',
        subject_token: CONFIG.JWT_TOKEN,
        subject_token_type: 'urn:ietf:params:oauth:token-type:access_token',
        client_id: CONFIG.CLIENT_ID,
        client_secret: CONFIG.CLIENT_SECRET,
        audience: CONFIG.TARGET_CLIENT_ID,
        requested_token_type: 'urn:ietf:params:oauth:token-type:access_token',
        scope: 'openid profile'
      }
    },
    {
      name: 'With resource parameter',
      params: {
        grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',
        subject_token: CONFIG.JWT_TOKEN,
        subject_token_type: 'urn:ietf:params:oauth:token-type:access_token',
        client_id: CONFIG.CLIENT_ID,
        client_secret: CONFIG.CLIENT_SECRET,
        resource: CONFIG.TARGET_CLIENT_ID,
        requested_token_type: 'urn:ietf:params:oauth:token-type:access_token'
      }
    },
    {
      name: 'Client credentials for target',
      params: {
        grant_type: 'client_credentials',
        client_id: CONFIG.CLIENT_ID,
        client_secret: CONFIG.CLIENT_SECRET,
        scope: 'openid profile'
      }
    }
  ];
  
  for (const alt of alternatives) {
    console.log(`\n🔄 Testing: ${alt.name}`);
    
    try {
      const tokenEndpoint = `${CONFIG.KEYCLOAK_BASE_URL}/realms/${CONFIG.KEYCLOAK_REALM}/protocol/openid-connect/token`;
      const params = new URLSearchParams(alt.params);
      
      const response = await axios.post(tokenEndpoint, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        },
        timeout: 10000,
        validateStatus: () => true
      });
      
      console.log(`   Status: ${response.status}`);
      
      if (response.data.access_token) {
        console.log(`✅ ${alt.name} successful!`);
        console.log(`   Token expires in: ${response.data.expires_in}s`);
        return {
          success: true,
          method: alt.name,
          accessToken: response.data.access_token
        };
      } else {
        console.log(`❌ ${alt.name} failed: ${response.data.error || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.log(`❌ ${alt.name} error: ${error.message}`);
    }
  }
  
  return { success: false };
}

/**
 * Test 3: Test exchanged token with permission service
 */
async function testExchangedTokenWithAPI(exchangedToken) {
  console.log('\n🧪 ==================== TEST EXCHANGED TOKEN WITH API ====================');
  
  if (!exchangedToken) {
    console.log('❌ No exchanged token to test');
    return false;
  }
  
  try {
    console.log('🔄 Testing exchanged token with orders API...');
    console.log(`   Token: ${exchangedToken.substring(0, 50)}...`);
    
    const testUrl = `${CONFIG.ORDERS_API_BASE}/orders/search`;
    const requestData = {
      "storeIds": [],
      "baseSize": 10,
      "page": 1,
      "size": 10,
      "timestamp": Date.now()
    };
    
    // Test with Bearer authentication
    const response = await axios.post(testUrl, requestData, {
      headers: {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'Authorization': `Bearer ${exchangedToken}`,
        'dnt': '1',
        'origin': 'https://admin.ingka-dt.cn',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://admin.ingka-dt.cn/',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'x-custom-referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    console.log(`   Response data: ${JSON.stringify(response.data, null, 2)}`);
    
    if (response.status === 200 && response.data && response.data.code !== 401) {
      console.log('🎉 SUCCESS! Exchanged token works with API!');
      return true;
    } else {
      console.log('❌ Exchanged token failed with API');
      return false;
    }
    
  } catch (error) {
    console.error('❌ API test with exchanged token failed:', error.message);
    return false;
  }
}

/**
 * Show Keycloak configuration instructions
 */
function showKeycloakConfigInstructions() {
  console.log('\n📋 ==================== KEYCLOAK CONFIGURATION INSTRUCTIONS ====================');
  
  console.log('🔧 **If token exchange failed, configure Keycloak as follows:**');
  console.log('');
  console.log('**Step 1: Configure mcp-mcp-odi Client (Requester)**');
  console.log('   1. Login to Keycloak Admin Console');
  console.log('   2. Go to Clients → mcp-mcp-odi');
  console.log('   3. Settings tab → Authentication flow:');
  console.log('      ✅ Enable "Standard flow"');
  console.log('      ✅ Enable "Direct access grants"');
  console.log('      ✅ Enable "Service accounts roles"');
  console.log('   4. Click Save');
  console.log('');
  console.log('**Step 2: Configure Service Account Roles**');
  console.log('   1. In mcp-mcp-odi client → Service Account Roles tab');
  console.log('   2. Client Roles dropdown → Select "realm-management"');
  console.log('   3. Add these roles:');
  console.log('      ✅ impersonation');
  console.log('      ✅ token-exchange');
  console.log('   4. Click "Add selected"');
  console.log('');
  console.log('**Step 3: Configure permission-service Client (Target)**');
  console.log('   1. Go to Clients → permission-service');
  console.log('   2. Settings tab → Authentication flow:');
  console.log('      ✅ Enable "Standard flow"');
  console.log('      ✅ Enable "Direct access grants"');
  console.log('   3. Click Save');
  console.log('');
  console.log('**Step 4: Verify Client Secrets**');
  console.log('   1. Both clients should have valid client secrets');
  console.log('   2. Check Credentials tab for each client');
  console.log('');
  console.log('**Step 5: Test Again**');
  console.log('   Run: npm run test:token-exchange-config');
  console.log('');
}

/**
 * Main function
 */
async function testTokenExchangeConfig() {
  console.log('🧪 ==================== TOKEN EXCHANGE CONFIGURATION TEST ====================');
  console.log('🎯 Purpose: Test if Keycloak token exchange is properly configured\n');
  
  console.log('📋 Configuration:');
  console.log(`   Token File: ${CONFIG.TOKEN_DATA ? 'Loaded ✅' : 'Missing ❌'}`);
  console.log(`   Client ID: ${CONFIG.CLIENT_ID}`);
  console.log(`   Client Secret: ${CONFIG.CLIENT_SECRET ? 'Configured ✅' : 'Missing ❌'}`);
  console.log(`   JWT Token: ${CONFIG.JWT_TOKEN ? CONFIG.JWT_TOKEN.substring(0, 50) + '...' : 'Not available ❌'}`);
  console.log(`   Target Client: ${CONFIG.TARGET_CLIENT_ID}`);
  console.log('');
  
  if (!CONFIG.JWT_TOKEN || !CONFIG.CLIENT_SECRET) {
    console.log('❌ Missing required configuration');
    return;
  }
  
  // Test 1: Basic token exchange
  const basicResult = await testBasicTokenExchange();
  
  // Test 2: Alternative methods
  const altResult = await testAlternativeTokenExchange();
  
  // Test 3: Test with API if we got a token
  const exchangedToken = basicResult.accessToken || altResult.accessToken;
  const apiResult = await testExchangedTokenWithAPI(exchangedToken);
  
  // Show configuration instructions
  showKeycloakConfigInstructions();
  
  console.log('\n🏁 ==================== TOKEN EXCHANGE TEST COMPLETE ====================');
  console.log('✅ Token exchange configuration test finished');
  console.log('');
  console.log('🎯 **Results Summary:**');
  console.log(`   Basic Token Exchange: ${basicResult.success ? 'SUCCESS ✅' : 'Failed ❌'}`);
  console.log(`   Alternative Methods: ${altResult.success ? 'SUCCESS ✅' : 'Failed ❌'}`);
  console.log(`   API Integration: ${apiResult ? 'SUCCESS ✅' : 'Failed ❌'}`);
  console.log('');
  
  if (basicResult.success && apiResult) {
    console.log('🎉 **PERFECT! Token exchange is working completely!**');
    console.log('   Your Keycloak configuration is correct');
    console.log('   You can now use token exchange in your MCP server');
  } else if (basicResult.success) {
    console.log('🎯 **Token exchange works, but API integration needs work**');
    console.log('   Keycloak configuration is correct');
    console.log('   The exchanged token might need different usage pattern');
  } else {
    console.log('❌ **Token exchange configuration needs work**');
    console.log('   Follow the Keycloak configuration instructions above');
    console.log('   Then run this test again');
  }
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testTokenExchangeConfig().catch(error => {
    console.error('💥 Token exchange configuration test failed:', error);
    process.exit(1);
  });
}

export { testTokenExchangeConfig };
