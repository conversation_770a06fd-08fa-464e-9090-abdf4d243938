name: PR Description Validation

on:
  pull_request:
    types:
      - opened
      - synchronize
      - reopened
      - edited
jobs:
  validate-pr-description:
    runs-on: 
      - self-hosted
      - ubuntu-lts
      - ali    
    steps:
      - name: Check PR Description Format
        uses: actions/github-script@v6
        with:
          script: |
            const prDescription = context.payload.pull_request.body || "";

            // Define valid values for component and changeType
            const validComponents = [
              "Admin Portal", "Argo-rollout", "Coder", "Context Client", 
              "Github Action Runner", "Istio", "Jiralert", "K8S Component", 
              "Keycloak", "Kong", "Metabase", "Neuvector", 
              "Permission Service", "Rancher", "Sentry"
            ];
            const validChangeTypes = [
              "Test Change", "Minor Change", "Major Change", "Emergency Change"
            ];

            // Define regex for date format
            const dateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}\+\d{4}$/;

            // Parse PR description
            const lines = prDescription.split("\n").map(line => line.trim());
            const componentLine = lines.find(line => line.startsWith("component:"));
            const changeTypeLine = lines.find(line => line.startsWith("changeType:"));
            const changeCompletionDateLine = lines.find(line => line.startsWith("changeCompletionDate:"));

            // Validate each field
            const errors = [];
            if (!componentLine || !validComponents.includes(componentLine.replace("component:", "").trim())) {
              errors.push("Invalid or missing 'component'. Must be one of: " + validComponents.join(", "));
            }
            if (!changeTypeLine || !validChangeTypes.includes(changeTypeLine.replace("changeType:", "").trim())) {
              errors.push("Invalid or missing 'changeType'. Must be one of: " + validChangeTypes.join(", "));
            }
            if (!changeCompletionDateLine || !dateRegex.test(changeCompletionDateLine.replace("changeCompletionDate:", "").trim())) {
              errors.push("Invalid or missing 'changeCompletionDate'. Must match format: YYYY-MM-DDTHH:mm:ss.sss+ZZZZ");
            }

            // Fail the workflow if there are errors
            if (errors.length > 0) {
              core.setFailed("PR description validation failed:\n" + errors.join("\n"));
            } else {
              console.log("PR description validation passed.");
            }
