/**
 * Project Name:backend
 * File Name:UserRoleService.java
 * Package Name:com.ikea.digital.permission.server.resource.service
 * Date:Jun 13, 20241:33:59 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.service;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.Closure;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.functors.IfClosure;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import com.fasterxml.jackson.core.type.TypeReference;
import com.ikea.digital.permission.core.context.UserHolder;
import com.ikea.digital.permission.server.common.BkdConstants;
import com.ikea.digital.permission.server.common.OptTypeEnum;
import com.ikea.digital.permission.server.dto.UserAssignRoleDto;
import com.ikea.digital.permission.server.dto.UserGrantRoleDto;
import com.ikea.digital.permission.server.feign.model.StoreDO;
import com.ikea.digital.permission.server.repository.ClientRepository;
import com.ikea.digital.permission.server.repository.LogRepository;
import com.ikea.digital.permission.server.repository.RoleRepository;
import com.ikea.digital.permission.server.repository.UserRepository;
import com.ikea.digital.permission.server.repository.UserRoleRepository;
import com.ikea.digital.permission.server.resource.domain.model.Role;
import com.ikea.digital.permission.server.resource.domain.model.User;
import com.ikea.digital.permission.server.resource.domain.model.UserRole;
import com.ikea.digital.permission.server.resource.domain.model.persistent.LogPO;
import com.ikea.mas.permission.utils.JacksonUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * ClassName:UserRoleService <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jun 13, 2024 1:33:59 PM <br/>
 * <AUTHOR>	 
 */
@Slf4j
@Service
public class UserRoleService {
	
	@Autowired
	private UserRoleRepository userRoleRepository;
	
	@Autowired
	private UserRepository userRepository;
	
	@Autowired
	private UserCacheService userCacheService;
	
	@Autowired
	private StoreService storeService;
	
	@Autowired
	private RoleRepository roleRepository;
	
	@Autowired
	private ClientRepository clientRepository;
	
	@Autowired
	private LogRepository logRepository;
	
	/**
	 * 删除{@code clientId}下，所有已经分配的{@code storeId}的权限
	 * @param storeId
	 * @param clientId
	 */
	@Async
	public void batchRemoveStoreFromUserRoleTable(String storeId, String clientId) {
		log.info("==========start, storeId:{}=============", storeId);
		StopWatch sw = new StopWatch();
		sw.start();
		List<UserRole> list = userRoleRepository.selectByStoreId(storeId, clientId);
		int total = 0;
		for(;CollectionUtils.isNotEmpty(list);
				list = userRoleRepository.selectByStoreId(storeId, clientId)) {
			total += updateStoreIds(list, storeId);
		}
		sw.stop();
		log.info("==========end, storeId:{}, total:{}, totalTime:{}=============", storeId, total, sw.getTotalTimeSeconds());
		
	}
	
	private int updateStoreIds(List<UserRole> list, String storeId) {
		Iterator<UserRole> it = list.iterator();
		UserRole userRolePO;
		while(it.hasNext()) {
			userRolePO = it.next();
			
			List<String> storeList = JacksonUtils.readValue(userRolePO.getStoreIds(), new TypeReference<List<String>>() {});
			if(!storeList.contains(storeId)) {
				it.remove();
				continue;
			}
			
			storeList.remove(storeId);
			userRolePO.setStoreIds(JacksonUtils.object2JsonString(storeList));
		}
		
		int n = 0;
		if(CollectionUtils.isNotEmpty(list)) {
			n = list.size();
			userRoleRepository.updateStoreIdsBatch(list);
		}
		
		return n;
	}
	
	public void assignRole(UserGrantRoleDto dto) {
		UserAssignRoleDto bo = UserGrantRoleDto.toUserAssignRoleBo(dto);
		User user = userRepository.fetchUserById(UserHolder.getUserInfo().getUserId());
		String clientId = bo.getClientId();
		
		List<String> kcRoles = getUserClientKcRoleNames(user, clientId);
		bo.setKcRoleList(kcRoles);
		Map<String, List<String>> operatorCurrentClientRoleStoreMap = getUserClientRoleStores(user, clientId);
		bo.setPmRoleStoreMap(operatorCurrentClientRoleStoreMap);
		assignRole(bo);
		
		// 刷新用户缓存 - 目前只有sdk会使用这个缓存
		userCacheService.clearNewUserCache(dto.getClientId(), dto.getUserId());
	}
	
	private Map<String, List<String>> getUserClientRoleStores(User user, String clientId) {
		// 用户在keycloak中没有client下的权限，返回空map
		Map<String, List<Role>> clientRoleMap = user.getClientRoleMap();
		if(MapUtils.isEmpty(clientRoleMap)) {
			return Collections.emptyMap();
		}
		
		if(!clientRoleMap.containsKey(clientId)) {
			return Collections.emptyMap();
		}
		
		List<Role> kcRoles = clientRoleMap.get(clientId);
		if(CollectionUtils.isEmpty(kcRoles)) {
			return Collections.emptyMap();
		}
		
		// 系统默认权限赋予默认门店
		Map<String, List<String>> roleStoresMap = new HashMap<>();
		Closure<Role> ifclosure = IfClosure.ifClosure(role -> BkdConstants.SysRole.contains(role.getName()), 
				role -> roleStoresMap.put(role.getName(), Arrays.asList(BkdConstants.DEFAULT_STORE)));
		kcRoles.forEach(ifclosure::execute);
		
		// admin拥有所有role,所有store权限
		if(roleStoresMap.containsKey(BkdConstants.AppAdmin)) {
			putAllClientNonSysRolesIfHaveClientAdmin(roleStoresMap, clientId);
			return roleStoresMap;
		}
		
		// 用户在permisssion service中拥有的client下的role
		List<UserRole> userRoleStores = userRoleRepository.getByClientIdAnsUserId(clientId, user.getUserId());
		if(CollectionUtils.isEmpty(userRoleStores)) {
			return roleStoresMap;
		}
		
		userRoleStores.forEach(role -> {
			roleStoresMap.putIfAbsent(role.getRoleName(), JacksonUtils.readValue(role.getStoreIds(), new TypeReference<List<String>>() {}));
		});
		
		return roleStoresMap;
	}
	
	private void putAllClientNonSysRolesIfHaveClientAdmin(Map<String, List<String>> roleStoresMap, String currClientId) {
		List<String> storeIds = storeService.queryAllStores().stream().map(StoreDO::getStoreId).collect(Collectors.toList());
		List<Role> allRoles = roleRepository.getRoles(currClientId);
		// 非系统角色赋予所有store
		Closure<Role> ifclosure = IfClosure.ifClosure(role -> !BkdConstants.SysRole.contains(role.getName()), 
				role -> roleStoresMap.put(role.getName(), storeIds));
		allRoles.forEach(ifclosure::execute);
	}
	
	public void assignRole(UserAssignRoleDto bo) {
		
		// 权限验证
		bo.validate();
		
		String clientPkId = clientRepository.fetchClientPkIdByClientId(bo.getClientId());
		
        List<UserRole> userRoleOldData = userRoleRepository.getByClientIdAnsUserId(bo.getUserId(), bo.getClientId());

        User user = userRepository.fetchUserById(bo.getUserId());
		// 新增
		if(CollectionUtils.isNotEmpty(bo.getAddedList())) {
			userRoleRepository.appendRoleStore(user, bo.getClientId(), clientPkId, bo.getAddedList());
		}
		
		// 删除
		if(CollectionUtils.isNotEmpty(bo.getRemovedList())) {
			userRoleRepository.removeRoleStore(user, bo.getClientId(), clientPkId, bo.getRemovedList());
		}
		
        List<UserRole> userRoleNewData = userRoleRepository.getByClientIdAnsUserId(bo.getClientId(), bo.getUserId());
        logRepository.insert(new LogPO(OptTypeEnum.ASSIGN_ROLE.getCode(), bo.getUserId(), JacksonUtils.object2JsonString(bo),
				JacksonUtils.object2JsonString(userRoleOldData), JacksonUtils.object2JsonString(userRoleNewData)));
	}
	
	/**
	 * 获取用户在keycloak中拥有的client下的所有角色名称.<br/>
	 * @param user
	 * @param clientId
	 * @return List of role name
	 */
	private List<String> getUserClientKcRoleNames(User user, String clientId) {
		Map<String, List<Role>> clientRoleMap = user.getClientRoleMap();
		if(MapUtils.isEmpty(clientRoleMap)) {
			return Collections.emptyList();
		}
		
		if(!clientRoleMap.containsKey(clientId)) {
			return Collections.emptyList();
		}
		
		List<Role> kcRoles = clientRoleMap.get(clientId);
		if(CollectionUtils.isEmpty(kcRoles)) {
			return Collections.emptyList();
		}
		
		return kcRoles.stream().map(Role::getName).collect(Collectors.toList());
	}
	
}