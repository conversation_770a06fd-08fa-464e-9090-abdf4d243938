import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>r<PERSON><PERSON><PERSON>Hand<PERSON> } from 'express';
import { requireBearerAuth } from '@modelcontextprotocol/sdk/server/auth/middleware/bearerAuth.js';
import { ProxyOAuthServerProvider } from '@modelcontextprotocol/sdk/server/auth/providers/proxyProvider.js';
import { getEnvironmentConfig } from '../utils/env.js';

/**
 * Scope access types
 */
export type ScopeAccessType = 'read' | 'write' | 'admin';

/**
 * Scope validation mode
 */
export type ScopeValidationMode = 'all' | 'any';

/**
 * Enhanced OAuth2 middleware options
 */
export interface OAuth2MiddlewareOptions {
  /** Required scopes for access */
  requiredScopes?: string[];
  /** Resource metadata URL for WWW-Authenticate header */
  resourceMetadataUrl?: string;
  /** Scope validation mode: 'all' requires all scopes, 'any' requires at least one */
  scopeValidation?: ScopeValidationMode;
  /** Custom scope validator function */
  customScopeValidator?: (userScopes: string[], requiredScopes: string[]) => boolean;
  /** Enable detailed logging */
  enableLogging?: boolean;
}

/**
 * Get required scopes for different access types
 */
function getRequiredScopes(type: ScopeAccessType): string[] {
  const env = getEnvironmentConfig();
  const scopeMap = {
    read: env.OAUTH2_SCOPES,
    write: [...env.OAUTH2_WRITE_SCOPES, ...env.OAUTH2_SCOPES],
    admin: env.OAUTH2_ADMIN_SCOPES
  };
  return scopeMap[type] || env.OAUTH2_SCOPES;
}

/**
 * Create OAuth2 authentication middleware with enhanced scope validation
 */
export function createOAuth2Meddleware(
  provider: ProxyOAuthServerProvider,
  options: OAuth2MiddlewareOptions = {}
): RequestHandler {
  const env = getEnvironmentConfig();
  const {
    requiredScopes = [],
    resourceMetadataUrl,
    scopeValidation = env.OAUTH2_DEFAULT_SCOPE_VALIDATION,
    customScopeValidator,
    enableLogging = false
  } = options;

  return requireBearerAuth({
    verifier: provider,
    requiredScopes: requiredScopes,
    resourceMetadataUrl: resourceMetadataUrl,
    // Override scope validation if custom logic is needed
    ...(customScopeValidator || scopeValidation !== 'all' ? {
      customScopeValidator: (userScopes: string[], required: string[]) => {
        if (enableLogging) {
          console.log(`🔍 [OAuth2Middleware] Validating scopes:`, {
            userScopes,
            requiredScopes: required,
            validationMode: scopeValidation
          });
        }

        let result: boolean;
        let clientId = 'unknown';

        // Extract client ID from user scopes if available
        const clientScope = userScopes.find(scope => scope.startsWith('client:'));
        if (clientScope) {
          clientId = clientScope.replace('client:', '');
        }

        // Use custom validator if provided
        if (customScopeValidator) {
          result = customScopeValidator(userScopes, required);
          if (enableLogging) {
            console.log(`🎯 [OAuth2Middleware] Custom validation result: ${result}`);
          }
        } else if (scopeValidation === 'any') {
          // Built-in 'any' validation
          result = required.length === 0 || required.some(scope => userScopes.includes(scope));
          if (enableLogging) {
            console.error(`🎯 [OAuth2Middleware] 'any' validation:`, {
              userScopes,
              requiredScopes: required,
              result,
              hasAnyMatch: required.some(scope => userScopes.includes(scope))
            });
          }
        } else {
          // Default 'all' validation
          result = required.every(scope => userScopes.includes(scope));
          if (enableLogging) {
            console.log(`🎯 [OAuth2Middleware] 'all' validation result: ${result}`);
          }
        }

        // Log scope validation result
        import('./oauth-logger.js').then(({ globalOAuth2Logger }) => {
          globalOAuth2Logger.logScopeValidation(
            clientId,
            userScopes,
            required,
            scopeValidation,
            result
          );
        });

        return result;
      }
    } : {})
  });
}

// Note: Custom middleware functions removed - using MCP SDK's requireBearerAuth instead
// The MCP SDK provides standardized authentication middleware that handles:
// - Bearer token validation
// - Proper WWW-Authenticate headers
// - OAuth2 error responses
// - Resource metadata integration



/**
 * Middleware to validate required scopes
 */
export function requireScopes(requiredScopes: string[]): RequestHandler {
  return (req, res, next) => {
    const auth = (req as any).auth;
    
    if (!auth) {
      return res.status(401).json({
        error: 'unauthorized',
        error_description: 'Authentication required',
      });
    }

    const userScopes = auth.scopes || [];
    const hasRequiredScopes = requiredScopes.every(scope => 
      userScopes.includes(scope)
    );

    if (!hasRequiredScopes) {
      return res.status(403).json({
        error: 'insufficient_scope',
        error_description: `Required scopes: ${requiredScopes.join(', ')}`,
        scope: requiredScopes.join(' '),
      });
    }

    next();
  };
}

/**
 * Middleware to validate user roles (Keycloak-specific)
 */
export function requireRoles(requiredRoles: string[], clientId?: string): RequestHandler {
  return (req, res, next) => {
    const auth = (req as any).auth;
    
    if (!auth || !auth.userInfo) {
      return res.status(401).json({
        error: 'unauthorized',
        error_description: 'Authentication required',
      });
    }

    const userInfo = auth.userInfo;
    let userRoles: string[] = [];

    // Get roles from realm_access
    if (userInfo.realm_access?.roles) {
      userRoles = userRoles.concat(userInfo.realm_access.roles);
    }

    // Get roles from resource_access (client-specific roles)
    if (clientId && userInfo.resource_access?.[clientId]?.roles) {
      userRoles = userRoles.concat(userInfo.resource_access[clientId].roles);
    }

    const hasRequiredRoles = requiredRoles.every(role => 
      userRoles.includes(role)
    );

    if (!hasRequiredRoles) {
      return res.status(403).json({
        error: 'insufficient_privileges',
        error_description: `Required roles: ${requiredRoles.join(', ')}`,
      });
    }

    next();
  };
}

/**
 * Create middleware that requires ALL specified scopes
 */
export function requireAllScopes(
  provider: ProxyOAuthServerProvider,
  scopes: string[],
  options: Omit<OAuth2MiddlewareOptions, 'requiredScopes' | 'scopeValidation'> = {}
): RequestHandler {
  return createOAuth2Middleware(provider, {
    ...options,
    requiredScopes: scopes,
    scopeValidation: 'all'
  });
}

/**
 * Create middleware that requires ANY of the specified scopes
 */
export function requireAnyScope(
  provider: ProxyOAuthServerProvider,
  scopes: string[],
  options: Omit<OAuth2MiddlewareOptions, 'requiredScopes' | 'scopeValidation'> = {}
): RequestHandler {
  return createOAuth2Middleware(provider, {
    ...options,
    requiredScopes: scopes,
    scopeValidation: 'any'
  });
}

/**
 * Create middleware for read-only access
 * Uses configured OAuth2 scopes from environment
 */
export function requireReadAccess(
  provider: ProxyOAuthServerProvider,
  options: Omit<OAuth2MiddlewareOptions, 'requiredScopes'> = {}
): RequestHandler {
  const env = getEnvironmentConfig();

  return createOAuth2Middleware(provider, {
    ...options,
    requiredScopes: getRequiredScopes('read'),
    scopeValidation: env.OAUTH2_DEFAULT_SCOPE_VALIDATION,
    enableLogging: true
  });
}

/**
 * Create middleware for write access
 * Uses configured OAuth2 write scopes from environment
 */
export function requireWriteAccess(
  provider: ProxyOAuthServerProvider,
  options: Omit<OAuth2MiddlewareOptions, 'requiredScopes'> = {}
): RequestHandler {
  const env = getEnvironmentConfig();

  return createOAuth2Middleware(provider, {
    ...options,
    requiredScopes: getRequiredScopes('write'),
    scopeValidation: env.OAUTH2_DEFAULT_SCOPE_VALIDATION,
    enableLogging: true
  });
}

/**
 * Create middleware for admin access
 * Uses configured OAuth2 admin scopes from environment
 */
export function requireAdminAccess(
  provider: ProxyOAuthServerProvider,
  options: Omit<OAuth2MiddlewareOptions, 'requiredScopes'> = {}
): RequestHandler {
  const env = getEnvironmentConfig();

  return createOAuth2Middleware(provider, {
    ...options,
    requiredScopes: getRequiredScopes('admin'),
    scopeValidation: env.OAUTH2_DEFAULT_SCOPE_VALIDATION,
    enableLogging: true
  });
}

/**
 * Create middleware with role-based access control
 */
export function requireRole(
  provider: ProxyOAuthServerProvider,
  roles: string[],
  options: Omit<OAuth2MiddlewareOptions, 'customScopeValidator'> = {}
): RequestHandler {
  return createOAuth2Middleware(provider, {
    ...options,
    customScopeValidator: (userScopes: string[], _requiredScopes: string[]) => {
      // Check if user has any of the required roles
      const userRoles = userScopes.filter(scope => scope.startsWith('role:'));
      const requiredRoleScopes = roles.map(role => `role:${role}`);

      return requiredRoleScopes.some(roleScope => userRoles.includes(roleScope));
    }
  });
}

/**
 * Error handling middleware for OAuth2 errors (MCP compliant)
 */
export function oAuth2ErrorHandler(serverBaseUrl: string): ErrorRequestHandler {
  return (err: any, _req: any, res: any, next: any) => {
    if (err.name === 'UnauthorizedError' || err.status === 401) {
      // MCP spec requires WWW-Authenticate header with resource metadata URL (RFC9728)
      res.set('WWW-Authenticate', `Bearer realm="${serverBaseUrl}", resource_metadata="${serverBaseUrl}/.well-known/oauth-protected-resource"`);
      return res.status(401).json({
        error: 'invalid_token',
        error_description: err.message || 'The access token provided is invalid',
      });
    }

    if (err.name === 'ForbiddenError' || err.status === 403) {
      return res.status(403).json({
        error: 'insufficient_scope',
        error_description: err.message || 'The request requires higher privileges than provided by the access token',
      });
    }

    // Pass other errors to the default error handler
    next(err);
  };
}

/**
 * Middleware to add CORS headers for OAuth2 endpoints
 */
export function oAuth2CorsMiddleware(): RequestHandler {
  return (req, res, next) => {
    // Allow CORS for OAuth2 endpoints
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    
    if (req.method === 'OPTIONS') {
      return res.sendStatus(200);
    }
    
    next();
  };
}

/**
 * Middleware to log OAuth2 requests
 */
export function oAuth2LoggingMiddleware(): RequestHandler {
  return (req, _res, next) => {
    const timestamp = new Date().toISOString();
    const auth = (req as any).auth;
    
    console.error(`🔐 [OAuth2] ${timestamp} ${req.method} ${req.path}`, {
      userAgent: req.headers['user-agent'],
      clientId: auth?.clientId,
      sub: auth?.sub,
      scopes: auth?.scopes,
    });
    
    next();
  };
}
