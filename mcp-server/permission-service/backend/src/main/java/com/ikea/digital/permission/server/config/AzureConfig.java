package com.ikea.digital.permission.server.config;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.zalando.logbook.Logbook;
import org.zalando.logbook.okhttp.GzipInterceptor;
import org.zalando.logbook.okhttp.LogbookInterceptor;

import com.azure.identity.ClientSecretCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.ikea.mas.permission.config.prop.AzureProperties;
import com.microsoft.graph.core.authentication.AzureIdentityAuthenticationProvider;
import com.microsoft.graph.serviceclient.GraphServiceClient;

import okhttp3.OkHttpClient;

@EnableConfigurationProperties(AzureProperties.class)
@Configuration
public class AzureConfig {

	final static String[] scopes = new String[] {"https://graph.microsoft.com/.default"};
	
	
	@Bean
	public GraphServiceClient graphClient(AzureProperties azureConfig, Logbook logbook) throws Exception {
		ClientSecretCredential credential = new ClientSecretCredentialBuilder().clientId(azureConfig.getClientId()).tenantId(azureConfig.getTenantId()).clientSecret(azureConfig.getSecretKey()).build();

		if (null == scopes || null == credential) {
		    throw new RuntimeException("Unexpected error");
		}
		
		OkHttpClient client = new OkHttpClient.Builder()
		        .addNetworkInterceptor(new LogbookInterceptor(logbook))
		        .addNetworkInterceptor(new GzipInterceptor())
		        .build();
		
		return new GraphServiceClient(new AzureIdentityAuthenticationProvider(credential, new String[] {}, scopes), client);
		
		//return new GraphServiceClient(credential, scopes);
	}
	
}
