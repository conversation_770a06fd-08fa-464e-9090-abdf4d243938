/**
 * Project Name:backend
 * File Name:GroupVo.java
 * Package Name:com.ikea.digital.permission.server.resource.web.response
 * Date:Jun 14, 20242:15:19 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.vo;

import java.util.List;

import lombok.Data;

/**
 * ClassName:GroupVo <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jun 14, 2024 2:15:19 PM <br/>
 * <AUTHOR>	 
 */
@Data
public class GroupVo {
	
	private String id;
	
	private String name;
	
	private List<GroupVo> subGroup;

}

