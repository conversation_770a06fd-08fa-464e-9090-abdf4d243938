/**
 * Project Name:bff
 * File Name:StateCacheDto.java
 * Package Name:com.ikea.mas.permission.dto
 * Date:May 15, 20246:08:12 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.mas.permission.dto;

import java.util.Map;

import lombok.Data;

/**
 * ClassName:StateCacheDto <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     May 15, 2024 6:08:12 PM <br/>
 * <AUTHOR>	 
 */
@Data
public class StateCacheDto {
	
	private String state;
	
	private Map<String, String> cache;
	
	private Long expireTime;
	
}