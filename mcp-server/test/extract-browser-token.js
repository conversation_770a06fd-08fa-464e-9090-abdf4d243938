#!/usr/bin/env node

/**
 * 🔍 Browser Token Extractor
 *
 * Purpose:
 * 1. Help extract pm_user_token from browser
 * 2. Test the extracted token
 * 3. Show how to use it in MCP server
 */

import axios from 'axios';

/**
 * Test a pm_user_token
 */
async function testPmUserToken(token) {
  console.log('🧪 ==================== TESTING PM_USER_TOKEN ====================');
  
  if (!token || token === 'PASTE_YOUR_TOKEN_HERE') {
    console.log('❌ No token provided');
    console.log('');
    console.log('📋 **How to get your token:**');
    console.log('   1. Go to https://admin.ingka-dt.cn/app/orders-portal/oms/index');
    console.log('   2. Open Developer Tools (F12) → Network tab');
    console.log('   3. Refresh the page or navigate to orders');
    console.log('   4. Look for requests to fe-dev-i.ingka-dt.cn/order-web/');
    console.log('   5. Find Cookie header with test_orders-portal=...');
    console.log('   6. Copy the Base64 token value');
    console.log('   7. Run: TOKEN="your-token" npm run test:extract-token');
    console.log('');
    return false;
  }
  
  try {
    console.log('🔄 Testing pm_user_token...');
    console.log(`   Token: ${token.substring(0, 50)}...`);
    console.log(`   Length: ${token.length} characters`);
    
    // Decode and show token content
    try {
      const tokenString = Buffer.from(token, 'base64').toString();
      console.log(`   Decoded: ${tokenString}`);
      
      // Parse token format: <EMAIL>
      const parts = tokenString.split('@');
      if (parts.length === 2) {
        const ssoUUID = parts[0];
        const rightPart = parts[1];
        const rightParts = rightPart.split('.');
        
        if (rightParts.length >= 3) {
          const loginTime = rightParts[0];
          const userId = rightParts[1];
          const sessionTimeout = rightParts[rightParts.length - 1];
          
          console.log('📊 Token breakdown:');
          console.log(`     SSO UUID: ${ssoUUID}`);
          console.log(`     Login Time: ${loginTime} (${new Date(parseInt(loginTime)).toISOString()})`);
          console.log(`     User ID: ${userId}`);
          console.log(`     Session Timeout: ${sessionTimeout}s`);
          
          // Check if token is expired
          const loginTimeMs = parseInt(loginTime);
          const sessionTimeoutMs = parseInt(sessionTimeout) * 1000;
          const expiresAt = loginTimeMs + sessionTimeoutMs;
          const now = Date.now();
          const isExpired = now > expiresAt;
          
          console.log(`     Expires: ${new Date(expiresAt).toISOString()}`);
          console.log(`     Status: ${isExpired ? 'EXPIRED ❌' : 'VALID ✅'}`);
          
          if (isExpired) {
            console.log('⚠️ Token is expired - you need to get a fresh one from browser');
            return false;
          }
        }
      }
    } catch (decodeError) {
      console.log('⚠️ Could not decode token - might be invalid format');
    }
    
    // Test with orders API
    console.log('');
    console.log('🔄 Testing with orders API...');
    
    const testUrl = 'https://fe-dev-i.ingka-dt.cn/order-web/orders/search';
    const requestData = {
      "storeIds": [],
      "baseSize": 10,
      "page": 1,
      "size": 10,
      "timestamp": Date.now()
    };
    
    const response = await axios.post(testUrl, requestData, {
      headers: {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'Cookie': `test_orders-portal=${token}`,
        'dnt': '1',
        'origin': 'https://admin.ingka-dt.cn',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://admin.ingka-dt.cn/',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'x-custom-referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    
    if (response.status === 200 && response.data) {
      if (response.data.code === 401 || response.data.message === 'to login') {
        console.log('❌ Token authentication failed');
        console.log('   API returned login redirect - token is invalid or expired');
        console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
        return false;
      } else {
        console.log('✅ Token authentication successful!');
        console.log('   API returned data - token is working');
        if (response.data.data) {
          console.log(`   Data preview: ${JSON.stringify(response.data.data, null, 2).substring(0, 200)}...`);
        }
        return true;
      }
    } else {
      console.log(`❌ API request failed with status ${response.status}`);
      if (response.data) {
        console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
      }
      return false;
    }
    
  } catch (error) {
    console.error('❌ Token test failed:', error.message);
    return false;
  }
}

/**
 * Show how to use the token in MCP server
 */
function showMCPIntegration(token, isWorking) {
  console.log('\n🔧 ==================== MCP SERVER INTEGRATION ====================');
  
  if (!isWorking) {
    console.log('❌ Cannot show integration - token is not working');
    console.log('   Get a fresh token from browser first');
    return;
  }
  
  console.log('✅ Your token is working! Here\'s how to use it in MCP server:');
  console.log('');
  console.log('📋 **Option 1: Environment Variable**');
  console.log('   Add to your .env file:');
  console.log(`   STATIC_PM_USER_TOKEN=${token}`);
  console.log('');
  console.log('📋 **Option 2: Dynamic Configuration**');
  console.log('   Update service adapter to use this token:');
  console.log('   ```javascript');
  console.log('   const sessionCookie = `test_orders-portal=${token}`;');
  console.log('   // Use in API requests');
  console.log('   ```');
  console.log('');
  console.log('📋 **Option 3: Test with curl**');
  console.log('   ```bash');
  console.log(`   curl 'https://fe-dev-i.ingka-dt.cn/order-web/orders/search' \\`);
  console.log(`     -H 'content-type: application/json' \\`);
  console.log(`     -b 'test_orders-portal=${token}' \\`);
  console.log(`     --data-raw '{"storeIds":[],"baseSize":10,"page":1,"size":10,"timestamp":${Date.now()}}'`);
  console.log('   ```');
  console.log('');
  console.log('⏰ **Token Expiration**');
  console.log('   • This token will expire (usually in 1 hour)');
  console.log('   • You\'ll need to extract a fresh token when it expires');
  console.log('   • Consider implementing automatic token refresh');
  console.log('');
  console.log('🚀 **Production Setup**');
  console.log('   1. Implement OAuth2 flow in your frontend');
  console.log('   2. Extract pm_user_token from OAuth2 response');
  console.log('   3. Pass token to MCP server via headers');
  console.log('   4. MCP server uses token for backend API calls');
  console.log('   5. Refresh token automatically when expired');
}

/**
 * Main function
 */
async function extractBrowserToken() {
  console.log('🔍 ==================== BROWSER TOKEN EXTRACTOR ====================');
  console.log('🎯 Purpose: Extract and test pm_user_token from browser\n');
  
  // Get token from environment variable or prompt
  const token = process.env.TOKEN || 'PASTE_YOUR_TOKEN_HERE';
  
  console.log('📋 Configuration:');
  console.log(`   Token provided: ${token !== 'PASTE_YOUR_TOKEN_HERE' ? 'Yes ✅' : 'No ❌'}`);
  console.log('');
  
  // Test the token
  const isWorking = await testPmUserToken(token);
  
  // Show integration instructions
  showMCPIntegration(token, isWorking);
  
  console.log('\n🏁 ==================== EXTRACTION COMPLETE ====================');
  console.log('✅ Browser token extraction finished');
  console.log('');
  console.log('🎯 **Summary:**');
  console.log(`   Token provided: ${token !== 'PASTE_YOUR_TOKEN_HERE' ? 'Yes ✅' : 'No ❌'}`);
  console.log(`   Token working: ${isWorking ? 'Yes ✅' : 'No ❌'}`);
  console.log('');
  
  if (isWorking) {
    console.log('🎉 **SUCCESS! Your token is working and ready to use!**');
    console.log('   You can now integrate it with your MCP server.');
  } else {
    console.log('⚠️ **Next Steps:**');
    console.log('   1. Go to your orders portal in browser');
    console.log('   2. Extract pm_user_token from network requests');
    console.log('   3. Run: TOKEN="your-token" npm run test:extract-token');
  }
}

// Run the extractor if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  extractBrowserToken().catch(error => {
    console.error('💥 Browser token extraction failed:', error);
    process.exit(1);
  });
}

export { extractBrowserToken };
