package com.ikea.digital.permission.server.service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ikea.digital.permission.core.context.UserHolder;
import com.ikea.digital.permission.server.common.BkdConstants;
import com.ikea.digital.permission.server.common.OptTypeEnum;
import com.ikea.digital.permission.server.common.util.JacksonUtils;
import com.ikea.digital.permission.server.common.util.KeycloakManager;
import com.ikea.digital.permission.server.dto.RoleCreateOrUpdateDto;
import com.ikea.digital.permission.server.dto.RoleSearchDto;
import com.ikea.digital.permission.server.dto.UserGrantRoleDto.RoleStoreDto;
import com.ikea.digital.permission.server.exception.ResourceException;
import com.ikea.digital.permission.server.feign.model.StoreDO;
import com.ikea.digital.permission.server.repository.LogRepository;
import com.ikea.digital.permission.server.repository.RoleRepository;
import com.ikea.digital.permission.server.repository.UserRoleRepository;
import com.ikea.digital.permission.server.resource.domain.model.Role;
import com.ikea.digital.permission.server.resource.domain.model.persistent.LogPO;
import com.ikea.digital.permission.server.vo.RolePageVo;
import com.ikea.digital.permission.server.vo.SimpleRoleVo;

@Service
public class RoleService {
	
	@Autowired
	private KeycloakManager keycloakManager;
	
	@Autowired
	private UserRoleRepository userRoleRepo;
	
	@Autowired
	private LogRepository logRepository;
	
	@Autowired
	private RoleRepository roleRepository;
	
	@Autowired
	private UserCacheService userCacheService;
	
	@Autowired
	private StoreService storeService;
	
	public void createRole(RoleCreateOrUpdateDto dto, String userToken) {
		
		roleRepository.createRole(dto);
		
		RoleStoreDto roleStoreDto = new RoleStoreDto();
		roleStoreDto.setName(dto.getName());
		if(BkdConstants.SysRole.contains(dto.getName())) {
			roleStoreDto.setStoreIds(Collections.singletonList(BkdConstants.DEFAULT_STORE));
		} else {
			roleStoreDto.setStoreIds(storeService.queryAllStores().stream().map(StoreDO::getStoreId).collect(Collectors.toList()));
		}
		
		userRoleRepo.appendRole(dto.getClientId(), Collections.singletonList(roleStoreDto));
		
		// 清除当前用户redis缓存
		if(StringUtils.isNotBlank(userToken)) {
			userCacheService.clearUserCache(userToken, UserHolder.getUserInfo().getUserId());
		}
	}
	
	public void updateRole(RoleCreateOrUpdateDto dto) {
		String clientPkId = keycloakManager.getClientPkId(dto.getClientId());
		
		Role role = keycloakManager.preciseSearchRole(clientPkId, dto.getName());
		if(role == null) {
			throw new ResourceException(dto.getName() + "[不存在！]");
		}
		
		keycloakManager.updateRole(clientPkId,dto.getName(),dto.getDescription(), dto.getMenuIds());
		
		logRepository.insert(new LogPO(OptTypeEnum.EDIT_ROLE.getCode(),	dto.getName(), JacksonUtils.object2JsonString(dto),
				JacksonUtils.object2JsonString(role), null));
	}
	
	public void deleteRole(String clientId, String roleId) {
		Optional<Role> role = roleRepository.getRoleById(clientId, roleId);
		if(!role.isPresent()) {
			throw new ResourceException(400,"角色不存在.");
		}
		
		String roleName = role.get().getName();
		if(BkdConstants.SysRole.contains(roleName)) {
			throw new ResourceException(400,"系统默认权限不可修改.");
		}
		
		roleRepository.deleteRole(roleId);
		userRoleRepo.deleteByClientIdAndRoleName(clientId, roleName);
		logRepository.insert(new LogPO(OptTypeEnum.DELETE_ROLE.getCode(), roleName, JacksonUtils.kv2JsonString("clientId", clientId, "roleName", roleName), 
				null, null));
	}

	public RolePageVo searchRole(RoleSearchDto dto) {
		String clientPkId = keycloakManager.getClientPkId(dto.getClientId());
		List<Role> roles = keycloakManager.searchRole(clientPkId,dto.getName());
		RolePageVo result = new RolePageVo();
		result.setDataCount(roles.size());
		int totalPages = (int)Math.ceil(roles.size() / (double)dto.getPageSize());
		result.setPageCount(totalPages);
		if(dto.getPageIndex() > totalPages) {
			result.setRoleDto(Collections.emptyList());
			return result;
		}
		
		int startIndex = (dto.getPageIndex() - 1) * dto.getPageSize();
		int realSize = Math.min(roles.size() - startIndex, dto.getPageSize()); 
		roles = roles.subList(startIndex, startIndex + realSize);
		result.setRoleDto(roles);
		return result;
	}
	
	public Optional<Role> getRole(String roleName, String clientId) {
		String clientPkId = keycloakManager.getClientPkId(clientId);
		return keycloakManager.getRole(clientPkId, roleName);
	}
	
	public List<SimpleRoleVo> listRole(String clientId) {
		List<Role> roles = roleRepository.getRoles(clientId);
		if(CollectionUtils.isEmpty(roles)) {
			return Collections.emptyList();
		}
		return roles.stream().map(Role::toSimpleRoleVo).collect(Collectors.toList());
	}
	
	public List<Role> getRoles(String clientId) {
		return roleRepository.getRoles(clientId);
	}
	
	public List<String> getRoleUsers(String roleName, String clientId){
		String clientPkId = keycloakManager.getClientPkId(clientId);
		return keycloakManager.getRoleUsers(roleName, clientPkId);
	}
	
}