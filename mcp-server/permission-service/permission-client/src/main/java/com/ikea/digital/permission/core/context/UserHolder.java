package com.ikea.digital.permission.core.context;

import com.ikea.digital.permission.common.Constants;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.server.ServerWebExchange;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 3/4/23
 */
public class UserHolder {
    public static void setUserInfo(UserContext userContext) {
        Objects.requireNonNull(RequestContextHolder.getRequestAttributes()).setAttribute(Constants.USER_INFO, userContext, RequestAttributes.SCOPE_REQUEST);
    }

    public static void setUserInfo(UserContext userContext, ServerWebExchange exchange) {
        Map<String, Object> attributes = exchange.getAttributes();
        attributes.put(Constants.USER_INFO, userContext);
    }

    public static UserContext getUserInfo() {
        return (UserContext) Objects.requireNonNull(RequestContextHolder.getRequestAttributes()).getAttribute(Constants.USER_INFO, RequestAttributes.SCOPE_REQUEST);
    }

    public static UserContext getUserInfo(ServerWebExchange exchange) {
        Map<String, Object> attributes = exchange.getAttributes();
        return (UserContext) attributes.get(Constants.USER_INFO);
    }
}
