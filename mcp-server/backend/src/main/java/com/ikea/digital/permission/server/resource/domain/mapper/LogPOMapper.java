package com.ikea.digital.permission.server.resource.domain.mapper;

import com.ikea.digital.permission.server.resource.domain.model.persistent.LogPO;

public interface LogPOMapper {
	
    int deleteByPrimaryKey(Long id);

    int insert(LogPO row);

    int insertSelective(LogPO row);

    LogPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LogPO row);

    int updateByPrimaryKey(LogPO row);
}