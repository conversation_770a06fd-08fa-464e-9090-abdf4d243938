package com.ikea.digital.permission.server.common;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public final class BkdConstants {
	
	private BkdConstants() {}

//    public static final String CLIENT_ID = "clientId";
//
//    public static final String CLIENT_SECRET = "secret";
    
    public static final String ADMIN_PORTAL = "admin_poratl";
    
    public static final String DEFAULT_STORE = "856";
    
    public static final String AppWrite = "application_write";
    
    public static final String AppRoleManager = "_roleManager";
    
    public static final String AppMenuManager = "_menuManager";
    
    public static final String AppAdmin = "application_admin";
    
    public static final List<String> SysRole = Collections.unmodifiableList(
    		Arrays.asList(AppWrite, AppRoleManager, AppMenuManager, AppAdmin));
    
    // keycloak中role的属性
    public static final String ATTR_MENU_ID = "menuIds";
    
}
