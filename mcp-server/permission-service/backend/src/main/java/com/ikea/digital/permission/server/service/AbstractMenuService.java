/**
 * Project Name:backend
 * File Name:AbstractMenuService.java
 * Package Name:com.ikea.digital.permission.server.resource.service
 * Date:Jun 12, 20243:40:41 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.ikea.digital.permission.server.dto.ResourceNode;
import com.ikea.digital.permission.server.repository.MenuRepository;
import com.ikea.digital.permission.server.repository.RoleRepository;
import com.ikea.digital.permission.server.resource.domain.model.Menu;

/**
 * ClassName:AbstractMenuService <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jun 12, 2024 3:40:41 PM <br/>
 * <AUTHOR>	 
 */
public abstract class AbstractMenuService {
	
	protected Logger log = LoggerFactory.getLogger(getClass());
	
	private static final Integer MENU_MAX_DEEP = 5;
	
	@Autowired
	protected RoleRepository roleRepository;
	
	@Autowired
	protected MenuRepository menuRepository;
	
	/**
	 * 从role的menuIds属性中删除当前menuId及其子menu的Id<br/>
	 * 如果role有没和当前menu的同parent下面的其他menu关联，那么当前menu的parent也需要递归删除。
	 * @param role
	 * @param clientId
	 * @param menuId
	 */
	protected void removeMenuFromRole(String role, String clientId, String menuId) {
		List<Long> menuIds = roleRepository.getMenuIds(role, clientId).stream().map(Long::valueOf).collect(Collectors.toList());
		if(CollectionUtils.isEmpty(menuIds)) {
			return;
		}
		// 构建NTree
		ResourceNode root = buildResourceNode(menuIds);
		
		// 从NTree中找到当前menuId的node
		ResourceNode node = ResourceNode.findNode(menuId, root);
		if(Objects.isNull(node)) {
			return;
		}
		
		removeNode(node, clientId, menuId);
		
		// 修改role的menuIds属性
		List<String> newMenuIds = ResourceNode.getAllResourceIds(root);
		roleRepository.updateRoleMenuIds(role, clientId, newMenuIds);
	}
	
	protected abstract void removeNode(ResourceNode node, String clientId, String menuId);

	public ResourceNode buildResourceNode(List<Long> menuIds) {
		// 0、查询
        List<Menu> menuList = menuRepository.selectByClientIdAndIdsSelective(StringUtils.EMPTY, menuIds);
		if(CollectionUtils.isEmpty(menuList)) {
			return null;
		}
		
		ResourceNode root = ResourceNode.buildRoot();
		
		// 1、接菜单树结构
        List<ResourceNode> topResourceList = menuList.stream().filter(m -> m.getParentMenuId() == null).sorted(Comparator.comparing(Menu::getSortIndex))
        		.map(resource -> {
        			ResourceNode node = new ResourceNode();
        			node.setId(resource.getId().toString());
        			node.setParent(root);
        			return node;
        		}).collect(Collectors.toList());
        root.setChildren(topResourceList);
        
        Map<Long, List<Menu>> subResourceMap = menuList.stream().filter(m -> m.getParentMenuId() != null).collect(Collectors.groupingBy(Menu::getParentMenuId));
		for(ResourceNode node : topResourceList) {
			List<ResourceNode> subMenuDtoList = this.buildResourceNode(node, subResourceMap, 1);
			node.setChildren(subMenuDtoList);
		}
		
		return root;
	}
	
	private List<ResourceNode> buildResourceNode(ResourceNode parent, final Map<Long,List<Menu>> subResourceMap, int deep) {
		if(deep >= MENU_MAX_DEEP) {
			return Collections.emptyList();
		}
		deep = deep++;
		
		List<Menu> subResourceList = subResourceMap.get(Long.valueOf(parent.getId()));
		if(CollectionUtils.isEmpty(subResourceList)) {
			return Collections.emptyList();
		}
		
		List<ResourceNode> children = new ArrayList<>();
		ResourceNode node;
		for(Menu subMenu : subResourceList) {
			node = new ResourceNode();
			node.setId(subMenu.getId().toString());
			List<ResourceNode> nextSubMenuList = this.buildResourceNode(node, subResourceMap, deep);
			node.setChildren(nextSubMenuList);
			node.setParent(parent);
			children.add(node);
		}
		return children;
	}

}