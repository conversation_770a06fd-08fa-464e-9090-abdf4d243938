package com.ikea.digital.permission.core;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ikea.digital.permission.common.BizBaseResponse;
import com.ikea.digital.permission.common.BizRespCodeEnum;
import com.ikea.digital.permission.common.Constants;
import com.ikea.digital.permission.core.config.PermissionClientConfig;
import com.ikea.digital.permission.core.context.UserContext;
import com.ikea.digital.permission.core.context.UserHolder;
import com.ikea.digital.permission.exception.PermissionAccessException;
import com.ikea.digital.permission.util.CookieUtils;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

public class PermissionSender extends AbstractPermissionSender {

    public PermissionSender(PermissionClientConfig pmClientConfig, RestTemplate restTemplate) {
       super(pmClientConfig, restTemplate);
    }

    // 从服务端获取登陆url
    @SuppressWarnings("rawtypes")
    public void toLogin(HttpServletRequest request, HttpServletResponse response) {
        String requestURL = request.getRequestURL().toString();
        String bizOriginUrl = request.getParameter(Constants.BIZ_ORIGIN_URL);
        String refererURL = request.getHeader(HttpHeaders.REFERER) == null ? "" : request.getHeader(HttpHeaders.REFERER);
        String customerReferer = request.getHeader(Constants.CUSTOMER_REFERER);
        ResponseEntity<BizBaseResponse> responseEntity = toLogin(requestURL, bizOriginUrl, refererURL, customerReferer);

        BizBaseResponse<String> body = BizBaseResponse.error(BizRespCodeEnum.NOAUTH_ERROR, responseEntity.getBody().getData().toString());

        try (ServletOutputStream out = response.getOutputStream()) {
            out.print(new ObjectMapper().writeValueAsString(body));
            response.flushBuffer();
        } catch (Exception e) {
            log.error("failed to write response: {}", e.getMessage());
            throw new PermissionAccessException(BizRespCodeEnum.UNKNOW_ERROR);
        }
    }

    
    // 根据cookie获取用户信息
    public boolean fetchUserByCookie(HttpServletRequest request, HttpServletResponse response, String userCookie) {
        UserContext userContext = getUserInfo(userCookie);

        if (null == userContext) {
//            this.toLogin(request, response);
            return false;
        }

        UserHolder.setUserInfo(userContext);
        return true;
    }

    @SuppressWarnings("rawtypes")
    public void logout(HttpServletRequest request) {
        Cookie userTokenCookie = CookieUtils.getCookie(request, pmClientConfig.getEnv() + "_" + pmClientConfig.getClientId());
        if (userTokenCookie != null) {
            Map<String, String> params = new HashMap<>();
            params.put(Constants.USER_TOKEN, userTokenCookie.getValue());

            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(headers);
            log.info("request logout param {}", params);
            ResponseEntity<BizBaseResponse> responseEntity = restTemplate.exchange(this.buildFullUrl(pmClientConfig.getLogoutUrl(), params), HttpMethod.GET, requestEntity, BizBaseResponse.class);
            log.info("respond logout response {}", responseEntity.getBody());

            if (responseEntity.getStatusCode() != HttpStatus.OK || responseEntity.getBody() == null || !responseEntity.getBody().rspOk()) {
                throw new PermissionAccessException(BizRespCodeEnum.UNKNOW_ERROR);
            }
        }
    }
}


