package com.ikea.digital.permission.server.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Enumeration;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.ikea.digital.permission.core.config.PermissionClientConfig;
import com.ikea.digital.permission.server.common.BkdConstants;
import com.ikea.digital.permission.server.common.util.ApplicationHolder;
import com.ikea.digital.permission.server.common.util.CookieUtils;
import com.ikea.digital.permission.server.validator.ClientValidator;
import com.ikea.mas.permission.common.Constants;
import com.ikea.mas.permission.exception.AuthException;

@Configuration
public class PmInterceptorConfig implements WebMvcConfigurer {

	@Autowired
	private PermissionClientConfig pmClientConfig;
	
	@Autowired
	private ClientValidator clientValidator;
	
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        InterceptorRegistration authRegistration = registry.addInterceptor(new AuthInterceptor());
        authRegistration.order(0);
        authRegistration.addPathPatterns(getAuthIncludePaths());
        authRegistration.excludePathPatterns(getAuthExcludePaths());
    }

    private String [] getAuthIncludePaths() {
    	return new String[] {
    			"/rs/role/**",
        		"/rs/user/**",
        		"/rs/menu/**",
        		"/rs/group/**",
        		"/rs/org/**",
        		"/third/api/**",
        		"/user/auth/logout",
        		"/user/auth/getLoginUrl"
        };
    }
    
    private String[] getAuthExcludePaths() {
        return new String[] {};
    }
    
    private class AuthInterceptor implements HandlerInterceptor {

		@Override
        public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
			// ps浏览器过来的请求
			String userToken = CookieUtils.readCookie(request, pmClientConfig.getEnv() + "_" + pmClientConfig.getClientId());
        	if(StringUtils.isNotBlank(userToken)) {	
        		return preHandleFromHtml(request);
        	}
        	
        	// sdk 过来的请求
        	return preHandleFromSdk(request);
        }
		
		/**
		 * 设置当前clientId, 只处理浏览器过来的请求
		 * @param request
		 * @return
		 */
		private boolean preHandleFromHtml(HttpServletRequest request) {
			String uri = request.getRequestURI();
    		if(uri.endsWith("/rs/user/info")) {
    			if("pmself".equals(request.getParameter("self"))) {
    				ApplicationHolder.set(pmClientConfig.getClientId());
    				return true;
    			}
    			ApplicationHolder.set(BkdConstants.ADMIN_PORTAL);
    			return true;
    		}
			ApplicationHolder.set(pmClientConfig.getClientId());
    		return true;
		}
		
		/**
		 * 设置当前clientId, 只处理sdk过来的请求
		 * @param request
		 * @return
		 */
		private boolean preHandleFromSdk(HttpServletRequest request) {
			String clientId = StringUtils.EMPTY, clientSecret = StringUtils.EMPTY;
			Enumeration<String> headerNames = request.getHeaderNames();
			while  (headerNames.hasMoreElements()) {
	             String name = headerNames.nextElement();
	             if(name.equalsIgnoreCase(Constants.CLIENT_ID)) {
	            	 clientId = request.getHeader(name);
	             } else if (name.equalsIgnoreCase(Constants.CLIENT_SECRET)) {
	            	 clientSecret = request.getHeader(name);
	             }
			}

			boolean validateAccess = clientValidator.validateAccess(clientId, clientSecret);
			if(!validateAccess) {
				throw new AuthException(400, "client invalid!");
			}
    		
			ApplicationHolder.set(clientId);
			return true;
		}

        @Override
        public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        	ApplicationHolder.clear();
        }
    }
}


