package com.ikea.digital.permission.auth.service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;

import com.ikea.digital.permission.core.config.PermissionClientConfig;
import com.ikea.digital.permission.server.common.util.KeycloakManager;
import com.ikea.digital.permission.server.common.util.UrlUtil;
import com.ikea.mas.permission.common.PmToken;
import com.ikea.mas.permission.config.prop.KeycloakIdpProperties;
import com.ikea.mas.permission.config.prop.PermissionServerProperties;
import com.ikea.mas.permission.utils.RedisHelper;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class UserAuthService {

    @Autowired
    private RedisHelper redisOptHelper;

    @Autowired
    private PermissionServerProperties pmServerConfig;
    
    @Autowired
	private PermissionClientConfig pmClientConfig;

    @Autowired
    private KeycloakManager keycloakManager;
    
    @Autowired
    private KeycloakIdpProperties keycloakIdpProp;
    
    @Autowired
    @Qualifier("kcAuthRestClient")
    private RestClient restClient;
    
    public String createPmLoginUrl(String clientId, String redirectUrl, String referer, boolean internalNet) {
        String state = UUID.randomUUID().toString();
        Map<String,String> cacheMap = new HashMap<>();
        cacheMap.put("clientId", clientId);
        cacheMap.put("redirectUrl", redirectUrl);
        cacheMap.put("referer", referer == null ? "" : referer);
        cacheMap.put("internalNet", String.valueOf(internalNet));
        redisOptHelper.hmset(state, cacheMap, pmServerConfig.getUrlTimeout());

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("clientId", clientId);
        queryParams.put("redirectUrl", redirectUrl);
        queryParams.put("state", state);
        return UrlUtil.buildUrl(pmServerConfig.getLoginUrl(), queryParams);
    }

	public void logout(String userToken) {
		if(StringUtils.isNotBlank(userToken)) {
			redisOptHelper.delete(userToken);
			
			PmToken pmToken = PmToken.from(userToken);
			String userId = pmToken.getUserId();
			
//			String ssoKey = pmToken.getSsoUUID();
//			Long expireTime = redisOptHelper.getExpire(ssoKey);
//			if(expireTime > 0) {
//				redisOptHelper.hset(ssoKey, ApplicationHolder.get(), "-1", expireTime);
//			}
			
			redisOptHelper.delete(pmToken.getSsoUUID());
			
//			try {
//				keycloakManager.logout(userId);
//			} catch(Exception e) {
//				log.warn("keycloak logout failed!",e);
//			}
			
			log.info("用户:{}登出成功！", userId);
		}
		
	}
	
	public String getLogOutUrl(String redirectUrl) {
		String idpLogoutUrl = StringUtils.EMPTY;
		if(StringUtils.isNotBlank(redirectUrl)) {
			/** 
			 * 因为azure会检查post_logout_redirect_uri参数是否在允许列表中，
			 * 这里为了避免添加过多的customerized地址在azure上，所以这里先跳转到azure,
			 * 然后统一先跳转到bff,最后跳转到redirectUrl
			 */
			idpLogoutUrl = keycloakIdpProp.getIdpLogoutUrl(pmServerConfig.getLogoutUrl(redirectUrl));
		} else {
			// 先从bff logout然后redirect到azure
			idpLogoutUrl = pmServerConfig.getLogoutUrl(keycloakIdpProp.getIdpLogoutUrl());
		}
		
		String kcLogoutUrl = keycloakManager.getLogoutUrl(pmClientConfig.getClientId(), idpLogoutUrl);
		return kcLogoutUrl;
	}

}
