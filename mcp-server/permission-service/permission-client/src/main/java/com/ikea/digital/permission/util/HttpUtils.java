package com.ikea.digital.permission.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

@Slf4j
public class HttpUtils {
	
    private static final String LOCAL_HOST = "localhost";

    public static boolean exclusivePath(String path, List<String> excludeAuthenticationPaths) {
        AntPathMatcher antPathMatcher = new AntPathMatcher();

        if (CollectionUtils.isEmpty(excludeAuthenticationPaths)) {
            return false;
        }

        return excludeAuthenticationPaths.stream().anyMatch(t -> antPathMatcher.match(t, path));
    }

    public static String getDomain(String cookieDomain, String host) {
        if (StringUtils.isNotBlank(cookieDomain)) {
            return cookieDomain;
        }
        if (LOCAL_HOST.equals(host)) {
            return host;
        }
        String[] hostArray = host.split("\\.");
        return hostArray[hostArray.length - 2] + "." + hostArray[hostArray.length - 1];
    }
    
    public static String hmacString(String url, String md5Key) {
        try {
            Mac hmac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(md5Key.getBytes("UTF-8"), "HmacSHA256");
            hmac.init(secretKey);
            byte[] hash = hmac.doFinal(url.getBytes("UTF-8"));
            return toHexString(hash);
        } catch (Exception e) {
            log.error("生成HMAC-SHA256摘要时出错", e);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 将字节数组转换为十六进制字符串。
     *
     * @param bytes 要转换的字节数组
     * @return 转换后的十六进制字符串
     */
    private static String toHexString(byte[] bytes) {
        StringBuilder hs = new StringBuilder();
        String stmp;
        for (byte b : bytes) {
            stmp = Integer.toHexString(b & 0xFF);
            if (stmp.length() == 1)
                hs.append('0');
            hs.append(stmp);
        }
        return hs.toString().toLowerCase();
    }
}
