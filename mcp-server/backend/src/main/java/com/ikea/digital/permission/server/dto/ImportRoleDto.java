/**
 * Project Name:permission-server
 * File Name:ImportRole.java
 * Package Name:com.ikea.digital.permission.server.resource.domain.model
 * Date:Sep 25, 20232:32:06 PM
 * Copyright (c) 2023, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.dto;

import java.util.List;

import lombok.Data;

/**
 * ClassName:ImportRoleDto <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Sep 25, 2023 2:32:06 PM <br/>
 * <AUTHOR>	 
 */
@Data
public class ImportRoleDto {
	
	private String name;
	
	private String desc;
	
	private List<ImportMenuDto> menues;

}