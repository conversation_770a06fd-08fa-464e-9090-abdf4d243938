package com.ikea.digital.permission.server.common.util;

import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JacksonUtils {

    //配置ObjectMapper对象
    private static ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true)
            .configure(JsonParser.Feature.IGNORE_UNDEFINED,true);

    public static <T> T readValue(String jsonString, Class<T> valueType) {
        try {
            //字符串转Json对象
            return objectMapper.readValue(jsonString, valueType);
        } catch (Exception e) {
            log.error("readValue error: ",e);
        }
        return null;
    }
    
    public static <T> T readValue(String jsonString, TypeReference<T> valueTypeRef) {
        try {
            //字符串转Json对象
            return objectMapper.readValue(jsonString, valueTypeRef);
        } catch (Exception e) {
            log.error("readValue error: ",e);
        }
        return null;
    }

    public static String object2JsonString(Object o) {
        try {
            //字符串转Json对象
            return objectMapper.writeValueAsString(o);
        } catch (Exception e) {
           log.error("object2JsonString error: ", e);
        }
        return "";
    }
    
    public static String kv2JsonString(String key1, Object value1, String key2, Object value2) {
        Map<String, Object> map = new HashMap<>();
        map.put(key1, value1);
        map.put(key2, value2);
        return object2JsonString(map);
    }

    /**
     * json字符串转成list
     *
     * @param jsonString
     * @param cls
     * @return
     */
    public static <T> List<T> java2List(@NonNull String jsonString, Class<T> cls) {
        try {
            return objectMapper.readValue(jsonString, getCollectionType(List.class, cls));
        } catch (JsonProcessingException e) {
            String className = cls.getSimpleName();
            log.error(" parse json [{}] to class [{}] error：{}", jsonString, className, e);
        }
        return null;
    }


    /**
     * 获取泛型的Collection Type
     *
     * @param collectionClass 泛型的Collection
     * @param elementClasses  实体bean
     * @return JavaType Java类型
     */
    private static JavaType getCollectionType(Class<?> collectionClass, Class<?>... elementClasses) {
        return objectMapper.getTypeFactory().constructParametricType(collectionClass, elementClasses);
    }


    public static <T> T jsonNodeToObject(JsonNode jsonNode, Class<T> cls) throws JsonProcessingException {
        return objectMapper.treeToValue(jsonNode, cls);
    }


    /**
     * json字符串转成list
     *
     * @param jsonNode
     * @param cls
     * @return
     */
    public static <T> List<T> jsonNode2List(@NonNull JsonNode jsonNode, Class<T> cls) {
        try {
            String jsonStr = objectMapper.writeValueAsString(jsonNode);
            return objectMapper.readValue(jsonStr, getCollectionType(List.class, cls));
        } catch (JsonProcessingException e) {
            String className = cls.getSimpleName();
            log.error(" parse json [{}] to class [{}] error：{}", jsonNode, className, e);
        }
        return null;
    }
    
    public static <T> T readValue(InputStream is, TypeReference<T> valueTypeRef) {
    	try {
			return objectMapper.readValue(is, valueTypeRef);
		} catch (Exception e) {
			log.error(" parse json error.", e);
		}
    	return null;
    }
}
