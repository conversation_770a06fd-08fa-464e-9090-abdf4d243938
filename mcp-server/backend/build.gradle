plugins {
    id "org.springdoc.springdoc-openapi-gradle-plugin" version "1.8.1-SNAPSHOT"
}

version = '1.0-SNAPSHOT'

configurations {
	all*.exclude group: 'org.apache.logging.log4j', module: 'log4j-api'
	all*.exclude group: 'org.apache.logging.log4j', module: 'log4j-core'
}

dependencies {

	api project(':permission-client')
	api project(':common')
	
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-data-redis'
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.cloud:spring-cloud-starter-bootstrap'

	implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
	implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config'

	implementation 'org.hibernate.validator:hibernate-validator'

	implementation 'com.auth0:java-jwt:3.4.0'
	implementation 'com.microsoft.graph:microsoft-graph:6.4.0'
  	implementation 'com.azure:azure-identity:1.+'

	implementation group: 'org.keycloak', name: 'keycloak-admin-client', version: '23.0.4'
	
	//implementation group: 'org.keycloak', name: 'keycloak-spring-boot-starter', version: '23.0.4'
	

	implementation group: 'org.apache.commons', name: 'commons-lang3'
	implementation group: 'org.apache.commons', name: 'commons-collections4'

	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'

	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	
	implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter'
	
    //implementation group: 'com.github.pagehelper', name: 'pagehelper-spring-boot-starter'
    implementation 'org.postgresql:postgresql'
    
    implementation 'com.ikea.mas:ikea-log-tracing:2.0.0-SNAPSHOT'
    implementation 'org.zalando:logbook-okhttp:3.7.2'
    implementation 'org.zalando:logbook-jaxrs:3.7.2'
    
    
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'
    
    implementation 'org.apache.httpcomponents.client5:httpclient5'
    
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-api'
    
    
}

//bootRun {
//    jvmArgs += " -p permission-client-2.0.0-SNAPSHOT-plain.jar"
//    jvmArgs += " --add-modules permission-client"
//}

// gradle.getTaskGraph().whenReady {
//     project.tasks.all {
//         Task t = it
//         String taskName = it.name
//         println("--------taskName-----------:" + taskName + " :" + it.getPath())
//         it.getTaskDependencies().any {
//             println("-----------------taskName----dependsOn-----------------:")
//             it.getDependencies(t).findAll() {
//                 println("----------------------------------:" + it.getPath())
//             }
//         }
//     }
// }

// forkedSpringBootRun {
//     dependsOn ':permission-client:jar'
// }

//gradle.taskGraph.whenReady {taskGraph ->
//    if (taskGraph.hasTask(':permission-client:jar')) {
//        println '-----------------------'
//    }
//}


//bootRun {
//    doFirst {
//        def folder = new File( '/runner/_work/permission-service/permission-service/permission-client/build/libs/permission-client-2.0.0-SNAPSHOT-plain.jar' )
//        if( folder.exists() ) {
//            println '====================='
//        }
//        println '++++++++++++++++'
//    }
//}

// generateOpenApiDocs {
//     doFirst {
//         def folder = new File( '/runner/_work/permission-service/permission-service/permission-client/build/libs/permission-client-2.0.0-SNAPSHOT-plain.jar' )
//         if( folder.exists() ) {
//             println '====================='
//         }
        
//         println '++++++++++++++++'
        
//     }
// }

openApi {
    apiDocsUrl.set("http://localhost:8080/permission-service/v3/api-docs.yaml")
    outputFileName.set("$projectDir/openapi.yaml")
    waitTimeInSeconds.set(120)
    customBootRun {
        systemProperties = System.properties
    }
}