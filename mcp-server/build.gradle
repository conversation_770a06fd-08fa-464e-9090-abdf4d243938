plugins {
    id 'com.ikea.mas.springboot' version '3.2.4-RELEASE' apply false
    id 'io.spring.dependency-management' version '1.1.4' apply false
}

allprojects {

    repositories {

        maven {
            url "https://artifactory.cloud.ingka-system.cn/artifactory/cn-digital-hub-gradle-virtual"
            credentials {
                username = "${RT_CDH_USERNAME}"
                password = "${RT_CDH_TOKEN}"
            }
        }
    
        maven {
            url "https://artifactory.cloud.ingka-system.cn/artifactory/cn-digital-hub-maven-virtual"
            credentials {
                username = "${RT_CDH_USERNAME}"
                password = "${RT_CDH_TOKEN}"
            }
        }
    }
}

subprojects {
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'
    apply plugin: 'java'
    apply plugin: 'java-library'
    
    group = 'com.ikea.digital'
    
    java {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    [compileJava, compileTestJava].each() {
        it.options.encoding = "UTF-8"
        it.options.compilerArgs << "-parameters"
    }
}
