/**
 * Project Name:permission-client
 * File Name:PermissionWebFluxSender.java
 * Package Name:com.ikea.digital.permission.core
 * Date:Nov 2, 20234:24:14 PM
 * Copyright (c) 2023, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.core;

import org.springframework.http.HttpCookie;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.server.ServerWebExchange;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ikea.digital.permission.common.BizBaseResponse;
import com.ikea.digital.permission.common.BizRespCodeEnum;
import com.ikea.digital.permission.common.Constants;
import com.ikea.digital.permission.core.config.PermissionClientConfig;
import com.ikea.digital.permission.core.context.UserContext;
import com.ikea.digital.permission.core.context.UserHolder;
import com.ikea.digital.permission.exception.PermissionAccessException;

/**
 * ClassName:PermissionWebFluxSender <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Nov 2, 2023 4:24:14 PM <br/>
 * <AUTHOR>	 
 */
public class PermissionWebFluxSender extends AbstractPermissionSender {
	
	public PermissionWebFluxSender(PermissionClientConfig pmClientConfig, RestTemplate restTemplate) {
       super(pmClientConfig, restTemplate);
    }
	
	public boolean fetchUserByCookie(ServerWebExchange exchange, HttpCookie userCookie) {
        UserContext userContext = getUserInfo(userCookie.getValue());

        if (null == userContext) {
//            this.toLogin(exchange);
            return false;
        }

        UserHolder.setUserInfo(userContext, exchange);
        return true;
    }
	
	@SuppressWarnings("rawtypes")
	public String toLogin(ServerWebExchange exchange) {
        String requestURL = exchange.getRequest().getURI().toString();
        if (requestURL.indexOf("?") > 0) {
            requestURL = requestURL.substring(0, requestURL.indexOf("?"));
        }

        String bizOriginUrl = exchange.getRequest().getQueryParams().getFirst(Constants.BIZ_ORIGIN_URL);
        String refererURL = exchange.getRequest().getHeaders().get(HttpHeaders.REFERER) == null ? "" : exchange.getRequest().getHeaders().getFirst(HttpHeaders.REFERER);
        String customerReferer = exchange.getRequest().getHeaders().get(Constants.CUSTOMER_REFERER) == null ? "" : exchange.getRequest().getHeaders().getFirst(Constants.CUSTOMER_REFERER);

        ResponseEntity<BizBaseResponse> responseEntity = toLogin(requestURL, bizOriginUrl, refererURL, customerReferer);

        try {
            BizBaseResponse<String> bodyData = BizBaseResponse.error(BizRespCodeEnum.NOAUTH_ERROR, responseEntity.getBody().getData().toString());
            return new ObjectMapper().writeValueAsString(bodyData);
        } catch (JsonProcessingException e) {
            log.error("failed to write body data, reason: {}", e.getMessage());
            throw new PermissionAccessException(BizRespCodeEnum.UNKNOW_ERROR);
        }
    }

}

