/**
 * Project Name:permission-server
 * File Name:KcResteasyClientProvider.java
 * Package Name:com.ikea.digital.permission.server.common.config
 * Date:Feb 22, 20242:20:24 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.config;

import java.util.Optional;

import javax.net.ssl.SSLContext;

import org.keycloak.admin.client.spi.ResteasyClientClassicProvider;
import org.zalando.logbook.Logbook;
import org.zalando.logbook.jaxrs.LogbookClientFilter;

import com.ikea.digital.permission.server.common.util.SpringAppContextUtils;

import jakarta.ws.rs.client.Client;

/**
 * ClassName:KcResteasyClientProvider <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Feb 22, 2024 2:20:24 PM <br/>
 * <AUTHOR>	 
 */
public class KcResteasyClientProvider extends ResteasyClientClassicProvider {
	
	public Client newRestEasyClient(Object customJacksonProvider, SSLContext sslContext, boolean disableTrustManager) {
		Client client = super.newRestEasyClient(customJacksonProvider, sslContext, disableTrustManager);
		Optional.ofNullable(SpringAppContextUtils.getAppContext()).ifPresent(appContext -> {
			Logbook logbook = appContext.getBean(Logbook.class);
			client.register(new LogbookClientFilter(logbook));
		});
        return client;
    }
	
}