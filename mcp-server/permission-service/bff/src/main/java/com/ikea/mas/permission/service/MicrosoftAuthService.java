package com.ikea.mas.permission.service;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URI;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

import javax.imageio.ImageIO;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.ikea.mas.permission.config.prop.AzureProperties;
import com.ikea.mas.permission.dto.AzureUserDto;
import com.ikea.mas.permission.dto.TokenDto;
import com.ikea.mas.permission.exception.AuthException;
import com.ikea.mas.permission.service.feign.AzureGraphClient;
import com.microsoft.aad.msal4j.AuthorizationCodeParameters;
import com.microsoft.aad.msal4j.AuthorizationRequestUrlParameters;
import com.microsoft.aad.msal4j.ClientCredentialFactory;
import com.microsoft.aad.msal4j.ConfidentialClientApplication;
import com.microsoft.aad.msal4j.IAuthenticationResult;
import com.microsoft.aad.msal4j.Prompt;
import com.microsoft.aad.msal4j.PublicClientApplication;
import com.microsoft.aad.msal4j.ResponseMode;
import com.nimbusds.jose.util.Base64;
import com.nimbusds.oauth2.sdk.AuthorizationCode;
import com.nimbusds.openid.connect.sdk.AuthenticationErrorResponse;
import com.nimbusds.openid.connect.sdk.AuthenticationResponse;
import com.nimbusds.openid.connect.sdk.AuthenticationResponseParser;
import com.nimbusds.openid.connect.sdk.AuthenticationSuccessResponse;

import feign.Response;
import lombok.Data;

@Service
public class MicrosoftAuthService {

	@Autowired
	private AzureProperties azureConfig;
	
	@Autowired
	private AzureGraphClient azureGraphClient;
	
	public static final String BEARER_TOKEN_PREFIX = "Bearer ";
	
	public TokenDto getMicrosoftToken(String code, String state, String sessionState, String referer) throws Exception {
		Map<String, List<String>> params = new HashMap<>();
		params.put("code", Collections.singletonList(code));
		params.put("state", Collections.singletonList(state));
		params.put("session_state", Collections.singletonList(sessionState));
//		AuthenticationResponse authResponse = null;
		
		AuthenticationResponse authResponse = AuthenticationResponseParser.parse(new URI(referer), params);
		if (!(authResponse instanceof AuthenticationSuccessResponse)) {
			AuthenticationErrorResponse oidcResponse = (AuthenticationErrorResponse) authResponse;
			throw new AuthException(String.format("Request for auth code failed: %s - %s",oidcResponse.getErrorObject().getCode(), oidcResponse.getErrorObject().getDescription()));
		}
		AuthenticationSuccessResponse oidcResponse = (AuthenticationSuccessResponse) authResponse;
		IAuthenticationResult iAuthenticationResult = getAuthResultByAuthCode(oidcResponse.getAuthorizationCode(), referer);
		TokenDto tokenResult = new TokenDto();
		tokenResult.setAccessToken(iAuthenticationResult.accessToken());
		tokenResult.setIdToken(iAuthenticationResult.idToken());
		tokenResult.setScope(iAuthenticationResult.scopes());
		tokenResult.setExpiresIn((iAuthenticationResult.expiresOnDate().getTime() - System.currentTimeMillis())/1000);
		return tokenResult;
	}

	private IAuthenticationResult getAuthResultByAuthCode(AuthorizationCode authorizationCode, String currentUri) throws Exception {
		ConfidentialClientApplication app = createClientApplication();
		String authCode = authorizationCode.getValue();
		AuthorizationCodeParameters parameters = AuthorizationCodeParameters.builder(authCode, new URI(currentUri)).build();
		Future<IAuthenticationResult> future = app.acquireToken(parameters);
		return future.get();
	}

	private ConfidentialClientApplication createClientApplication() throws MalformedURLException {
		return ConfidentialClientApplication
				.builder(azureConfig.getClientId(),ClientCredentialFactory.createFromSecret(azureConfig.getSecretKey()))
				.authority(azureConfig.getAuthority())
				.build();
	}
	
	public AzureUserDto getAzureUserInfo(String idToken, String accessToken) {
        DecodedJWT jwt = JWT.decode(idToken);
        String email = jwt.getClaim("preferred_username").asString();
        AzureUserDto azureUser = new AzureUserDto();
        ResponseEntity<AzureUserDto> azureResponse = azureGraphClient.getUserDetail("Bearer " + accessToken, email);
        if (azureResponse != null && azureResponse.getStatusCode().equals(HttpStatus.OK)) {
        	azureUser = azureResponse.getBody();
        }
        azureUser.setEmail(email);
    	azureUser.setName(jwt.getClaim("name").asString());
    	
        Response imageResponse = azureGraphClient.getUserAvatar(accessToken);
        if (imageResponse.status() == HttpStatus.OK.value()) {
            try (InputStream is = imageResponse.body().asInputStream();
                 ByteArrayOutputStream os = new ByteArrayOutputStream()) {
                BufferedImage bufferedImage = ImageIO.read(is);
                ImageIO.write(bufferedImage, "jpeg", os);
                String base64 = Base64.encode(os.toByteArray()).toString();
                azureUser.setAvatar(base64);
            } catch (IOException ioException) {
            }
        }
        return azureUser;
    }
	
	@Data
	public static class JwtSub {
		String sub;
	}
	
	public AzureUserDto getAzureUserInfo(String accessToken) {
		String bearerToken = StringUtils.substring(accessToken, BEARER_TOKEN_PREFIX.length());
        DecodedJWT jwt = JWT.decode(bearerToken);
        String email = jwt.getClaim("unique_name").asString();
        AzureUserDto azureUser = new AzureUserDto();
        ResponseEntity<AzureUserDto> azureResponse = azureGraphClient.getUserDetail(accessToken, email);
        if (azureResponse != null && azureResponse.getStatusCode().equals(HttpStatus.OK)) {
        	azureUser = azureResponse.getBody();
        }
        azureUser.setSub(jwt.getClaim("xms_st").as(JwtSub.class).getSub());
        azureUser.setEmail(email);
        azureUser.setName(jwt.getClaim("name").asString());
        return azureUser;
    }
	
	public String generateMicrosoftLoginUrl(String nonce, String state, String callBackUrl) throws Exception {
        String updatedScopes = "";
        PublicClientApplication pca = PublicClientApplication.builder(azureConfig.getClientId())
                .authority(azureConfig.getAuthority()).build();
        AuthorizationRequestUrlParameters parameters = AuthorizationRequestUrlParameters
                .builder(callBackUrl, Collections.singleton(updatedScopes))
                .responseMode(ResponseMode.QUERY).prompt(Prompt.SELECT_ACCOUNT).state(state).nonce(nonce).build();
        return pca.getAuthorizationRequestUrl(parameters).toString();
    }

}
