/**
 * Project Name:permission-server
 * File Name:ResourceVo.java
 * Package Name:com.ikea.digital.permission.server.resource.domain.cmd.result
 * Date:Nov 16, 20232:43:31 PM
 * Copyright (c) 2023, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.vo;

import java.util.List;

import lombok.Data;

/**
 * ClassName:ResourceVo <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Nov 16, 2023 2:43:31 PM <br/>
 * <AUTHOR>	 
 */
@Data
public class ResourceVo {
	
	Long id;
	
	String name;
	
	String description;
	
	String path;
	
	String icon;
	
	Integer resourceType;
	
	Long parentMenuId;
	
	Integer order;
	
	String clientId;
	
	List<ResourceVo> subResource;
	
	List<String> storeIds;
	
}