spring:
  application:
    name: permission-service
  servlet:
    session:
      cookie:
        max-age: 39600
        path: /   

server:
  servlet:
    context-path: /permission-service
    
#logging:
#    level:
#      com.ikea.digital.permission.server.resource.domain.repository: info
    
#mybatis:
#  mapper-locations: mapper/*.xml
#  configuration:
#    map-underscore-to-camel-case: true
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl 

mybatis:
  type-aliases-package: com.ikea.digital.permission.server.resource.domain.model.persistent
  mapper-locations: classpath*:mapper/*Mapper.xml


logbook: 
  format: 
    style: logstash
  exclude: 
    - /permission-service/actuator/health