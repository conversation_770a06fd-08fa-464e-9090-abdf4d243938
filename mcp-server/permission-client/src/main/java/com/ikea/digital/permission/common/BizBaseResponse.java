package com.ikea.digital.permission.common;

public class BizBaseResponse<T> {
	private Integer code; 
    private String message;
    private T data;

    public static <T> BizBaseResponse<T> success() {
        return success(null);
    }

    public static <T> BizBaseResponse<T> success(T object) {
        BizBaseResponse<T> result = new BizBaseResponse<>();
        result.setCode(BizRespCodeEnum.SUCCESS.getCode());
        result.setMessage(BizRespCodeEnum.SUCCESS.getMsg());
        result.setData(object);
        return result;
    }

    public static <T> BizBaseResponse<T> error(BizRespCodeEnum codeEnum) {
        return error(codeEnum.getCode(), codeEnum.getMsg(), null);
    }

    public static <T> BizBaseResponse<T> error(BizRespCodeEnum codeEnum, T data) {
        return error(codeEnum.getCode(), codeEnum.getMsg(), data);
    }

    public static <T> BizBaseResponse<T> error(Integer code, String msg, T data) {
        BizBaseResponse<T> result = new BizBaseResponse<>();
        result.setCode(code);
        result.setMessage(msg);
        result.setData(data);
        return result;
    }

    public boolean rspOk() {
        return BizRespCodeEnum.SUCCESS.getCode() == code;
    }
    
    public boolean rspBadRequest() {
        return BizRespCodeEnum.INVALID_CLIENT.getCode() == code;
    }

    public boolean rspDenined() {
        return BizRespCodeEnum.NOAUTH_ERROR.getCode() == code;
    }

    public boolean rspServiceError() {
        return BizRespCodeEnum.UNKNOW_ERROR.getCode() == code;
    }

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}

	@Override
	public String toString() {
		return "BizBaseResponse [code=" + code + ", message=" + message + ", data=" + data + "]";
	}
	
}
