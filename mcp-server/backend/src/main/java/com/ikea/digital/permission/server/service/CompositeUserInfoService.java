/**
 * Project Name:backend
 * File Name:CompositeUserInfoService.java
 * Package Name:com.ikea.digital.permission.server.service
 * Date:Dec 4, 20241:41:41 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ikea.digital.permission.server.common.util.ApplicationHolder;
import com.ikea.digital.permission.server.vo.UserDetailVo;

/**
 * ClassName:CompositeUserInfoService <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Dec 4, 2024 1:41:41 PM <br/>
 * <AUTHOR>	 
 */
@Service
public class CompositeUserInfoService {
	
	@Autowired
	private List<AbstractUserInfoService> userInfoServices;
	
	public UserDetailVo fetchUserByUserToken(String userToken) {
		String clientId = ApplicationHolder.get();
		for (AbstractUserInfoService userInfoService : userInfoServices) {
			if (userInfoService.supportClient(clientId)) {
				return userInfoService.fetchUserByUserToken(userToken);
			}
			
		}
		return null;
	} 

}

