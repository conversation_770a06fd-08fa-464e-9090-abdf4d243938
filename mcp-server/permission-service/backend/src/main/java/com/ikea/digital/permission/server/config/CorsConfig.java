package com.ikea.digital.permission.server.config;

import java.util.Arrays;

import jakarta.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.HandlerExecutionChain;

import com.ikea.mas.permission.config.prop.PermissionServerProperties;

/**
 * 在本项目的spring boot 2.5.7版本中：
 * 0、对于非options请求,不需要此类，只需要在{@see WebMvcConfig#addCorsMappings}WebMvcConfig添加如下配置,CorsInterceptor会置于拦截器链的最前面。
 * 1、对于options类型请求,由于拦截器顺序问题,在CorsInterceptor之前的任一拦截器返回false都将导致流程结束，无法执行到CorsInterceptor相关逻辑,
 * 	   所以通常需要通过filter优先于拦截器运行这一特征来配置
 * 具体原因见 
 * @see org.springframework.web.servlet.handler.AbstractHandlerMapping#getCorsHandlerExecutionChain(HttpServletRequest,HandlerExecutionChain, CorsConfiguration)
 * @see CorsFilter
 * @see org.springframework.web.cors.DefaultCorsProcessor
 * 
 * created by st.z on 2022年9月28日
 */
@Configuration
public class CorsConfig  {
	
	@Autowired
	private PermissionServerProperties permissionServerConfig;
	
	private CorsConfiguration buildConfig() {
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        corsConfiguration.addAllowedHeader("*");
        corsConfiguration.setAllowCredentials(true);
        corsConfiguration.setAllowedMethods(Arrays.asList("GET","POST","OPTIONS"));
        corsConfiguration.setAllowedOrigins(permissionServerConfig.getAllowedOrigins());
        return corsConfiguration;
    }
    
    @Bean
    public FilterRegistrationBean<CorsFilter> corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", buildConfig());
        FilterRegistrationBean<CorsFilter> bean = new FilterRegistrationBean<>(new CorsFilter(source));
        bean.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return bean;
    } 

}
