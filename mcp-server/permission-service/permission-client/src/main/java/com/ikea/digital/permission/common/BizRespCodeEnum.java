package com.ikea.digital.permission.common;

public enum BizRespCodeEnum {
    // success
    SUCCESS(200, "success"),

    // client error
    INVALID_CLIENT(400, "invalid params"),
    NOAUTH_ERROR(401,"to login"),
    ACCESS_DENIED(4001,"Access Denied!"),

    // error
    UNKNOW_ERROR(500, "unknow error"),

    GET_LOGIN_URL_ERROR(5000, "failed to get login url, please retry.[5000]"),
    GET_USER_INFO_ERROR(5001, "failed to get user info, please retry.[5001]");
	

    private final Integer code;
	
    private final String msg;

    BizRespCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }
}
