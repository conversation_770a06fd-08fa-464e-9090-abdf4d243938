/**
 * Project Name:bff
 * File Name:SsoValidator.java
 * Package Name:com.ikea.mas.permission.validator
 * Date:May 16, 202411:29:49 AM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.mas.permission.validator;

import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ikea.mas.permission.common.Result;
import com.ikea.mas.permission.dto.SsoCacheDto;
import com.ikea.mas.permission.utils.RedisHelper;

/**
 * ClassName:SsoValidator <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     May 16, 2024 11:29:49 AM <br/>
 * <AUTHOR>	 
 */
@Component
public class SsoValidator {
	
	@Autowired
	private RedisHelper redisHelper;
	
	@SuppressWarnings("unchecked")
	public Result<SsoCacheDto> checkSso(String ssoUUID) {
		if(StringUtils.isBlank(ssoUUID)) {
			return Result.fail();
		}
		
		Long expireTime = redisHelper.getExpire(ssoUUID);
    	if(expireTime <= 0) {
    		return Result.fail();
    	}
    	
    	Map<String, String> ssoMap = (Map<String, String>) redisHelper.hgetall(ssoUUID);
    	if(MapUtils.isEmpty(ssoMap)) {
    		return Result.fail();
    	}
    	
    	SsoCacheDto dto = new SsoCacheDto();
    	dto.setSsoUUID(ssoUUID);
    	dto.setExpireTime(expireTime);
    	dto.setCache(ssoMap);
		
		return Result.ok(dto);
		
	}

}