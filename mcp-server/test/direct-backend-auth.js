#!/usr/bin/env node

/**
 * 🎯 Direct Backend Authentication
 *
 * Purpose:
 * 1. Use JWT token directly with backend services
 * 2. Try different authentication approaches
 * 3. Find working authentication method
 */

import axios from 'axios';
import { getEnvironmentConfig } from '../dist/utils/env.js';
import { decodeJWTUnsafe } from '../dist/utils/jwt-utils.js';
import fs from 'fs';
import path from 'path';

/**
 * Load configuration
 */
function loadConfiguration() {
  const env = getEnvironmentConfig();
  
  let tokenData = null;
  try {
    const tokenFile = path.join(process.cwd(), 'mcp-mpc-odi.token.json');
    const tokenContent = fs.readFileSync(tokenFile, 'utf8');
    tokenData = JSON.parse(tokenContent);
  } catch (error) {
    console.error('⚠️ Could not load token file:', error.message);
  }

  return {
    JWT_TOKEN: tokenData?.access_token || '',
    TOKEN_DATA: tokenData,
    ORDERS_API_BASE: 'https://fe-dev-i.ingka-dt.cn/order-web'
  };
}

const CONFIG = loadConfiguration();

/**
 * Test 1: JWT Bearer Authentication
 */
async function testJWTBearerAuth() {
  console.log('🔄 ==================== JWT BEARER AUTHENTICATION ====================');
  
  try {
    console.log('🔄 Testing JWT Bearer authentication...');
    
    const payload = decodeJWTUnsafe(CONFIG.JWT_TOKEN);
    console.log('📊 JWT Token Info:');
    console.log(`   User: ${payload.email} (${payload.name})`);
    console.log(`   Client: ${payload.azp}`);
    console.log(`   Audience: ${payload.aud ? payload.aud.slice(0, 5).join(', ') + '...' : 'N/A'}`);
    console.log(`   Expires: ${new Date(payload.exp * 1000).toISOString()}`);
    
    // Check if permission-service is in audience
    const hasPermissionService = payload.aud && payload.aud.includes('permission-service');
    console.log(`   Has permission-service audience: ${hasPermissionService ? '✅ Yes' : '❌ No'}`);
    
    const testUrl = `${CONFIG.ORDERS_API_BASE}/orders/search`;
    const requestData = {
      "storeIds": [],
      "baseSize": 10,
      "page": 1,
      "size": 10,
      "timestamp": Date.now()
    };
    
    console.log(`\n🔄 Testing with orders API...`);
    console.log(`   URL: ${testUrl}`);
    console.log(`   Method: Bearer token authentication`);
    
    const response = await axios.post(testUrl, requestData, {
      headers: {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'Authorization': `Bearer ${CONFIG.JWT_TOKEN}`,
        'dnt': '1',
        'origin': 'https://admin.ingka-dt.cn',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://admin.ingka-dt.cn/',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'x-custom-referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    console.log(`   Response data: ${JSON.stringify(response.data, null, 2)}`);
    
    if (response.status === 200 && response.data && response.data.code !== 401) {
      console.log('🎉 SUCCESS! JWT Bearer authentication works!');
      console.log('   Your JWT token can be used directly with the API');
      return { success: true, method: 'Bearer' };
    } else if (response.data?.code === 401) {
      console.log('❌ JWT Bearer authentication failed');
      console.log('   API requires different authentication method');
      return { success: false, method: 'Bearer' };
    } else {
      console.log('⚠️ Unexpected response from JWT Bearer authentication');
      return { success: false, method: 'Bearer' };
    }
    
  } catch (error) {
    console.error('❌ JWT Bearer authentication failed:', error.message);
    return { success: false, method: 'Bearer' };
  }
}

/**
 * Test 2: JWT as Custom Header
 */
async function testJWTCustomHeader() {
  console.log('\n🔄 ==================== JWT CUSTOM HEADER AUTHENTICATION ====================');
  
  try {
    console.log('🔄 Testing JWT as custom header...');
    
    const testUrl = `${CONFIG.ORDERS_API_BASE}/orders/search`;
    const requestData = {
      "storeIds": [],
      "baseSize": 10,
      "page": 1,
      "size": 10,
      "timestamp": Date.now()
    };
    
    // Try different custom header names
    const headerVariations = [
      { name: 'X-Auth-Token', value: CONFIG.JWT_TOKEN },
      { name: 'X-JWT-Token', value: CONFIG.JWT_TOKEN },
      { name: 'X-Access-Token', value: CONFIG.JWT_TOKEN },
      { name: 'X-Keycloak-Token', value: CONFIG.JWT_TOKEN }
    ];
    
    for (const header of headerVariations) {
      console.log(`\n🔄 Trying header: ${header.name}`);
      
      const response = await axios.post(testUrl, requestData, {
        headers: {
          'accept': 'application/json, text/plain, */*',
          'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
          'cache-control': 'no-cache',
          'content-type': 'application/json',
          [header.name]: header.value,
          'dnt': '1',
          'origin': 'https://admin.ingka-dt.cn',
          'pragma': 'no-cache',
          'priority': 'u=1, i',
          'referer': 'https://admin.ingka-dt.cn/',
          'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"macOS"',
          'sec-fetch-dest': 'empty',
          'sec-fetch-mode': 'cors',
          'sec-fetch-site': 'same-site',
          'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
          'x-custom-referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
        },
        timeout: 10000,
        validateStatus: () => true
      });
      
      console.log(`   Response status: ${response.status}`);
      
      if (response.status === 200 && response.data && response.data.code !== 401) {
        console.log(`🎉 SUCCESS! Custom header ${header.name} works!`);
        console.log(`   Response data: ${JSON.stringify(response.data, null, 2)}`);
        return { success: true, method: header.name };
      } else if (response.data?.code === 401) {
        console.log(`❌ Custom header ${header.name} failed`);
      } else {
        console.log(`⚠️ Unexpected response for ${header.name}`);
      }
    }
    
    console.log('❌ All custom header variations failed');
    return { success: false, method: 'Custom Headers' };
    
  } catch (error) {
    console.error('❌ JWT custom header authentication failed:', error.message);
    return { success: false, method: 'Custom Headers' };
  }
}

/**
 * Test 3: Analyze API Response for Authentication Hints
 */
async function analyzeAPIResponse() {
  console.log('\n🔍 ==================== API RESPONSE ANALYSIS ====================');
  
  try {
    console.log('🔄 Analyzing API response for authentication hints...');
    
    const testUrl = `${CONFIG.ORDERS_API_BASE}/orders/search`;
    const requestData = {
      "storeIds": [],
      "baseSize": 10,
      "page": 1,
      "size": 10,
      "timestamp": Date.now()
    };
    
    // Make request without authentication
    const response = await axios.post(testUrl, requestData, {
      headers: {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'dnt': '1',
        'origin': 'https://admin.ingka-dt.cn',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://admin.ingka-dt.cn/',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'x-custom-referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    console.log(`   Response headers: ${JSON.stringify(response.headers, null, 2)}`);
    console.log(`   Response data: ${JSON.stringify(response.data, null, 2)}`);
    
    // Look for authentication hints
    const authHints = [];
    
    if (response.headers['www-authenticate']) {
      authHints.push(`WWW-Authenticate header: ${response.headers['www-authenticate']}`);
    }
    
    if (response.data?.message && response.data.message.includes('login')) {
      authHints.push(`Login redirect message: ${response.data.message}`);
    }
    
    if (response.data?.data && typeof response.data.data === 'string' && response.data.data.includes('toLogin')) {
      authHints.push(`Login URL in response: ${response.data.data}`);
    }
    
    if (authHints.length > 0) {
      console.log('🔍 Authentication hints found:');
      authHints.forEach(hint => console.log(`   • ${hint}`));
    } else {
      console.log('⚠️ No clear authentication hints found');
    }
    
    return {
      status: response.status,
      authHints,
      requiresAuth: response.data?.code === 401 || response.data?.message === 'to login'
    };
    
  } catch (error) {
    console.error('❌ API response analysis failed:', error.message);
    return { status: 'error', authHints: [], requiresAuth: true };
  }
}

/**
 * Main function
 */
async function testDirectBackendAuth() {
  console.log('🎯 ==================== DIRECT BACKEND AUTHENTICATION ====================');
  console.log('🎯 Purpose: Test direct authentication methods with backend APIs\n');
  
  console.log('📋 Configuration:');
  console.log(`   Token File: ${CONFIG.TOKEN_DATA ? 'Loaded ✅' : 'Missing ❌'}`);
  console.log(`   JWT Token: ${CONFIG.JWT_TOKEN ? CONFIG.JWT_TOKEN.substring(0, 50) + '...' : 'Not available ❌'}`);
  console.log(`   Orders API: ${CONFIG.ORDERS_API_BASE}`);
  console.log('');
  
  if (!CONFIG.JWT_TOKEN) {
    console.log('❌ No JWT token available. Please update mcp-mpc-odi.token.json');
    return;
  }
  
  // Test 1: JWT Bearer authentication
  const bearerResult = await testJWTBearerAuth();
  
  // Test 2: JWT as custom header
  const customHeaderResult = await testJWTCustomHeader();
  
  // Test 3: Analyze API response
  const analysisResult = await analyzeAPIResponse();
  
  console.log('\n🏁 ==================== DIRECT AUTHENTICATION COMPLETE ====================');
  console.log('✅ Direct backend authentication test finished');
  console.log('');
  console.log('🎯 **Results Summary:**');
  console.log(`   JWT Bearer: ${bearerResult.success ? 'SUCCESS ✅' : 'Failed ❌'}`);
  console.log(`   Custom Headers: ${customHeaderResult.success ? 'SUCCESS ✅' : 'Failed ❌'}`);
  console.log(`   API Analysis: ${analysisResult.requiresAuth ? 'Requires Auth ✅' : 'No Auth Required ❌'}`);
  console.log('');
  
  if (bearerResult.success) {
    console.log('🎉 **EXCELLENT! JWT Bearer authentication works!**');
    console.log('   You can use your JWT token directly with the API');
    console.log('   No need for pm_user_token or complex OAuth2 flows!');
    console.log('');
    console.log('🔧 **Integration with MCP Server:**');
    console.log('   ```javascript');
    console.log('   // In your service adapter');
    console.log('   headers: {');
    console.log(`     'Authorization': 'Bearer ${CONFIG.JWT_TOKEN.substring(0, 30)}...',`);
    console.log('     // other headers...');
    console.log('   }');
    console.log('   ```');
  } else if (customHeaderResult.success) {
    console.log(`🎉 **GREAT! Custom header ${customHeaderResult.method} works!**`);
    console.log('   You can use your JWT token with custom headers');
    console.log('');
    console.log('🔧 **Integration with MCP Server:**');
    console.log('   ```javascript');
    console.log('   // In your service adapter');
    console.log('   headers: {');
    console.log(`     '${customHeaderResult.method}': '${CONFIG.JWT_TOKEN.substring(0, 30)}...',`);
    console.log('     // other headers...');
    console.log('   }');
    console.log('   ```');
  } else {
    console.log('❌ **Direct JWT authentication not supported**');
    console.log('   The API requires pm_user_token session cookies');
    console.log('   Use browser token extraction as the solution');
    console.log('');
    console.log('💡 **Next Steps:**');
    console.log('   1. Extract working pm_user_token from browser');
    console.log('   2. Use: npm run test:extract-token');
    console.log('   3. Configure MCP server with working token');
  }
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testDirectBackendAuth().catch(error => {
    console.error('💥 Direct backend authentication test failed:', error);
    process.exit(1);
  });
}

export { testDirectBackendAuth };
