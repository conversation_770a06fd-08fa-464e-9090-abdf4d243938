package com.ikea.mas.permission.service.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

import com.ikea.mas.permission.config.FeignConfiguration;
import com.ikea.mas.permission.dto.AzureUserDto;

import feign.Response;

/**
 * client connect to Azure Graph API
 * Created by <PERSON> on 2021/5/25
 **/
@FeignClient(name = "azureGraphClient", url = "https://graph.microsoft.com", configuration = FeignConfiguration.class)
public interface AzureGraphClient {

    @GetMapping(value = "/beta/users/{email}")
    ResponseEntity<AzureUserDto> getUserDetail(@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
                                            @PathVariable(value = "email") String email);

    @GetMapping(value = "/v1.0/me/photo/$value")
    Response getUserAvatar(@RequestHeader(HttpHeaders.AUTHORIZATION) String token);

}
