package com.ikea.mas.permission.validator;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ikea.mas.permission.config.prop.PermissionServerProperties;
import com.ikea.mas.permission.config.prop.PermissionServerProperties.BizSystemInfo;

@Component
public class ClientValidator {
	
	@Autowired
	private PermissionServerProperties pmServerConfig;
	
	public boolean validateAccess(String clientId,String clientSecret) {
    	for(BizSystemInfo biz : pmServerConfig.getBizSystemList()) {
    		if(biz.validateAccess(clientId, clientSecret)) {
				return true;
			}
		}
    	return false;
    }

}