<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ikea.digital.permission.server.resource.domain.mapper.OrgUserPOMapper">
  <resultMap id="BaseResultMap" type="com.ikea.digital.permission.server.resource.domain.model.persistent.OrgUserPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="user_email" jdbcType="VARCHAR" property="userEmail" />
    <result column="client_id" jdbcType="VARCHAR" property="clientId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_gmt" jdbcType="TIMESTAMP" property="createGmt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, org_id, user_email, client_id, creator, create_gmt
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from organization_user
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from organization_user
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.ikea.digital.permission.server.resource.domain.model.persistent.OrgUserPO"
    useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into organization_user (org_id, user_email, 
      client_id, creator, create_gmt
      )
    values (#{orgId,jdbcType=BIGINT}, #{userEmail,jdbcType=VARCHAR}, 
      #{clientId,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{createGmt,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ikea.digital.permission.server.resource.domain.model.persistent.OrgUserPO"
    useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into organization_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        org_id,
      </if>
      <if test="userEmail != null">
        user_email,
      </if>
      <if test="clientId != null">
        client_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createGmt != null">
        create_gmt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="userEmail != null">
        #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="clientId != null">
        #{clientId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createGmt != null">
        #{createGmt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ikea.digital.permission.server.resource.domain.model.persistent.OrgUserPO">
    update organization_user
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="userEmail != null">
        user_email = #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="clientId != null">
        client_id = #{clientId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createGmt != null">
        create_gmt = #{createGmt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ikea.digital.permission.server.resource.domain.model.persistent.OrgUserPO">
    update organization_user
    set org_id = #{orgId,jdbcType=BIGINT},
      user_email = #{userEmail,jdbcType=VARCHAR},
      client_id = #{clientId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_gmt = #{createGmt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="selectByClientIdAndOrgIdIn" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from organization_user
    <where>
      <if test="clientId != null and clientId != ''">
        and client_id = #{clientId,jdbcType=VARCHAR}
      </if>
      <if test="orgIds != null">
        and org_id in 
        <foreach collection="orgIds" item = "orgId" separator="," open="(" close=")">
            #{orgId, jdbcType=BIGINT}
        </foreach>
      </if>
    </where>
  </select>
  
  <select id="selectSelectiveWithUserEmailIn" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from organization_user
    <where>
      <if test="clientId != null and clientId != ''">
        and client_id = #{clientId,jdbcType=VARCHAR}
      </if>
      <if test="orgId != null">
        and org_id = #{orgId,jdbcType=BIGINT}
      </if>
      <if test="userEmails != null">
        and user_email in 
        <foreach collection="userEmails" item = "userEmail" separator="," open="(" close=")">
            #{userEmail, jdbcType=VARCHAR}
        </foreach>
      </if>
    </where>
  </select>
  
  <select id="selectSelective" parameterType="com.ikea.digital.permission.server.resource.domain.model.persistent.OrgUserPO" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from organization_user
    <where>
      <if test="orgId != null">
        and org_id = #{orgId,jdbcType=BIGINT}
      </if>
      <if test="clientId != null and clientId != ''">
        and client_id = #{clientId,jdbcType=VARCHAR}
      </if>
      <if test="userEmail != null and userEmail != ''">
        and user_email = #{userEmail,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
  
  <insert id="insertList" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into organization_user (org_id, user_email, 
      client_id, creator, create_gmt) values 
    <foreach collection = "orgUsers" item = "user" separator = ",">
        (#{user.orgId}, #{user.userEmail}, #{user.clientId}, 
          #{user.creator,jdbcType=VARCHAR}, #{user.createGmt,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  
  <delete id="deleteOrgUser">
    delete from organization_user
    where client_id = #{clientId, jdbcType=VARCHAR} 
    and org_id = #{orgId, jdbcType=BIGINT}
    and user_email in 
    <foreach collection="userEmails" item = "userEmail" separator="," open="(" close=")">
        #{userEmail, jdbcType=VARCHAR}
    </foreach>
  </delete>
</mapper>