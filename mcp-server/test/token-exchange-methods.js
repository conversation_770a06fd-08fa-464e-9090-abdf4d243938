#!/usr/bin/env node

/**
 * 🧪 Token Exchange Methods Test
 *
 * Purpose:
 * 1. Test different token exchange approaches
 * 2. Determine which client secrets are actually needed
 * 3. Find the minimal configuration required
 */

import { globalKeycloakTokenExchange } from '../dist/auth/keycloak-token-exchange.js';
import axios from 'axios';

/**
 * Sample JWT token for testing
 */
const SAMPLE_JWT = 'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICIwYTQ4ZGU0Yy1jMDU4LTQ1YWUtYjkxZS03MGRkODRjMDQ4MDMifQ.eyJleHAiOjE3NTQyMTM1NTksImlhdCI6MTc1Mzk1NDM1OSwianRpIjoiZTBmMDQ2YmItZGJmNS00ZTljLTkxYWQtZDM2Njc2MmFiZjlmIiwiaXNzIjoiaHR0cHM6Ly9rZXljbG9hay5pbmdrYS1kdC5jbi9hdXRoL3JlYWxtcy9tYXN0ZXIiLCJzdWIiOiIzZDJlMjY1Yy1mODk1LTRlMzItYjNiZS01YTEzOWZhODU2YzEiLCJ0eXAiOiJTZXJpYWxpemVkLUlEIiwic2lkIjoiN2FiNDJiZDktYmFjMi00ODBkLWIzNzUtN2JhNThkZTIzMjM5Iiwic3RhdGVfY2hlY2tlciI6Ikp5bF9jV2RZLW51MnU5NG5VdzZ2TzZvMkVYN2twakwzeDBlVzBDeUdCRlUifQ.invalid_signature';

/**
 * Test configuration scenarios
 */
const TEST_SCENARIOS = [
  {
    name: 'No Client Secrets',
    config: {
      MCP_CLIENT_SECRET: '',
      PERMISSION_SERVICE_CLIENT_SECRET: ''
    }
  },
  {
    name: 'Only MCP Client Secret',
    config: {
      MCP_CLIENT_SECRET: 'test-mcp-secret',
      PERMISSION_SERVICE_CLIENT_SECRET: ''
    }
  },
  {
    name: 'Only Permission Service Secret',
    config: {
      MCP_CLIENT_SECRET: '',
      PERMISSION_SERVICE_CLIENT_SECRET: 'test-permission-secret'
    }
  },
  {
    name: 'Both Client Secrets',
    config: {
      MCP_CLIENT_SECRET: 'test-mcp-secret',
      PERMISSION_SERVICE_CLIENT_SECRET: 'test-permission-secret'
    }
  }
];

/**
 * Test token exchange requirements
 */
async function testTokenExchangeRequirements() {
  console.log('🔍 ==================== TOKEN EXCHANGE REQUIREMENTS TEST ====================');
  
  console.log('📋 Testing different configuration scenarios to determine requirements:');
  console.log('');
  
  for (const scenario of TEST_SCENARIOS) {
    console.log(`🔄 Testing: ${scenario.name}`);
    console.log(`   MCP Secret: ${scenario.config.MCP_CLIENT_SECRET ? 'Provided' : 'Not provided'}`);
    console.log(`   Permission Secret: ${scenario.config.PERMISSION_SERVICE_CLIENT_SECRET ? 'Provided' : 'Not provided'}`);
    
    // This would test with different configurations
    // For now, we'll simulate the results
    console.log(`   Result: Would test token exchange with this configuration`);
    console.log('');
  }
}

/**
 * Test manual token exchange calls
 */
async function testManualTokenExchange() {
  console.log('🔧 ==================== MANUAL TOKEN EXCHANGE TEST ====================');
  
  const keycloakBaseUrl = 'https://keycloak.ingka-dt.cn';
  const realm = 'master';
  const tokenEndpoint = `${keycloakBaseUrl}/auth/realms/${realm}/protocol/openid-connect/token`;
  
  console.log('🔄 Testing manual token exchange calls...');
  console.log(`   Endpoint: ${tokenEndpoint}`);
  console.log('');
  
  // Test 1: Public client token exchange (no secret)
  console.log('📋 Test 1: Public client token exchange (no client secret)');
  try {
    const params1 = new URLSearchParams({
      grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',
      subject_token: SAMPLE_JWT,
      subject_token_type: 'urn:ietf:params:oauth:token-type:access_token',
      client_id: 'mcp-mcp-odi',
      audience: 'permission-service',
      requested_token_type: 'urn:ietf:params:oauth:token-type:access_token'
    });
    
    console.log('   Request parameters:');
    console.log(`     grant_type: urn:ietf:params:oauth:grant-type:token-exchange`);
    console.log(`     client_id: mcp-mcp-odi`);
    console.log(`     audience: permission-service`);
    console.log(`     subject_token: ${SAMPLE_JWT.substring(0, 50)}...`);
    console.log('   Expected result: Likely to fail (most Keycloak setups require client secret)');
    console.log('');
  } catch (error) {
    console.log('   ❌ Would fail (expected with sample token)');
  }
  
  // Test 2: With source client secret
  console.log('📋 Test 2: Token exchange with source client secret (mcp-mcp-odi)');
  console.log('   Request parameters:');
  console.log(`     grant_type: urn:ietf:params:oauth:grant-type:token-exchange`);
  console.log(`     client_id: mcp-mcp-odi`);
  console.log(`     client_secret: [your-mcp-client-secret]`);
  console.log(`     audience: permission-service`);
  console.log('   Expected result: Most likely to work (standard approach)');
  console.log('');
  
  // Test 3: With target client secret
  console.log('📋 Test 3: Token exchange with target client secret (permission-service)');
  console.log('   Request parameters:');
  console.log(`     grant_type: urn:ietf:params:oauth:grant-type:token-exchange`);
  console.log(`     client_id: permission-service`);
  console.log(`     client_secret: [permission-service-secret]`);
  console.log(`     audience: permission-service`);
  console.log('   Expected result: May work, but less common pattern');
  console.log('');
}

/**
 * Analyze Keycloak token exchange documentation
 */
async function analyzeTokenExchangeStandards() {
  console.log('📚 ==================== TOKEN EXCHANGE STANDARDS ANALYSIS ====================');
  
  console.log('📖 RFC 8693 - OAuth 2.0 Token Exchange:');
  console.log('');
  console.log('🔑 **Client Authentication Requirements:**');
  console.log('   • The client making the token exchange request MUST authenticate');
  console.log('   • This is typically the client that wants to exchange the token');
  console.log('   • In your case: mcp-mcp-odi client should authenticate');
  console.log('');
  console.log('🎯 **Standard Pattern:**');
  console.log('   1. Client A (mcp-mcp-odi) has a token');
  console.log('   2. Client A wants to call Service B (permission-service)');
  console.log('   3. Client A exchanges its token for a token valid for Service B');
  console.log('   4. Client A authenticates with its own credentials during exchange');
  console.log('');
  console.log('✅ **Recommended Configuration:**');
  console.log('   • MCP_CLIENT_SECRET: Required (your client authenticates)');
  console.log('   • PERMISSION_SERVICE_CLIENT_SECRET: Not typically required');
  console.log('');
  console.log('⚠️ **Keycloak-Specific Notes:**');
  console.log('   • Keycloak may have different requirements than the standard');
  console.log('   • Some setups require target client secret for security');
  console.log('   • Public clients may not support token exchange');
  console.log('');
}

/**
 * Provide configuration recommendations
 */
async function provideConfigurationRecommendations() {
  console.log('💡 ==================== CONFIGURATION RECOMMENDATIONS ====================');
  
  console.log('🎯 **Minimal Configuration (Try First):**');
  console.log('```bash');
  console.log('# Enable token exchange');
  console.log('KEYCLOAK_TOKEN_EXCHANGE_ENABLED=true');
  console.log('');
  console.log('# Your MCP client (source client)');
  console.log('MCP_CLIENT_ID=mcp-mcp-odi');
  console.log('MCP_CLIENT_SECRET=your-mcp-client-secret  # ✅ Required');
  console.log('');
  console.log('# Target client (may not need secret)');
  console.log('PERMISSION_SERVICE_CLIENT_ID=permission-service');
  console.log('# PERMISSION_SERVICE_CLIENT_SECRET=  # ❓ Try without first');
  console.log('```');
  console.log('');
  console.log('🔧 **How to Get MCP Client Secret:**');
  console.log('   1. Go to Keycloak Admin Console');
  console.log('   2. Navigate to Clients → mcp-mcp-odi');
  console.log('   3. Go to Credentials tab');
  console.log('   4. Copy the Secret value');
  console.log('');
  console.log('🧪 **Testing Approach:**');
  console.log('   1. Configure only MCP_CLIENT_SECRET');
  console.log('   2. Test token exchange');
  console.log('   3. If it fails, add PERMISSION_SERVICE_CLIENT_SECRET');
  console.log('   4. Test again');
  console.log('');
  console.log('🚀 **Fallback Strategy:**');
  console.log('   • If token exchange fails, system automatically tries:');
  console.log('     1. Client credentials (permission-service)');
  console.log('     2. Direct PmToken creation');
  console.log('     3. Dynamic cookies');
  console.log('     4. Static cookies');
  console.log('');
}

/**
 * Test the actual implementation
 */
async function testCurrentImplementation() {
  console.log('🧪 ==================== CURRENT IMPLEMENTATION TEST ====================');
  
  console.log('🔄 Testing current token exchange implementation...');
  
  try {
    // This will test with current environment configuration
    const result = await globalKeycloakTokenExchange.exchangeToken(SAMPLE_JWT);
    
    if (result) {
      console.log('✅ Token exchange successful!');
      console.log(`   Result: ${result.substring(0, 50)}...`);
    } else {
      console.log('⚠️ Token exchange returned null');
      console.log('   This is expected with sample token and test configuration');
    }
  } catch (error) {
    console.log('❌ Token exchange failed:', error.message);
    console.log('   This is expected with sample token and test configuration');
  }
  
  console.log('');
  console.log('📊 Current configuration status:');
  console.log(`   KEYCLOAK_TOKEN_EXCHANGE_ENABLED: ${process.env.KEYCLOAK_TOKEN_EXCHANGE_ENABLED || 'false'}`);
  console.log(`   MCP_CLIENT_ID: ${process.env.MCP_CLIENT_ID || 'mcp-mcp-odi'}`);
  console.log(`   MCP_CLIENT_SECRET: ${process.env.MCP_CLIENT_SECRET ? 'Configured' : 'Not configured'}`);
  console.log(`   PERMISSION_SERVICE_CLIENT_SECRET: ${process.env.PERMISSION_SERVICE_CLIENT_SECRET ? 'Configured' : 'Not configured'}`);
}

/**
 * Main test function
 */
async function runTokenExchangeMethodsTest() {
  console.log('🧪 ==================== TOKEN EXCHANGE METHODS TEST SUITE ====================');
  console.log('🎯 Purpose: Determine minimal configuration needed for token exchange\n');

  // Test 1: Requirements analysis
  await testTokenExchangeRequirements();

  // Test 2: Manual token exchange analysis
  await testManualTokenExchange();

  // Test 3: Standards analysis
  await analyzeTokenExchangeStandards();

  // Test 4: Configuration recommendations
  await provideConfigurationRecommendations();

  // Test 5: Current implementation test
  await testCurrentImplementation();

  console.log('\n🏁 ==================== TEST SUITE COMPLETE ====================');
  console.log('✅ Token exchange methods analysis finished');
  console.log('');
  console.log('🎯 **Key Findings:**');
  console.log('   • MCP_CLIENT_SECRET: Likely required (standard OAuth2 pattern)');
  console.log('   • PERMISSION_SERVICE_CLIENT_SECRET: May not be required');
  console.log('   • Try minimal configuration first, add more if needed');
  console.log('');
  console.log('🚀 **Next Steps:**');
  console.log('   1. Get your mcp-mcp-odi client secret from Keycloak');
  console.log('   2. Configure MCP_CLIENT_SECRET in .env');
  console.log('   3. Test with real JWT token');
  console.log('   4. Add permission-service secret only if needed');
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTokenExchangeMethodsTest().catch(error => {
    console.error('💥 Test suite crashed:', error);
    process.exit(1);
  });
}

export { runTokenExchangeMethodsTest };
