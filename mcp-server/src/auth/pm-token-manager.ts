import { decodeJWTUnsafe, extractSessionInfo } from '../utils/jwt-utils.js';
import { v4 as uuidv4 } from 'uuid';

/**
 * PmToken structure matching the Java implementation
 */
export interface PmTokenData {
  ssoUUID: string;
  userId: string;
  clientSessionTimeOut: number;
  loginTime: number;
}

/**
 * Configuration for PmToken creation
 */
export interface PmTokenConfig {
  clientSessionTimeOut: number; // Default session timeout in seconds
  defaultSsoUUID?: string; // Optional default SSO UUID
}

/**
 * PmToken Manager
 * Creates pm_user_token from JWT tokens, matching the Java PmToken implementation
 */
export class PmTokenManager {
  private config: PmTokenConfig;

  constructor(config: PmTokenConfig = { clientSessionTimeOut: 3600 }) {
    this.config = config;
  }

  /**
   * Create pm_user_token from JWT token
   * This matches the Java PmToken.token() method
   */
  createPmUserTokenFromJWT(jwtToken: string): string {
    try {
      console.log('🔧 [PmTokenManager] Creating pm_user_token from JWT');
      
      // Extract session info from JWT
      const sessionInfo = extractSessionInfo(jwtToken);
      
      // Create PmToken data structure
      const pmTokenData: PmTokenData = {
        ssoUUID: this.config.defaultSsoUUID || uuidv4(), // Generate new SSO UUID
        userId: sessionInfo.userId,
        clientSessionTimeOut: this.config.clientSessionTimeOut,
        loginTime: Date.now()
      };

      // Create token string matching Java format: ${ssoUUID}@${loginTime}.${userId}.${clientSessionTimeOut}
      const tokenString = `${pmTokenData.ssoUUID}@${pmTokenData.loginTime}.${pmTokenData.userId}.${pmTokenData.clientSessionTimeOut}`;
      
      // Base64 encode (matching Java implementation)
      const pmUserToken = Buffer.from(tokenString).toString('base64');
      
      console.log('✅ [PmTokenManager] pm_user_token created successfully');
      console.log(`   User ID: ${pmTokenData.userId}`);
      console.log(`   SSO UUID: ${pmTokenData.ssoUUID}`);
      console.log(`   Session Timeout: ${pmTokenData.clientSessionTimeOut}s`);
      
      return pmUserToken;
    } catch (error) {
      console.error('❌ [PmTokenManager] Failed to create pm_user_token:', error);
      throw new Error(`Failed to create pm_user_token: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Parse pm_user_token back to PmTokenData
   * This matches the Java PmToken.from() method
   */
  parsePmUserToken(pmUserToken: string): PmTokenData | null {
    try {
      // Base64 decode
      const tokenString = Buffer.from(pmUserToken, 'base64').toString();
      
      // Validate format using regex (matching Java TOKEN_REGEXP)
      const UUID_REGEXP = '[a-f0-9]{8}(-[a-f0-9]{4}){3}-[a-f0-9]{12}';
      const TOKEN_REGEXP = new RegExp(`${UUID_REGEXP}@\\d{13}\\.${UUID_REGEXP}\\.\\d+`);
      
      if (!TOKEN_REGEXP.test(tokenString)) {
        console.error('❌ [PmTokenManager] Invalid pm_user_token format:', tokenString);
        return null;
      }
      
      // Parse components: <EMAIL>
      const parts = tokenString.split(/[@.]/);
      if (parts.length !== 4) {
        console.error('❌ [PmTokenManager] Invalid pm_user_token parts:', parts);
        return null;
      }
      
      return {
        ssoUUID: parts[0],
        loginTime: parseInt(parts[1]),
        userId: parts[2],
        clientSessionTimeOut: parseInt(parts[3])
      };
    } catch (error) {
      console.error('❌ [PmTokenManager] Failed to parse pm_user_token:', error);
      return null;
    }
  }

  /**
   * Create session cookie from pm_user_token
   * This matches the permission client cookie creation logic
   */
  createSessionCookieFromPmToken(pmUserToken: string, cookieName: string = 'test_orders-portal'): string {
    try {
      console.log('🍪 [PmTokenManager] Creating session cookie from pm_user_token');
      
      // Validate the token first
      const tokenData = this.parsePmUserToken(pmUserToken);
      if (!tokenData) {
        throw new Error('Invalid pm_user_token format');
      }
      
      // Create cookie in the format expected by the backend
      // The cookie value is the pm_user_token itself (Base64 encoded)
      const sessionCookie = `${cookieName}=${pmUserToken}`;
      
      console.log('✅ [PmTokenManager] Session cookie created successfully');
      console.log(`   Cookie: ${sessionCookie.substring(0, 50)}...`);
      
      return sessionCookie;
    } catch (error) {
      console.error('❌ [PmTokenManager] Failed to create session cookie:', error);
      throw error;
    }
  }

  /**
   * Complete JWT to session cookie conversion
   * This is the main method you'll use
   */
  async convertJWTToSessionCookie(jwtToken: string, cookieName: string = 'test_orders-portal'): Promise<string> {
    try {
      console.log('🔄 [PmTokenManager] Converting JWT to session cookie');
      
      // Step 1: Create pm_user_token from JWT
      const pmUserToken = this.createPmUserTokenFromJWT(jwtToken);
      
      // Step 2: Create session cookie from pm_user_token
      const sessionCookie = this.createSessionCookieFromPmToken(pmUserToken, cookieName);
      
      console.log('✅ [PmTokenManager] JWT to session cookie conversion complete');
      return sessionCookie;
    } catch (error) {
      console.error('❌ [PmTokenManager] JWT to session cookie conversion failed:', error);
      throw error;
    }
  }

  /**
   * Validate if a JWT token can be converted to pm_user_token
   */
  canConvertJWT(jwtToken: string): boolean {
    try {
      const sessionInfo = extractSessionInfo(jwtToken);
      return !!(sessionInfo.userId && sessionInfo.sessionId);
    } catch (error) {
      return false;
    }
  }

  /**
   * Get token expiration time
   */
  getTokenExpiration(pmUserToken: string): Date | null {
    const tokenData = this.parsePmUserToken(pmUserToken);
    if (!tokenData) {
      return null;
    }
    
    // Calculate expiration: loginTime + sessionTimeout
    const expirationMs = tokenData.loginTime + (tokenData.clientSessionTimeOut * 1000);
    return new Date(expirationMs);
  }

  /**
   * Check if pm_user_token is expired
   */
  isTokenExpired(pmUserToken: string): boolean {
    const expiration = this.getTokenExpiration(pmUserToken);
    if (!expiration) {
      return true;
    }
    
    return expiration.getTime() <= Date.now();
  }

  /**
   * Get token info for debugging
   */
  getTokenInfo(pmUserToken: string): any {
    const tokenData = this.parsePmUserToken(pmUserToken);
    if (!tokenData) {
      return null;
    }
    
    const expiration = this.getTokenExpiration(pmUserToken);
    const isExpired = this.isTokenExpired(pmUserToken);
    
    return {
      ...tokenData,
      expiration: expiration?.toISOString(),
      isExpired,
      timeToExpiry: expiration ? Math.max(0, expiration.getTime() - Date.now()) : 0
    };
  }
}

/**
 * Default configuration for orders-portal
 */
export const defaultPmTokenConfig: PmTokenConfig = {
  clientSessionTimeOut: 3600, // 1 hour
};

/**
 * Global PmToken manager instance
 */
export const globalPmTokenManager = new PmTokenManager(defaultPmTokenConfig);
