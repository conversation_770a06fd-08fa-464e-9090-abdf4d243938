package com.ikea.digital.permission.server.common.util;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * created by st.z on 2022年3月1日
 */
public class UrlUtil {

    private static final String CHARSET = "utf-8";

    public static String buildUrl(String url, Map<String, String> queryParam) {
        return url + "?" + buildQueryParameters(queryParam);
    }

    private static String buildQueryParameters(Map<String, String> params) {
        if (MapUtils.isEmpty(params)) {
            return StringUtils.EMPTY;
        }

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (entry.getKey() == null) {
                continue;
            }
            String value = entry.getValue() == null ? "" : entry.getValue();
            try {
                String encodedKey = URLEncoder.encode(entry.getKey(), CHARSET);
                String encodedValue = URLEncoder.encode(value, CHARSET);
                if (sb.length() > 0) {
                    sb.append('&');
                }
                sb.append(encodedKey);
                sb.append('=');
                sb.append(encodedValue);
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e.getMessage(), e);
            }
        }

        return sb.toString();
    }
}
