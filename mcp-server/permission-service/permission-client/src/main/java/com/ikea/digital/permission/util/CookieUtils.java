package com.ikea.digital.permission.util;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseCookie;

public final class CookieUtils {

    public static String readCookie(HttpServletRequest request, String key) {
        Cookie cookie = getCookie(request,key);
        return cookie == null ? null :cookie.getValue();
    }
    
    public static Cookie getCookie(HttpServletRequest request, String key) {
        if (StringUtils.isBlank(key)) {
            throw new RuntimeException("key must not be null!");
        }

        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie ck : cookies) {
                if (key.equals(ck.getName())) {
                    return ck;
                }
            }
        }

        return null;
    }

    public static Cookie createCookie(String name, String value, String domain, String path, int maxAge, boolean secure) {
        Cookie cookie = new <PERSON>ie(name, value);
        cookie.setDomain(domain);
        cookie.setPath(path);
        cookie.setMaxAge(maxAge);
        cookie.setHttpOnly(true);
        cookie.setSecure(secure);
        return cookie;
    }

    public static ResponseCookie createResponseCookie(String name, String value, String domain, String path, int maxAge, boolean secure) {
        return ResponseCookie.from(name, value)
                .domain(domain)
                .maxAge(maxAge)
                .path(path)
                .secure(secure)
                .httpOnly(true)
                .build();
    }
}
