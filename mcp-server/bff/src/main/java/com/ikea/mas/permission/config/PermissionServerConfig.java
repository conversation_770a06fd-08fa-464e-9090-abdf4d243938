/**
 * Project Name:bff
 * File Name:PermissionServerConfig.java
 * Package Name:com.ikea.mas.permission.config
 * Date:May 22, 202411:01:10 AM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.mas.permission.config;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import com.ikea.mas.permission.config.prop.PermissionServerProperties;

/**
 * ClassName:PermissionServerConfig <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     May 22, 2024 11:01:10 AM <br/>
 * <AUTHOR>	 
 */
@EnableConfigurationProperties(PermissionServerProperties.class)
@Configuration
public class PermissionServerConfig {

}