/**
 * Project Name:permission-server
 * File Name:UserInfoVo.java
 * Package Name:com.ikea.digital.permission.server.resource.web.response
 * Date:Nov 14, 20233:37:23 PM
 * Copyright (c) 2023, theodore.ni All Rights Reserved.
 */
package com.ikea.digital.permission.server.vo;

import java.util.List;
import java.util.Map;

import com.ikea.digital.permission.server.resource.domain.model.Role;
import com.ikea.digital.permission.server.resource.domain.model.User;

import lombok.Data;

/**
 * ClassName:UserInfoVo <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Nov 14, 2023 3:37:23 PM <br/>
 * <AUTHOR>	 
 */
@Data
public class UserInfoVo {
	
	String userId;
	
	String name;
	
	String email;
	
	String networkId;
	
	String avatar;
	
	List<String> storeIds;

	List<String> groups;

//	Map<String,List<String>> roleMap;
	
	Map<String,List<ResourceVo>> resourceMap;
	
//	Map<String,List<String>> roleStoreMap;
	
//	Map<String,Map<String,List<String>>> clientRoleStoreMap = new HashMap<>();
	
	Map<String,List<Role>> clientRoleModelMap;
	
	public UserInfoVo() {}
	
	public UserInfoVo(User user){
		this.userId = user.getUserId();
		this.name = user.getName();
		this.email = user.getEmail();
		this.groups = user.getGroups();
//		this.roleMap = user.getRoleMap();
		this.clientRoleModelMap = user.getClientRoleMap();
	}

}