/**
 * Project Name:permission-server
 * File Name:RefreshDataController.java
 * Package Name:com.ikea.digital.permission.server.resource.web.controller
 * Date:Sep 25, 202311:18:28 AM
 * Copyright (c) 2023, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.ikea.digital.permission.core.config.PmPreAuthorize;
import com.ikea.digital.permission.server.common.BaseResponse;
import com.ikea.digital.permission.server.common.RespCodeEnum;
import com.ikea.digital.permission.server.dto.ImportUserDto;
import com.ikea.digital.permission.server.service.ImportDataService;
import com.ikea.digital.permission.server.service.UserRoleService;
import com.ikea.digital.permission.server.validator.AuthValidator;
import com.ikea.digital.permission.server.validator.ClientValidator;
import com.ikea.mas.permission.common.Constants;

/**
 * ClassName:RefreshDataController <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Sep 25, 2023 11:18:28 AM <br/>
 * <AUTHOR>	 
 */
@RestController
@RequestMapping("/refresh")
public class RefreshDataController {
	
	@Autowired
//	ImportFacade importFacade;
	
	private ImportDataService importDataService;
	
	@Autowired
	ClientValidator clientValidator;
	
	@Autowired
	AuthValidator authValidator;
	
	@Autowired
	private UserRoleService userRoleService;
	
	@PmPreAuthorize("hasAnyRoleWithClient('assignManager','permission-service')")
	@GetMapping("/import/roles/{clientId}")
	public BaseResponse<Boolean> importRoleAndMenue(@PathVariable("clientId") String clientId) {
		boolean result = importDataService.importRolesAndMenues(clientId);
		return BaseResponse.success(result);
	}
	
	@PmPreAuthorize("hasAnyRoleWithClient('assignManager','permission-service')")
	@GetMapping("/import/users/{clientId}")
	public BaseResponse<Integer> importUser(@PathVariable("clientId") String clientId) {
		int failCount = importDataService.importUsers(clientId);
		return BaseResponse.success(failCount);
	}
	
	//@PmPreAuthorize("hasAnyRoleWithClient('assignManager','permission-service')")
	@PostMapping("/import/users")
	public BaseResponse<List<ImportUserDto>> importUser(@RequestHeader(Constants.CLIENT_ID) String clientId,
			@RequestBody List<ImportUserDto> users,
			@RequestHeader(Constants.CLIENT_SECRET)String secret) {
		
		if(CollectionUtils.isEmpty(users)) {
			return BaseResponse.error(RespCodeEnum.INVALID_CLIENT);
		}
		
		if(users.size() > 100) {
			return BaseResponse.error(RespCodeEnum.TOO_MUCH_DATA);
		}
		
		if(!clientValidator.validateAccess(clientId, secret)) {
			return BaseResponse.error(RespCodeEnum.INVALID_CLIENT);
		}
		
		List<ImportUserDto> failedUsers = importDataService.importUsers(clientId, users);
		return BaseResponse.success(failedUsers);
	}
	
	@PmPreAuthorize("hasAnyRoleWithClient('assignManager','permission-service')")
	@GetMapping("/remove/user/store")
	public BaseResponse<String> removeStoreIdBatch(@RequestParam("storeId") String storeId,
			@RequestParam(name = "clientId", required = false) String clientId) {
		userRoleService.batchRemoveStoreFromUserRoleTable(storeId, clientId);
		return BaseResponse.success();
	}
	
}