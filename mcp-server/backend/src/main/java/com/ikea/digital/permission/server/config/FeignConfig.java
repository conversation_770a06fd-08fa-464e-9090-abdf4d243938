/**
 * Project Name:backend
 * File Name:FeignConfig.java
 * Package Name:com.ikea.digital.permission.server.config
 * Date:Jul 24, 20243:46:25 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import feign.Logger;

/**
 * ClassName:FeignConfig <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jul 24, 2024 3:46:25 PM <br/>
 * <AUTHOR>	 
 */
@Configuration
public class FeignConfig {
	
	@Bean
    public Logger.Level feignLevel() {
        return Logger.Level.FULL;
    }

}

