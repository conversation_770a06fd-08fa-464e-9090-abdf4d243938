/**
// * Project Name:backend
// * File Name:RoleDeleteDto.java
// * Package Name:com.ikea.digital.permission.server.dto
// * Date:Dec 3, 20249:43:11 AM
// * Copyright (c) 2024, theodore.ni All Rights Reserved.
// *
// */
//package com.ikea.digital.permission.server.dto;
//
//import lombok.Data;
//
///**
// * ClassName:RoleDeleteDto <br/>
// * Function: TODO ADD FUNCTION. <br/>
// * Date:     Dec 3, 2024 9:43:11 AM <br/>
// * <AUTHOR>	 
// */
//@Data
//public class RoleDeleteDto {
//	
//	String clientId;
//	
//	String name;
//
//}