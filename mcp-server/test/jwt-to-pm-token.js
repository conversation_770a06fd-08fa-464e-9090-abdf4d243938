#!/usr/bin/env node

/**
 * 🎯 JWT to pm_user_token Converter
 *
 * Purpose:
 * 1. Start with mcp-mcp-odi client JWT token
 * 2. Try to get pm_user_token using minimal configuration
 * 3. Output the final pm_user_token that can be used as session cookie
 */

import { globalKeycloakTokenExchange } from '../dist/auth/keycloak-token-exchange.js';
import { globalPmTokenManager } from '../dist/auth/pm-token-manager.js';
import { decodeJWTUnsafe, extractSessionInfo } from '../dist/utils/jwt-utils.js';
import axios from 'axios';

/**
 * Configuration - Replace with your actual values
 */
const CONFIG = {
  // Your JWT token from mcp-mcp-odi client (replace with real token)
  JWT_TOKEN: process.env.TEST_JWT_TOKEN || 'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICIwYTQ4ZGU0Yy1jMDU4LTQ1YWUtYjkxZS03MGRkODRjMDQ4MDMifQ.eyJleHAiOjE3NTQyMTM1NTksImlhdCI6MTc1Mzk1NDM1OSwianRpIjoiZTBmMDQ2YmItZGJmNS00ZTljLTkxYWQtZDM2Njc2MmFiZjlmIiwiaXNzIjoiaHR0cHM6Ly9rZXljbG9hay5pbmdrYS1kdC5jbi9hdXRoL3JlYWxtcy9tYXN0ZXIiLCJzdWIiOiIzZDJlMjY1Yy1mODk1LTRlMzItYjNiZS01YTEzOWZhODU2YzEiLCJ0eXAiOiJTZXJpYWxpemVkLUlEIiwic2lkIjoiN2FiNDJiZDktYmFjMi00ODBkLWIzNzUtN2JhNThkZTIzMjM5Iiwic3RhdGVfY2hlY2tlciI6Ikp5bF9jV2RZLW51MnU5NG5VdzZ2TzZvMkVYN2twakwzeDBlVzBDeUdCRlUifQ.invalid_signature',
  
  // Your mcp-mcp-odi client credentials
  MCP_CLIENT_ID: process.env.MCP_CLIENT_ID || 'mcp-mcp-odi',
  MCP_CLIENT_SECRET: process.env.MCP_CLIENT_SECRET || '',
  
  // Keycloak configuration
  KEYCLOAK_BASE_URL: 'https://keycloak.ingka-dt.cn',
  KEYCLOAK_REALM: 'master',
  
  // Target
  TARGET_CLIENT_ID: 'permission-service'
};

/**
 * Step 1: Analyze the JWT token
 */
async function analyzeJWTToken() {
  console.log('🔍 ==================== JWT TOKEN ANALYSIS ====================');
  
  try {
    console.log('🔄 Analyzing your JWT token...');
    
    // Decode JWT
    const payload = decodeJWTUnsafe(CONFIG.JWT_TOKEN);
    const sessionInfo = extractSessionInfo(CONFIG.JWT_TOKEN);
    
    console.log('✅ JWT token decoded successfully');
    console.log('📊 Token information:');
    console.log(`   User ID (sub): ${sessionInfo.userId}`);
    console.log(`   Session ID (sid): ${sessionInfo.sessionId}`);
    console.log(`   Client ID (azp): ${sessionInfo.clientId || 'N/A'}`);
    console.log(`   Email: ${sessionInfo.email || 'N/A'}`);
    console.log(`   Issuer: ${sessionInfo.issuer}`);
    console.log(`   Expires: ${sessionInfo.expiresAt.toISOString()}`);
    console.log(`   Is Expired: ${new Date() > sessionInfo.expiresAt ? '❌ Yes' : '✅ No'}`);
    
    return sessionInfo;
  } catch (error) {
    console.error('❌ Failed to analyze JWT token:', error.message);
    console.log('💡 Make sure to provide a valid JWT token from your mcp-mcp-odi client');
    return null;
  }
}

/**
 * Step 2: Try Keycloak token exchange
 */
async function tryTokenExchange(sessionInfo) {
  console.log('\n🔄 ==================== KEYCLOAK TOKEN EXCHANGE ====================');
  
  if (!CONFIG.MCP_CLIENT_SECRET) {
    console.log('⚠️ MCP_CLIENT_SECRET not provided, skipping token exchange');
    console.log('💡 To test token exchange, set MCP_CLIENT_SECRET environment variable');
    return null;
  }
  
  try {
    console.log('🔄 Attempting Keycloak token exchange...');
    console.log(`   Source client: ${CONFIG.MCP_CLIENT_ID}`);
    console.log(`   Target client: ${CONFIG.TARGET_CLIENT_ID}`);
    console.log(`   User: ${sessionInfo.userId}`);
    
    // Manual token exchange call
    const tokenEndpoint = `${CONFIG.KEYCLOAK_BASE_URL}/auth/realms/${CONFIG.KEYCLOAK_REALM}/protocol/openid-connect/token`;
    
    const params = new URLSearchParams({
      grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',
      subject_token: CONFIG.JWT_TOKEN,
      subject_token_type: 'urn:ietf:params:oauth:token-type:access_token',
      client_id: CONFIG.MCP_CLIENT_ID,
      client_secret: CONFIG.MCP_CLIENT_SECRET,
      audience: CONFIG.TARGET_CLIENT_ID,
      requested_token_type: 'urn:ietf:params:oauth:token-type:access_token'
    });

    const response = await axios.post(tokenEndpoint, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      }
    });

    if (response.data.access_token) {
      console.log('✅ Token exchange successful!');
      console.log(`   New token expires in: ${response.data.expires_in}s`);
      console.log(`   Token type: ${response.data.token_type}`);
      console.log(`   New token preview: ${response.data.access_token.substring(0, 50)}...`);
      
      return response.data.access_token;
    } else {
      console.log('❌ No access token in response');
      return null;
    }
    
  } catch (error) {
    if (error.response) {
      console.error('❌ Token exchange failed:');
      console.error(`   Status: ${error.response.status} ${error.response.statusText}`);
      console.error(`   Error: ${JSON.stringify(error.response.data, null, 2)}`);
    } else {
      console.error('❌ Token exchange failed:', error.message);
    }
    return null;
  }
}

/**
 * Step 3: Create pm_user_token
 */
async function createPmUserToken(token, sessionInfo) {
  console.log('\n🔧 ==================== PM_USER_TOKEN CREATION ====================');
  
  try {
    console.log('🔄 Creating pm_user_token...');
    
    // Use the exchanged token if available, otherwise use original JWT
    const tokenToUse = token || CONFIG.JWT_TOKEN;
    console.log(`   Using ${token ? 'exchanged' : 'original'} token`);
    
    // Create pm_user_token
    const pmUserToken = globalPmTokenManager.createPmUserTokenFromJWT(tokenToUse);
    
    console.log('✅ pm_user_token created successfully!');
    console.log(`   Token length: ${pmUserToken.length} characters`);
    console.log(`   Token preview: ${pmUserToken.substring(0, 80)}...`);
    
    // Parse and display token info
    const tokenData = globalPmTokenManager.parsePmUserToken(pmUserToken);
    if (tokenData) {
      console.log('📊 pm_user_token details:');
      console.log(`   SSO UUID: ${tokenData.ssoUUID}`);
      console.log(`   User ID: ${tokenData.userId}`);
      console.log(`   Session Timeout: ${tokenData.clientSessionTimeOut}s`);
      console.log(`   Created: ${new Date(tokenData.loginTime).toISOString()}`);
      
      const expiration = new Date(tokenData.loginTime + (tokenData.clientSessionTimeOut * 1000));
      console.log(`   Expires: ${expiration.toISOString()}`);
    }
    
    return pmUserToken;
  } catch (error) {
    console.error('❌ Failed to create pm_user_token:', error.message);
    return null;
  }
}

/**
 * Step 4: Create session cookie
 */
async function createSessionCookie(pmUserToken) {
  console.log('\n🍪 ==================== SESSION COOKIE CREATION ====================');
  
  if (!pmUserToken) {
    console.log('❌ No pm_user_token available, cannot create session cookie');
    return null;
  }
  
  try {
    console.log('🔄 Creating session cookie...');
    
    const sessionCookie = globalPmTokenManager.createSessionCookieFromPmToken(pmUserToken, 'test_orders-portal');
    
    console.log('✅ Session cookie created successfully!');
    console.log(`   Cookie: ${sessionCookie}`);
    console.log('');
    console.log('🎯 **This is your final result!**');
    console.log('   You can use this cookie for API calls to your backend services.');
    console.log('');
    console.log('📋 Usage example:');
    console.log('   ```bash');
    console.log(`   curl -H "Cookie: ${sessionCookie}" \\`);
    console.log('        "https://fe-dev-i.ingka-dt.cn/order-web/api/orders"');
    console.log('   ```');
    
    return sessionCookie;
  } catch (error) {
    console.error('❌ Failed to create session cookie:', error.message);
    return null;
  }
}

/**
 * Step 5: Test the session cookie (optional)
 */
async function testSessionCookie(sessionCookie) {
  console.log('\n🧪 ==================== SESSION COOKIE TEST ====================');
  
  if (!sessionCookie) {
    console.log('❌ No session cookie to test');
    return;
  }
  
  console.log('🔄 Testing session cookie with a simple API call...');
  console.log('   Note: This may fail if the token is expired or invalid, but shows the usage pattern');
  
  try {
    // Test with a simple endpoint (this might fail with sample data, but shows the pattern)
    const testUrl = 'https://fe-dev-i.ingka-dt.cn/order-web/user/current';
    
    const response = await axios.get(testUrl, {
      headers: {
        'Cookie': sessionCookie,
        'User-Agent': 'Mozilla/5.0 (compatible; MCP-Server/1.0)'
      },
      timeout: 5000,
      validateStatus: () => true // Accept any status code
    });
    
    console.log(`   Response status: ${response.status} ${response.statusText}`);
    if (response.status === 200) {
      console.log('✅ Session cookie appears to be working!');
    } else if (response.status === 302) {
      console.log('🔄 Got redirect - cookie might be working (common for auth flows)');
    } else {
      console.log('⚠️ Unexpected response - cookie might need real user session');
    }
    
  } catch (error) {
    console.log('⚠️ Test request failed (expected with sample data):', error.message);
    console.log('   This doesn\'t mean the cookie is invalid - test with real JWT token');
  }
}

/**
 * Main function
 */
async function convertJWTToPmUserToken() {
  console.log('🎯 ==================== JWT TO PM_USER_TOKEN CONVERTER ====================');
  console.log('🎯 Purpose: Convert mcp-mcp-odi JWT token to pm_user_token session cookie\n');
  
  console.log('📋 Configuration:');
  console.log(`   MCP Client ID: ${CONFIG.MCP_CLIENT_ID}`);
  console.log(`   MCP Client Secret: ${CONFIG.MCP_CLIENT_SECRET ? 'Configured ✅' : 'Not configured ⚠️'}`);
  console.log(`   JWT Token: ${CONFIG.JWT_TOKEN.substring(0, 50)}...`);
  console.log(`   Target Client: ${CONFIG.TARGET_CLIENT_ID}`);
  console.log('');
  
  // Step 1: Analyze JWT token
  const sessionInfo = await analyzeJWTToken();
  if (!sessionInfo) {
    console.log('\n❌ Cannot proceed without valid JWT token');
    return;
  }
  
  // Step 2: Try token exchange (optional, requires client secret)
  const exchangedToken = await tryTokenExchange(sessionInfo);
  
  // Step 3: Create pm_user_token
  const pmUserToken = await createPmUserToken(exchangedToken, sessionInfo);
  if (!pmUserToken) {
    console.log('\n❌ Failed to create pm_user_token');
    return;
  }
  
  // Step 4: Create session cookie
  const sessionCookie = await createSessionCookie(pmUserToken);
  
  // Step 5: Test the cookie (optional)
  await testSessionCookie(sessionCookie);
  
  console.log('\n🏁 ==================== CONVERSION COMPLETE ====================');
  console.log('✅ JWT to pm_user_token conversion finished');
  console.log('');
  console.log('🎯 **Results Summary:**');
  console.log(`   Token Exchange: ${exchangedToken ? 'Success ✅' : 'Skipped/Failed ⚠️'}`);
  console.log(`   pm_user_token: ${pmUserToken ? 'Created ✅' : 'Failed ❌'}`);
  console.log(`   Session Cookie: ${sessionCookie ? 'Created ✅' : 'Failed ❌'}`);
  console.log('');
  
  if (sessionCookie) {
    console.log('🚀 **Your session cookie is ready to use!**');
    console.log(`   ${sessionCookie}`);
  }
  
  console.log('');
  console.log('💡 **To test with your real JWT token:**');
  console.log('   1. Get JWT token from your frontend: const token = keycloak.token');
  console.log('   2. Run: TEST_JWT_TOKEN="your-real-jwt" npm run test:jwt-to-pm-token');
  console.log('   3. Optional: MCP_CLIENT_SECRET="your-secret" for token exchange');
}

// Run the converter if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  convertJWTToPmUserToken().catch(error => {
    console.error('💥 Conversion failed:', error);
    process.exit(1);
  });
}

export { convertJWTToPmUserToken };
