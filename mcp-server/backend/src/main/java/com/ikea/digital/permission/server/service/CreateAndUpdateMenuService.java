/**
 * Project Name:backend
 * File Name:CreateAndUpdateMenuService.java
 * Package Name:com.ikea.digital.permission.server.resource.service
 * Date:Jun 12, 20244:48:24 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.ikea.digital.permission.server.dto.MenuCreateOrUpdateDto;
import com.ikea.digital.permission.server.exception.ResourceException;
import com.ikea.digital.permission.server.resource.domain.model.Menu;
import com.ikea.digital.permission.server.resource.domain.model.Role;

/**
 * ClassName:CreateAndUpdateMenuService <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jun 12, 2024 4:48:24 PM <br/>
 * <AUTHOR>	 
 */
@Service("createAndUpdateMenuService")
public class CreateAndUpdateMenuService extends UpdateMenuService {

	public void createMenu(MenuCreateOrUpdateDto dto) {
		// check menu if exist
		Optional<Menu> op = menuRepository.get(dto.getName(), dto.getClientId(), dto.getParentMenuId());
		if(op.isPresent()) {
			throw new ResourceException("menu:" + dto.getName() + " 已存在!");
		}
		
		Long menuId = menuRepository.createMenu(dto);
		dto.setId(menuId);
		
		// 添加到role
		if(CollectionUtils.isNotEmpty(dto.getRoles())) {
			//menu的父祖menuIds
			List<Long> parentMenuIds = menuRepository.getParentMenuIds(dto.getClientId(), dto.getId());
			// 添加menu到某个role下面，需要把当前menu的parents和subs都添加给role
			for (String role : dto.getRoles()) {
				roleRepository.addMenuIdToRole(role, dto.getClientId(), String.valueOf(dto.getId()), parentMenuIds);
			}
		}
		
		// 从role删除,当前新增的menu取消勾选role将可能导致parent的关联也被取消
		removeParentMenuFromRole(dto.getClientId(), dto.getId(), dto.getParentMenuId(), dto.getRoles());
	}
	
	private void removeParentMenuFromRole(String clientId, Long menuId, Long parentMenuId, List<String> roles) {
		if(Objects.isNull(parentMenuId)) {
			return;
		}
		
		// parent的其他subMenu
		List<Long> subMenuIds = menuRepository.getSubMenuIds(clientId, parentMenuId);
		subMenuIds.remove(menuId);
		
		// 如果当前parent没有其他subMenu,且当前parent关联了role,取消勾选role将可能导致当前parent的关联也被取消
		if(CollectionUtils.isNotEmpty(subMenuIds)) {
			return;
		}
		
		// 当前menu的parent关联的role
		List<Role> parentRoles = roleRepository.getRolesWithMenuId(clientId, parentMenuId);
		List<String> roleNames = parentRoles.stream().map(Role::getName).collect(Collectors.toList());
		List<String> calcedRole = roleNames.stream().filter(role -> !roles.contains(role)).collect(Collectors.toList());
		
		for (String role : calcedRole) {
			removeMenuFromRole(role, clientId, parentMenuId.toString());
		}
	}
}