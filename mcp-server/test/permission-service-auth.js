#!/usr/bin/env node

/**
 * 🔄 Permission Service Authentication
 *
 * Purpose:
 * 1. Implement the EXACT authentication flow from permission service
 * 2. Use the correct OAuth2 authorization code flow
 * 3. Get a valid pm_user_token that's stored in Redis backend
 */

import axios from 'axios';
import { getEnvironmentConfig } from '../dist/utils/env.js';
import { decodeJWTUnsafe } from '../dist/utils/jwt-utils.js';
import fs from 'fs';
import path from 'path';

/**
 * Load configuration
 */
function loadConfiguration() {
  const env = getEnvironmentConfig();
  
  let tokenData = null;
  try {
    const tokenFile = path.join(process.cwd(), 'mcp-mpc-odi.token.json');
    const tokenContent = fs.readFileSync(tokenFile, 'utf8');
    tokenData = JSON.parse(tokenContent);
  } catch (error) {
    console.error('⚠️ Could not load token file:', error.message);
  }

  return {
    JWT_TOKEN: tokenData?.access_token || '',
    TOKEN_DATA: tokenData,
    PERMISSION_SERVICE_BASE: 'https://api-dev-mpp-fe.ingka-dt.cn',
    ORDERS_API_BASE: 'https://fe-dev-i.ingka-dt.cn/order-web',
    CLIENT_ID: 'permission-service', // From browser capture
    REDIRECT_URL: 'https://fe-dev-i.ingka-dt.cn/order-web/user/current',
    REFERER: 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
  };
}

const CONFIG = loadConfiguration();

/**
 * Step 1: Get login URL from permission service
 * This matches: GET /permission-service/user/auth/getLoginUrl
 */
async function getLoginUrl() {
  console.log('🔄 ==================== STEP 1: GET LOGIN URL ====================');
  
  try {
    console.log('🔄 Getting login URL from permission service...');
    
    const loginUrlEndpoint = `${CONFIG.PERMISSION_SERVICE_BASE}/permission-service/user/auth/getLoginUrl`;
    
    console.log(`   Endpoint: ${loginUrlEndpoint}`);
    console.log(`   Client ID: ${CONFIG.CLIENT_ID}`);
    console.log(`   Redirect URL: ${CONFIG.REDIRECT_URL}`);
    console.log(`   Referer: ${CONFIG.REFERER}`);
    
    const response = await axios.get(loginUrlEndpoint, {
      headers: {
        'clientId': CONFIG.CLIENT_ID,
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
      },
      params: {
        redirectUrl: CONFIG.REDIRECT_URL,
        referer: CONFIG.REFERER
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    console.log(`   Response data: ${JSON.stringify(response.data, null, 2)}`);
    
    if (response.data?.success && response.data?.data) {
      const loginUrl = response.data.data;
      console.log('✅ Got login URL from permission service');
      console.log(`   Login URL: ${loginUrl}`);
      
      // Extract state from login URL
      const urlParams = new URLSearchParams(loginUrl.split('?')[1]);
      const state = urlParams.get('state');
      
      return {
        loginUrl,
        state,
        success: true
      };
    } else {
      console.log('❌ Failed to get login URL');
      return { success: false };
    }
    
  } catch (error) {
    console.error('❌ Failed to get login URL:', error.message);
    return { success: false };
  }
}

/**
 * Step 2: Start OAuth2 flow
 * This matches the /toLogin endpoint logic
 */
async function startOAuth2Flow(loginInfo) {
  console.log('\n🔄 ==================== STEP 2: START OAUTH2 FLOW ====================');
  
  if (!loginInfo.success) {
    console.log('❌ Cannot start OAuth2 flow without login URL');
    return { success: false };
  }
  
  try {
    console.log('🔄 Starting OAuth2 flow...');
    console.log(`   Login URL: ${loginInfo.loginUrl}`);
    console.log(`   State: ${loginInfo.state}`);
    
    // This would normally redirect the user to Keycloak for authentication
    // In a real implementation, you would:
    // 1. Redirect user to loginUrl
    // 2. User authenticates with Keycloak
    // 3. Keycloak redirects back with authorization code
    // 4. Call loginComplete with the code
    
    console.log('📋 **Manual Steps Required:**');
    console.log('   1. Open this URL in browser (while logged into Keycloak):');
    console.log(`      ${loginInfo.loginUrl}`);
    console.log('   2. After authentication, you\'ll be redirected to:');
    console.log(`      ${CONFIG.REDIRECT_URL}?pm_user_token=...&biz_origin_url=...&code=...`);
    console.log('   3. Extract the pm_user_token from the URL');
    console.log('   4. Use that token for API calls');
    
    return {
      success: true,
      loginUrl: loginInfo.loginUrl,
      state: loginInfo.state,
      nextSteps: 'Manual browser authentication required'
    };
    
  } catch (error) {
    console.error('❌ OAuth2 flow failed:', error.message);
    return { success: false };
  }
}

/**
 * Step 3: Simulate loginComplete (for understanding)
 * This shows what happens in the /loginComplete endpoint
 */
async function explainLoginComplete() {
  console.log('\n🔄 ==================== STEP 3: LOGIN COMPLETE EXPLANATION ====================');
  
  console.log('📋 What happens in /loginComplete endpoint:');
  console.log('');
  console.log('1. **Exchange authorization code for tokens:**');
  console.log('   ```java');
  console.log('   AccessTokenResponse tokenRes = keycloakService.getKeycloakAccessToken(code, redirectUrl, sessionId);');
  console.log('   DecodedJWT authJwt = JWT.decode(tokenRes.getIdToken());');
  console.log('   String userId = authJwt.getClaim("sub").asString();');
  console.log('   ```');
  console.log('');
  console.log('2. **Create SSO session in Redis:**');
  console.log('   ```java');
  console.log('   String ssoUUID = UUID.randomUUID().toString();');
  console.log('   Map<String,String> ssoValueMap = new HashMap<>();');
  console.log('   ssoValueMap.put(clientId, String.valueOf(currMill + clientIdleTime * 1000));');
  console.log('   ssoValueMap.put("kc_user_id", userId);');
  console.log('   redisHelper.hmset(ssoUUID, ssoValueMap, ssoLoginTimeOut + 600);');
  console.log('   ```');
  console.log('');
  console.log('3. **Create pm_user_token:**');
  console.log('   ```java');
  console.log('   PmToken pmToken = new PmToken(ssoUUID, userId, clientSessionTimeOut, currMill);');
  console.log('   String userTag = pmToken.token(); // Base64 encoded');
  console.log('   ```');
  console.log('');
  console.log('4. **Store pm_user_token in Redis:**');
  console.log('   ```java');
  console.log('   redisHelper.set(userTag, userId, clientSessionTimeOut);');
  console.log('   ```');
  console.log('');
  console.log('5. **Return redirect URL with pm_user_token:**');
  console.log('   ```java');
  console.log('   String url = generateRedirectUrl(stateMap, clientId, userTag);');
  console.log('   // Result: https://fe-dev-i.ingka-dt.cn/order-web/user/current?pm_user_token=...&biz_origin_url=...&code=...');
  console.log('   ```');
  console.log('');
  console.log('🎯 **Key Insight:**');
  console.log('   pm_user_token is BOTH:');
  console.log('   • A formatted token (Base64 of <EMAIL>)');
  console.log('   • A Redis key that stores the userId');
  console.log('   • This is why our generated tokens don\'t work - they\'re not in Redis!');
}

/**
 * Step 4: Test with browser-extracted token
 */
async function testWithBrowserToken() {
  console.log('\n🔄 ==================== STEP 4: TEST WITH BROWSER TOKEN ====================');
  
  console.log('🔄 Testing with browser-extracted pm_user_token...');
  console.log('   This token should be extracted from the browser after completing OAuth2 flow');
  
  // You would replace this with the actual token from browser
  const browserToken = 'REPLACE_WITH_ACTUAL_TOKEN_FROM_BROWSER';
  
  if (browserToken === 'REPLACE_WITH_ACTUAL_TOKEN_FROM_BROWSER') {
    console.log('⚠️ No browser token provided');
    console.log('   To test:');
    console.log('   1. Complete the OAuth2 flow in browser');
    console.log('   2. Extract pm_user_token from the redirect URL');
    console.log('   3. Replace REPLACE_WITH_ACTUAL_TOKEN_FROM_BROWSER with the real token');
    console.log('   4. Run this test again');
    return { success: false };
  }
  
  try {
    const testUrl = `${CONFIG.ORDERS_API_BASE}/orders/search`;
    const requestData = {
      "storeIds": [],
      "baseSize": 10,
      "page": 1,
      "size": 10,
      "timestamp": Date.now()
    };
    
    const response = await axios.post(testUrl, requestData, {
      headers: {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'Cookie': `test_orders-portal=${browserToken}`,
        'dnt': '1',
        'origin': 'https://admin.ingka-dt.cn',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://admin.ingka-dt.cn/',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'x-custom-referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    console.log(`   Response data: ${JSON.stringify(response.data, null, 2)}`);
    
    if (response.status === 200 && response.data && response.data.code !== 401) {
      console.log('✅ Browser token works!');
      return { success: true };
    } else {
      console.log('❌ Browser token failed or expired');
      return { success: false };
    }
    
  } catch (error) {
    console.error('❌ Browser token test failed:', error.message);
    return { success: false };
  }
}

/**
 * Main function
 */
async function runPermissionServiceAuth() {
  console.log('🔄 ==================== PERMISSION SERVICE AUTHENTICATION ====================');
  console.log('🎯 Purpose: Understand and implement the correct authentication flow\n');
  
  console.log('📋 Configuration:');
  console.log(`   Token File: ${CONFIG.TOKEN_DATA ? 'Loaded ✅' : 'Missing ❌'}`);
  console.log(`   JWT Token: ${CONFIG.JWT_TOKEN ? CONFIG.JWT_TOKEN.substring(0, 50) + '...' : 'Not available ❌'}`);
  console.log(`   Permission Service: ${CONFIG.PERMISSION_SERVICE_BASE}`);
  console.log(`   Client ID: ${CONFIG.CLIENT_ID}`);
  console.log(`   Redirect URL: ${CONFIG.REDIRECT_URL}`);
  console.log('');
  
  // Step 1: Get login URL
  const loginInfo = await getLoginUrl();
  
  // Step 2: Start OAuth2 flow
  const oauthInfo = await startOAuth2Flow(loginInfo);
  
  // Step 3: Explain what happens in loginComplete
  await explainLoginComplete();
  
  // Step 4: Test with browser token
  const browserTest = await testWithBrowserToken();
  
  console.log('\n🏁 ==================== AUTHENTICATION FLOW COMPLETE ====================');
  console.log('✅ Permission service authentication analysis finished');
  console.log('');
  console.log('🎯 **Summary:**');
  console.log(`   Login URL: ${loginInfo.success ? 'Retrieved ✅' : 'Failed ❌'}`);
  console.log(`   OAuth2 Flow: ${oauthInfo.success ? 'Explained ✅' : 'Failed ❌'}`);
  console.log(`   Browser Test: ${browserTest.success ? 'Success ✅' : 'Needs token ⚠️'}`);
  console.log('');
  console.log('🚀 **Next Steps:**');
  if (loginInfo.success) {
    console.log('   1. Open the login URL in your browser');
    console.log('   2. Complete the OAuth2 authentication');
    console.log('   3. Extract pm_user_token from the redirect URL');
    console.log('   4. Use that token in your MCP server');
    console.log('');
    console.log('💡 **For MCP Server Integration:**');
    console.log('   • Implement OAuth2 flow in your frontend');
    console.log('   • Pass the pm_user_token to MCP server');
    console.log('   • MCP server uses the token for API calls');
    console.log('   • No need to generate tokens - use the real ones from OAuth2!');
  } else {
    console.log('   ❌ Could not retrieve login URL - check configuration');
  }
}

// Run the authentication flow if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runPermissionServiceAuth().catch(error => {
    console.error('💥 Permission service authentication failed:', error);
    process.exit(1);
  });
}

export { runPermissionServiceAuth };
