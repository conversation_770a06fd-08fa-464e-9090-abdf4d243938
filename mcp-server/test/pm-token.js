#!/usr/bin/env node

/**
 * 🧪 PmToken Test Script
 *
 * Purpose:
 * 1. Test PmToken creation from JWT tokens
 * 2. Verify token parsing and validation
 * 3. Test session cookie creation
 * 4. Validate token expiration logic
 */

import { globalPmTokenManager } from '../dist/auth/pm-token-manager.js';
import jwt from 'jsonwebtoken';

/**
 * Sample JWT tokens for testing
 */
const SAMPLE_JWT_TOKENS = {
  // Valid JWT structure (sample - replace with real token for testing)
  valid: 'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICIwYTQ4ZGU0Yy1jMDU4LTQ1YWUtYjkxZS03MGRkODRjMDQ4MDMifQ.eyJleHAiOjE3NTQyMTM1NTksImlhdCI6MTc1Mzk1NDM1OSwianRpIjoiZTBmMDQ2YmItZGJmNS00ZTljLTkxYWQtZDM2Njc2MmFiZjlmIiwiaXNzIjoiaHR0cHM6Ly9rZXljbG9hay5pbmdrYS1kdC5jbi9hdXRoL3JlYWxtcy9tYXN0ZXIiLCJzdWIiOiIzZDJlMjY1Yy1mODk1LTRlMzItYjNiZS01YTEzOWZhODU2YzEiLCJ0eXAiOiJTZXJpYWxpemVkLUlEIiwic2lkIjoiN2FiNDJiZDktYmFjMi00ODBkLWIzNzUtN2JhNThkZTIzMjM5Iiwic3RhdGVfY2hlY2tlciI6Ikp5bF9jV2RZLW51MnU5NG5VdzZ2TzZvMkVYN2twakwzeDBlVzBDeUdCRlUifQ.invalid_signature',
  invalid: 'invalid.jwt.token'
};

/**
 * Test JWT token validation
 */
async function testJWTValidation() {
  console.log('🔍 ==================== JWT VALIDATION TEST ====================');
  
  try {
    console.log('🔄 Testing JWT validation...');
    
    // Test valid JWT
    const canConvertValid = globalPmTokenManager.canConvertJWT(SAMPLE_JWT_TOKENS.valid);
    console.log(`   Valid JWT can convert: ${canConvertValid ? '✅' : '❌'}`);
    
    // Test invalid JWT
    const canConvertInvalid = globalPmTokenManager.canConvertJWT(SAMPLE_JWT_TOKENS.invalid);
    console.log(`   Invalid JWT can convert: ${canConvertInvalid ? '❌' : '✅'}`);
    
    if (canConvertValid) {
      // Decode and show JWT content
      const decoded = jwt.decode(SAMPLE_JWT_TOKENS.valid);
      if (decoded && typeof decoded === 'object') {
        console.log('   JWT payload:');
        console.log(`     User ID (sub): ${decoded.sub}`);
        console.log(`     Session ID (sid): ${decoded.sid}`);
        console.log(`     Issuer: ${decoded.iss}`);
        console.log(`     Expires: ${decoded.exp ? new Date(decoded.exp * 1000).toISOString() : 'N/A'}`);
      }
    }
    
    return canConvertValid;
  } catch (error) {
    console.error('❌ JWT validation test failed:', error.message);
    return false;
  }
}

/**
 * Test PmToken creation
 */
async function testPmTokenCreation() {
  console.log('\n🔧 ==================== PM TOKEN CREATION TEST ====================');
  
  try {
    console.log('🔄 Testing pm_user_token creation...');
    
    const startTime = Date.now();
    const pmUserToken = globalPmTokenManager.createPmUserTokenFromJWT(SAMPLE_JWT_TOKENS.valid);
    const duration = Date.now() - startTime;
    
    console.log(`✅ pm_user_token created successfully! (${duration}ms)`);
    console.log(`   Token length: ${pmUserToken.length} characters`);
    console.log(`   Token preview: ${pmUserToken.substring(0, 50)}...`);
    
    // Test token parsing
    const tokenData = globalPmTokenManager.parsePmUserToken(pmUserToken);
    if (tokenData) {
      console.log('📊 Token data:');
      console.log(`   SSO UUID: ${tokenData.ssoUUID}`);
      console.log(`   User ID: ${tokenData.userId}`);
      console.log(`   Session Timeout: ${tokenData.clientSessionTimeOut}s`);
      console.log(`   Login Time: ${new Date(tokenData.loginTime).toISOString()}`);
    }
    
    return pmUserToken;
  } catch (error) {
    console.error('❌ pm_user_token creation failed:', error.message);
    return null;
  }
}

/**
 * Test session cookie creation
 */
async function testSessionCookieCreation(pmUserToken) {
  console.log('\n🍪 ==================== SESSION COOKIE CREATION TEST ====================');
  
  if (!pmUserToken) {
    console.log('❌ Skipping cookie test - no pm_user_token available');
    return null;
  }
  
  try {
    console.log('🔄 Testing session cookie creation...');
    
    const startTime = Date.now();
    const sessionCookie = globalPmTokenManager.createSessionCookieFromPmToken(pmUserToken);
    const duration = Date.now() - startTime;
    
    console.log(`✅ Session cookie created successfully! (${duration}ms)`);
    console.log(`   Cookie: ${sessionCookie.substring(0, 80)}...`);
    
    // Test with custom cookie name
    const customCookie = globalPmTokenManager.createSessionCookieFromPmToken(pmUserToken, 'custom_cookie');
    console.log(`   Custom cookie: ${customCookie.substring(0, 50)}...`);
    
    return sessionCookie;
  } catch (error) {
    console.error('❌ Session cookie creation failed:', error.message);
    return null;
  }
}

/**
 * Test complete JWT to cookie conversion
 */
async function testCompleteConversion() {
  console.log('\n🔄 ==================== COMPLETE CONVERSION TEST ====================');
  
  try {
    console.log('🔄 Testing complete JWT to session cookie conversion...');
    
    const startTime = Date.now();
    const sessionCookie = await globalPmTokenManager.convertJWTToSessionCookie(SAMPLE_JWT_TOKENS.valid);
    const duration = Date.now() - startTime;
    
    console.log(`✅ Complete conversion successful! (${duration}ms)`);
    console.log(`   Final cookie: ${sessionCookie.substring(0, 80)}...`);
    
    return sessionCookie;
  } catch (error) {
    console.error('❌ Complete conversion failed:', error.message);
    return null;
  }
}

/**
 * Test token expiration logic
 */
async function testTokenExpiration(pmUserToken) {
  console.log('\n⏰ ==================== TOKEN EXPIRATION TEST ====================');
  
  if (!pmUserToken) {
    console.log('❌ Skipping expiration test - no pm_user_token available');
    return;
  }
  
  try {
    console.log('🔄 Testing token expiration logic...');
    
    // Get token info
    const tokenInfo = globalPmTokenManager.getTokenInfo(pmUserToken);
    if (tokenInfo) {
      console.log('📊 Token expiration info:');
      console.log(`   Created: ${new Date(tokenInfo.loginTime).toISOString()}`);
      console.log(`   Expires: ${tokenInfo.expiration}`);
      console.log(`   Is Expired: ${tokenInfo.isExpired ? '❌' : '✅'}`);
      console.log(`   Time to Expiry: ${Math.round(tokenInfo.timeToExpiry / 1000)}s`);
    }
    
    // Test expiration check
    const isExpired = globalPmTokenManager.isTokenExpired(pmUserToken);
    console.log(`   Expiration check: ${isExpired ? 'Expired ❌' : 'Valid ✅'}`);
    
  } catch (error) {
    console.error('❌ Token expiration test failed:', error.message);
  }
}

/**
 * Test error handling
 */
async function testErrorHandling() {
  console.log('\n❌ ==================== ERROR HANDLING TEST ====================');
  
  const testCases = [
    { name: 'Invalid JWT', token: SAMPLE_JWT_TOKENS.invalid },
    { name: 'Empty string', token: '' },
    { name: 'Malformed Base64', token: 'not-base64-token' },
    { name: 'Invalid pm_user_token format', token: Buffer.from('invalid-format').toString('base64') }
  ];
  
  for (const testCase of testCases) {
    try {
      console.log(`🔄 Testing: ${testCase.name}`);
      
      if (testCase.name.includes('JWT')) {
        const canConvert = globalPmTokenManager.canConvertJWT(testCase.token);
        console.log(`   Can convert: ${canConvert ? '⚠️ Unexpected' : '✅ Correctly rejected'}`);
      } else {
        const tokenData = globalPmTokenManager.parsePmUserToken(testCase.token);
        console.log(`   Parse result: ${tokenData ? '⚠️ Unexpected success' : '✅ Correctly failed'}`);
      }
    } catch (error) {
      console.log(`   ✅ Correctly threw error: ${error.message}`);
    }
  }
}

/**
 * Demonstrate real usage
 */
async function demonstrateRealUsage() {
  console.log('\n💡 ==================== REAL USAGE DEMONSTRATION ====================');
  
  console.log('📝 How to use PmToken manager in your application:');
  console.log('');
  console.log('1. **Convert JWT to session cookie:**');
  console.log('   ```javascript');
  console.log('   import { globalPmTokenManager } from "./auth/pm-token-manager.js";');
  console.log('   ');
  console.log('   // Your frontend JWT token');
  console.log('   const jwtToken = keycloak.token;');
  console.log('   ');
  console.log('   // Convert to session cookie');
  console.log('   const sessionCookie = await globalPmTokenManager.convertJWTToSessionCookie(jwtToken);');
  console.log('   ');
  console.log('   // Use in API calls');
  console.log('   const response = await fetch("/api/orders", {');
  console.log('     headers: { "Cookie": sessionCookie }');
  console.log('   });');
  console.log('   ```');
  console.log('');
  console.log('2. **Manual token creation:**');
  console.log('   ```javascript');
  console.log('   // Create pm_user_token');
  console.log('   const pmUserToken = globalPmTokenManager.createPmUserTokenFromJWT(jwtToken);');
  console.log('   ');
  console.log('   // Create session cookie');
  console.log('   const cookie = globalPmTokenManager.createSessionCookieFromPmToken(pmUserToken);');
  console.log('   ```');
  console.log('');
  console.log('3. **Token validation:**');
  console.log('   ```javascript');
  console.log('   // Check if JWT can be converted');
  console.log('   const canConvert = globalPmTokenManager.canConvertJWT(jwtToken);');
  console.log('   ');
  console.log('   // Check token expiration');
  console.log('   const isExpired = globalPmTokenManager.isTokenExpired(pmUserToken);');
  console.log('   ```');
  console.log('');
  console.log('4. **Integration with service adapter:**');
  console.log('   The JWT cookie manager automatically uses PmToken creation as the first method.');
  console.log('   This provides the most reliable JWT-to-cookie conversion.');
}

/**
 * Main test function
 */
async function runPmTokenTest() {
  console.log('🧪 ==================== PM TOKEN TEST SUITE ====================');
  console.log('🎯 Purpose: Test PmToken creation and JWT-to-cookie conversion\n');

  // Test 1: JWT validation
  const canConvert = await testJWTValidation();

  // Test 2: PmToken creation
  const pmUserToken = await testPmTokenCreation();

  // Test 3: Session cookie creation
  const sessionCookie = await testSessionCookieCreation(pmUserToken);

  // Test 4: Complete conversion
  const completeCookie = await testCompleteConversion();

  // Test 5: Token expiration
  await testTokenExpiration(pmUserToken);

  // Test 6: Error handling
  await testErrorHandling();

  // Test 7: Usage demonstration
  await demonstrateRealUsage();

  console.log('\n🏁 ==================== TEST SUITE COMPLETE ====================');
  console.log('✅ PmToken test suite finished');
  console.log('💡 Key findings:');
  console.log(`   - JWT validation: ${canConvert ? 'Working' : 'Failed'}`);
  console.log(`   - Token creation: ${pmUserToken ? 'Working' : 'Failed'}`);
  console.log(`   - Cookie creation: ${sessionCookie ? 'Working' : 'Failed'}`);
  console.log(`   - Complete flow: ${completeCookie ? 'Working' : 'Failed'}`);
  console.log('');
  console.log('🚀 This approach eliminates the need for:');
  console.log('   ❌ Manual cookie extraction from browser');
  console.log('   ❌ Complex OAuth2 flow reconstruction');
  console.log('   ❌ HTTP session management');
  console.log('   ✅ Direct JWT-to-cookie conversion!');
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runPmTokenTest().catch(error => {
    console.error('💥 Test suite crashed:', error);
    process.exit(1);
  });
}

export { runPmTokenTest };
