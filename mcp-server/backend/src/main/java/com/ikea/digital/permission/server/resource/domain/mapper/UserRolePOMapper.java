package com.ikea.digital.permission.server.resource.domain.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.ikea.digital.permission.server.resource.domain.model.UserRole;

public interface UserRolePOMapper {
	
    int deleteByPrimaryKey(Long id);

    long insert(UserRole row);

    long insertSelective(UserRole row);
    
    int insertList(@Param("users") List<UserRole> users);

    UserRole selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserRole row);

    int updateByPrimaryKey(UserRole row);
    
    List<UserRole> selectSelective(UserRole row);
    
    /**
     * 慎重使用，必须校验参数，确保至少有一个参数有值
     */
    int deleteSelective(UserRole row);
    
    int deleteSelectiveWithRoles(@Param("clientId")String clientId, @Param("userId")String userId, @Param("roles") List<String> roles);

    List<UserRole> selectByStoreId(@Param("storeId")String storeId, @Param("clientId")String clientId);
    
    int updateStoreIdsBatch(@Param("list")List<UserRole> list);
    
    List<UserRole> selectByRoleAndStore(@Param("storeId")String storeId, 
    		@Param("clientId")String clientId,
    		@Param("roleName")String roleName,
    		@Param("start")Integer start,
    		@Param("size")Integer size);

}