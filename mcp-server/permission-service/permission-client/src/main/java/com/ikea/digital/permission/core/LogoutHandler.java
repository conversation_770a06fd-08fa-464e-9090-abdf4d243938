package com.ikea.digital.permission.core;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

public class LogoutHandler {
	
	PermissionSender permissionSender;
	
    LogoutHandler(PermissionSender permissionSender){
    	this.permissionSender = permissionSender;
    }
    
    public void logout(HttpServletRequest request, HttpServletResponse response)  {
    	permissionSender.logout(request);
    }
}


