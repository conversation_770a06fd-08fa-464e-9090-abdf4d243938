/**
 * Project Name:permission-server
 * File Name:BatchUserAssignRoleDto.java
 * Package Name:com.ikea.digital.permission.server.dto
 * Date:Mar 19, 20245:36:41 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.dto;

/**
 * ClassName:BatchUserAssignRoleDto <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Mar 19, 2024 5:36:41 PM <br/>
 * <AUTHOR>	 
 */
public class BatchUserAssignRoleDto extends UserAssignRoleDto {
	
	@Override
	public void validate() {
		
	}

}

