name: Publish Sdk # The name of your workflow
on: # The name of the GitHub event that triggers the workflow
  # When using the push or pull_request events, you can configure a workflow to run on specific branches or tags
  workflow_dispatch:
jobs:
  publish-sdk:
    # Name of the job which will be shown on the web console
    name: Build and Publish Sdk
    # Use a list of labels to determine which runner the workflow will run on
    runs-on:
      - self-hosted
      - ubuntu-lts
      - ali

    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE on runner, so your workflow can access it
      - uses: ikea-github-actions/checkout@v2

      - name: download jdk
        run: |
          download_url="https://ikea-tools.oss-cn-shanghai.aliyuncs.com/jdk/OpenJDK17U-jdk_x64_linux_hotspot_17.0.10_7.tar.gz"
          wget -O $RUNNER_TEMP/java_package.tar.gz $download_url

      - name: Setup Java
        uses: ikea-github-actions/setup-java@v3
        with:
          distribution: 'jdkfile'
          jdkFile: ${{ runner.temp }}/java_package.tar.gz
          java-version: '17.0.0'
          architecture: x64

      - name: publish sdk to artifactory
        run: |
          ./gradlew -p permission-client/ clean publish -PRT_CDH_USERNAME=${{ secrets.MAS_ARTIFACTORY_USERNAME }} -PRT_CDH_TOKEN=${{ secrets.MAS_ARTIFACTORY_TOKEN }}
