package com.ikea.digital.permission.exception;

import com.ikea.digital.permission.common.BizRespCodeEnum;

public class PermissionAccessException extends RuntimeException {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Integer code;

	public PermissionAccessException() {
		super();
	}

	public PermissionAccessException(String message) {
		super(message);
	}

	public PermissionAccessException(int code, String message) {
		super(message);

		this.code = code;
	}

	public PermissionAccessException(BizRespCodeEnum respCodeEnum) {
		super(respCodeEnum.getMsg());

		this.code = respCodeEnum.getCode();
	}

	public PermissionAccessException(Throwable cause) {
		super(cause);
	}

	public PermissionAccessException(String message, Throwable cause) {
		super(message, cause);
	}

	public Integer getCode() {
		return code;
	}
}
