package com.ikea.digital.permission.server.service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.ikea.digital.permission.server.repository.ClientRepository;
import com.ikea.digital.permission.server.resource.domain.model.Client;
import com.ikea.digital.permission.server.vo.ClientVo;

@Service
public class ClientService {

	@Autowired
	private ClientRepository clientRepository;
	
	public List<ClientVo> listClient() {
		List<Client> clientList = clientRepository.fetchClientList();
		if(CollectionUtils.isEmpty(clientList)) {
			return Collections.emptyList();
		}
		
		
		return clientList.stream().map(client -> {
			ClientVo vo = new ClientVo();
			vo.setId(client.getId());
			vo.setName(client.getName());
			return vo;
		}).collect(Collectors.toList());
		
	}

}
