package com.ikea.digital.permission.server.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ikea.digital.permission.server.common.BaseResponse;
import com.ikea.digital.permission.server.feign.model.StoreDO;
import com.ikea.digital.permission.server.service.StoreService;

@RestController
@RequestMapping("/rs/store")
public class StoreController {
	
	@Autowired
	private StoreService storeService;
	
	@GetMapping("/list")
	public BaseResponse<?> storeList(){
		List<StoreDO> result = storeService.queryAllStores();
		return BaseResponse.success(result);
	}
	
}
