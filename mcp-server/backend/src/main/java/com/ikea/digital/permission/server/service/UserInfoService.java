/**
 * Project Name:permission-server
 * File Name:UserInfoService.java
 * Package Name:com.ikea.digital.permission.server.resource.service
 * Date:Feb 26, 20244:09:00 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.service;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ikea.digital.permission.server.common.BkdConstants;
import com.ikea.digital.permission.server.common.util.ApplicationHolder;
import com.ikea.digital.permission.server.common.util.JacksonUtils;
import com.ikea.digital.permission.server.resource.domain.model.UserRole;
import com.ikea.digital.permission.server.vo.UserDetailVo;

/**
 * ClassName:UserInfoService <br/>
 * Function: 除了admin_portal前端和permission-service之外的client通过sdk获取用户权限信息使用此接口<br/>
 * Date:     Feb 26, 2024 4:09:00 PM <br/>
 * <AUTHOR>	 
 */
@Service("userInfoService")
public class UserInfoService extends AbstractUserInfoService {
	
	@Autowired
	protected OrgService orgService;
	
	@Override
	protected void fillUserDetailInfo(UserDetailVo user, Map<String, List<String>> attrMap, String clientId) {
		super.fillUserDetailInfo(user, attrMap, clientId);
		fillUserOrgs(user, clientId);
	}

	@SuppressWarnings("unchecked")
	@Override
	protected void fillUserRolesAndStores(UserDetailVo user, Map<String, List<UserRole>> userRoleStoreMap) {
		String currClientId = ApplicationHolder.get();
		user.setClientRoleModelMap(Collections.emptyMap());
		List<UserRole> userClientRoles = userRoleStoreMap.get(currClientId);
		if(CollectionUtils.isNotEmpty(userClientRoles)) {
			Map<String,List<String>> roleStoreMap = userClientRoles.stream().collect(Collectors.toMap(UserRole::getRoleName, po -> { return JacksonUtils.readValue(po.getStoreIds(), List.class);}, (e1, e2) -> e1));
			user.setRoleStoreMap(roleStoreMap);
			this.fillMenu(user, currClientId);
		}
		
		user.setRoleMap(Collections.singletonMap(currClientId,user.getRoleMap().get(currClientId)));
		
		Set<String> storeIds = new HashSet<>();
		if(MapUtils.isNotEmpty(user.getRoleStoreMap())) {
			storeIds.addAll(user.getRoleStoreMap().values().stream().flatMap(Collection::stream).collect(Collectors.toSet()));
		}
		
		user.setStoreIds(storeIds.stream().collect(Collectors.toList()));
		
	}

	@Override
	public boolean supportClient(String clientId) {
		return !BkdConstants.ADMIN_PORTAL.equals(clientId) && !pmClientConfig.getClientId().equals(clientId);
	}
	
	private void fillUserOrgs(UserDetailVo user, String clientId) {
		List<Long> orgIds = orgService.getOrgIdList(user.getEmail(), clientId);
		user.setOrgIds(orgIds.stream().distinct().collect(Collectors.toList()));
	}

}