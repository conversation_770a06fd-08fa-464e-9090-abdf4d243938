package com.ikea.digital.permission.server.controller;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ikea.digital.permission.core.config.PermissionClientConfig;
import com.ikea.digital.permission.core.config.PmPreAuthorize;
import com.ikea.digital.permission.server.common.BaseResponse;
import com.ikea.digital.permission.server.common.RespCodeEnum;
import com.ikea.digital.permission.server.common.util.CookieUtils;
import com.ikea.digital.permission.server.dto.UpdateValidGroup;
import com.ikea.digital.permission.server.dto.UserCreateOrUpdateDto;
import com.ikea.digital.permission.server.dto.UserGrantGroupDto;
import com.ikea.digital.permission.server.dto.UserGrantRoleDto;
import com.ikea.digital.permission.server.dto.UserSearchDto;
import com.ikea.digital.permission.server.exception.ResourceException;
import com.ikea.digital.permission.server.resource.domain.model.User;
import com.ikea.digital.permission.server.service.CompositeUserInfoService;
import com.ikea.digital.permission.server.service.FrontUserInfoService;
import com.ikea.digital.permission.server.service.UserRoleService;
import com.ikea.digital.permission.server.service.UserService;
import com.ikea.digital.permission.server.vo.UserDetailVo;
import com.ikea.digital.permission.server.vo.UserInfoVo;

@RestController
@RequestMapping("/rs/user")
@Validated
public class UserController {
	
	@Autowired
	private PermissionClientConfig pmClientConfig;
	
	@Autowired
	private FrontUserInfoService frontUserService;
	
	@Autowired
	private CompositeUserInfoService userInfoService;
	
	@Autowired
	private UserRoleService userRoleService;
	
	@Autowired
	private UserService userService;
	
	@RequestMapping("/create")
	@PmPreAuthorize("hasAnyRoleWithClient('userManager', 'permission-service')")
	public BaseResponse<?> create(@RequestBody @Valid UserCreateOrUpdateDto reqVo){
		userService.createUser(reqVo);
		return BaseResponse.success();
	}
	
	@PostMapping("/update")
	@PmPreAuthorize("hasAnyRoleWithClient('userManager', 'permission-service')")
	public BaseResponse<?> update(@RequestBody  @Validated(UpdateValidGroup.class) UserCreateOrUpdateDto reqVo ){
		userService.updateUser(reqVo);
		return BaseResponse.success();
	}
	
	@GetMapping("/delete/{userId}")
	@PmPreAuthorize("hasAnyRoleWithClient('deleteManager','permission-service')")
	public BaseResponse<?> remove(@PathVariable("userId") String userId){
		userService.deleteUser(userId);
		return BaseResponse.success();
	}
	
	@GetMapping("/search")
	public BaseResponse<List<User>> search(@RequestParam(value = "clientId",required = false) String clientId,
			@RequestParam(value = "userName", required = false) String userName,@RequestParam(value = "roleName",required = false) String roleName,
			@RequestParam(name = "pageIndex", defaultValue = "1") int pageIndx, 
			@RequestParam(name = "pageSize", defaultValue = "10") int pageSize){
		if(StringUtils.isAllBlank(userName,roleName)) {
			throw new ResourceException("用户名、角色名不能同时为空");
		}
		
		UserSearchDto dto = new UserSearchDto(clientId,userName, roleName, pageIndx, pageSize);
//		UserSearchVo result = userService.searchUser(dto);
		return BaseResponse.success(userService.searchUser(dto));
	}
	
	@GetMapping("/search/total")
	public BaseResponse<Integer> searchTotal(@RequestParam(value = "clientId",required = false) String clientId,
			@RequestParam(value = "userName", required = false) String userName,
			@RequestParam(value = "roleName",required = false) String roleName){
		if(StringUtils.isAllBlank(userName,roleName)) {
			throw new ResourceException("用户名、角色名不能同时为空");
		}
		int total = userService.searchUserTotal(clientId, userName, roleName);
		return BaseResponse.success(total);
	}
	
	@GetMapping("/detail/{userId}")
	public BaseResponse<UserInfoVo> detail(@PathVariable(value = "userId",required = true) String userId){
		UserInfoVo result = frontUserService.queryUserDetail(userId);
		if(Objects.isNull(result)) {
			return BaseResponse.error(RespCodeEnum.USER_NOT_EXIST);
		}
		return BaseResponse.success(result);
	}
	
	// for sdk
	@GetMapping("/info")
	public BaseResponse<UserDetailVo> queryUserWithUserTokenFromClient(HttpServletRequest request,@RequestParam(value = "userToken", required = false) String userToken) throws IOException {
		if(StringUtils.isBlank(userToken)){
			userToken = CookieUtils.readCookie(request, pmClientConfig.getEnv() + "_" + pmClientConfig.getClientId()); 
		}
		if(StringUtils.isBlank(userToken)) {
			return BaseResponse.error(RespCodeEnum.INVALID_CLIENT);
		}
		UserDetailVo userDetailInfo = userInfoService.fetchUserByUserToken(userToken);
		if (userDetailInfo == null) {
			return BaseResponse.error(RespCodeEnum.NOAUTH_ERROR);
		}
		
		return BaseResponse.success(userDetailInfo);
	}
	
	// for prm front page
	@GetMapping("/pmInfo")
	public BaseResponse<UserInfoVo> pmInfo(HttpServletRequest request, @RequestParam(value = "userToken", required = false) String userToken) throws IOException {
		if(StringUtils.isBlank(userToken)){
			userToken = CookieUtils.readCookie(request, pmClientConfig.getEnv() + "_" + pmClientConfig.getClientId()); 
		}
		if(StringUtils.isBlank(userToken)) {
			return BaseResponse.error(RespCodeEnum.INVALID_CLIENT);
		}
		UserInfoVo userDetailInfo = frontUserService.fetchUserByUserToken(userToken);
		if (userDetailInfo == null) {
			return BaseResponse.error(RespCodeEnum.NOAUTH_ERROR);
		}
		
		return BaseResponse.success(userDetailInfo);
	}
	
	@PostMapping("/assignGroup")
	@PmPreAuthorize("hasAnyRoleWithClient('userManager','permission-service')")
	public BaseResponse<?> assignGroup(@RequestBody @Valid UserGrantGroupDto dto){
		userService.assignGroup(dto);
		return BaseResponse.success();
	}
	
	@PostMapping("/assignRole")
	public BaseResponse<?> assignRole(@RequestBody @Valid UserGrantRoleDto dto){
		userRoleService.assignRole(dto);
		return BaseResponse.success();
	}
	
	@GetMapping("check")
	public BaseResponse<?> test() {
		return BaseResponse.success();
	}
	
	@GetMapping("/allKcRoles")
	public BaseResponse<User> listUserAllKcRoles(@RequestParam String userId) {
		return BaseResponse.success(userService.fetchUserById(userId));
	}
	
}
