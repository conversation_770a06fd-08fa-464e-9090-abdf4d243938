import jwt from 'jsonwebtoken';

/**
 * Keycloak JWT payload structure
 */
export interface KeycloakJWTPayload {
  sub: string;           // User ID
  sid: string;           // Session ID
  iss: string;           // Issuer
  aud: string | string[]; // Audience
  exp: number;           // Expiration timestamp
  iat: number;           // Issued at timestamp
  azp?: string;          // Authorized party (client ID)
  client_id?: string;    // Client ID
  scope?: string;        // Scopes (space-separated)
  email?: string;        // User email
  preferred_username?: string; // Username
  given_name?: string;   // First name
  family_name?: string;  // Last name
  name?: string;         // Full name
  realm_access?: {       // Realm-level roles
    roles: string[];
  };
  resource_access?: {    // Client-specific roles
    [clientId: string]: {
      roles: string[];
    };
  };
  session_state?: string; // Keycloak session state
  jti?: string;          // JWT ID
  typ?: string;          // Token type
  [key: string]: any;    // Additional custom claims
}

/**
 * Session information extracted from JWT
 */
export interface SessionInfo {
  userId: string;
  sessionId: string;
  clientId?: string;
  email?: string;
  username?: string;
  scopes: string[];
  roles: string[];
  expiresAt: Date;
  issuedAt: Date;
  issuer: string;
}

/**
 * Decode JWT token without verification (for extracting session info)
 * Use this when you trust the token source but need to extract claims
 */
export function decodeJWTUnsafe(token: string): KeycloakJWTPayload {
  try {
    const decoded = jwt.decode(token) as KeycloakJWTPayload;
    if (!decoded || typeof decoded !== 'object') {
      throw new Error('Invalid JWT token structure');
    }
    return decoded;
  } catch (error) {
    throw new Error(`Failed to decode JWT token: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Extract session information from JWT token
 */
export function extractSessionInfo(token: string): SessionInfo {
  const payload = decodeJWTUnsafe(token);
  
  // Validate required fields
  if (!payload.sub) {
    throw new Error('JWT token missing required "sub" (user ID) claim');
  }
  
  if (!payload.sid) {
    throw new Error('JWT token missing required "sid" (session ID) claim');
  }
  
  // Extract scopes
  const scopes = payload.scope ? payload.scope.split(' ') : [];
  
  // Extract roles from realm_access and resource_access
  const roles: string[] = [];
  
  // Add realm roles
  if (payload.realm_access?.roles) {
    roles.push(...payload.realm_access.roles);
  }
  
  // Add client-specific roles
  if (payload.resource_access) {
    Object.values(payload.resource_access).forEach(clientAccess => {
      if (clientAccess.roles) {
        roles.push(...clientAccess.roles);
      }
    });
  }
  
  return {
    userId: payload.sub,
    sessionId: payload.sid,
    clientId: payload.azp || payload.client_id,
    email: payload.email,
    username: payload.preferred_username,
    scopes: scopes,
    roles: [...new Set(roles)], // Remove duplicates
    expiresAt: new Date(payload.exp * 1000),
    issuedAt: new Date(payload.iat * 1000),
    issuer: payload.iss
  };
}

/**
 * Check if JWT token is expired
 */
export function isJWTExpired(token: string, bufferSeconds: number = 60): boolean {
  try {
    const payload = decodeJWTUnsafe(token);
    const now = Math.floor(Date.now() / 1000);
    return payload.exp <= (now + bufferSeconds);
  } catch (error) {
    // If we can't decode the token, consider it expired
    return true;
  }
}

/**
 * Get JWT expiration time
 */
export function getJWTExpiration(token: string): Date | null {
  try {
    const payload = decodeJWTUnsafe(token);
    return new Date(payload.exp * 1000);
  } catch (error) {
    return null;
  }
}

/**
 * Extract user-friendly information from JWT
 */
export function extractUserInfo(token: string): {
  id: string;
  email?: string;
  name?: string;
  username?: string;
  roles: string[];
  scopes: string[];
} {
  const sessionInfo = extractSessionInfo(token);
  
  return {
    id: sessionInfo.userId,
    email: sessionInfo.email,
    name: extractFullName(decodeJWTUnsafe(token)),
    username: sessionInfo.username,
    roles: sessionInfo.roles,
    scopes: sessionInfo.scopes
  };
}

/**
 * Extract full name from JWT payload
 */
function extractFullName(payload: KeycloakJWTPayload): string | undefined {
  if (payload.name) {
    return payload.name;
  }
  
  if (payload.given_name && payload.family_name) {
    return `${payload.given_name} ${payload.family_name}`;
  }
  
  if (payload.given_name) {
    return payload.given_name;
  }
  
  return undefined;
}

/**
 * Validate JWT token structure (without signature verification)
 */
export function validateJWTStructure(token: string): {
  valid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  try {
    const payload = decodeJWTUnsafe(token);
    
    // Check required claims
    if (!payload.sub) errors.push('Missing "sub" (user ID) claim');
    if (!payload.iss) errors.push('Missing "iss" (issuer) claim');
    if (!payload.exp) errors.push('Missing "exp" (expiration) claim');
    if (!payload.iat) errors.push('Missing "iat" (issued at) claim');
    
    // Check Keycloak-specific claims
    if (!payload.sid) warnings.push('Missing "sid" (session ID) claim - may not be a Keycloak token');
    if (!payload.azp && !payload.client_id) warnings.push('Missing client ID claim');
    
    // Check expiration
    if (payload.exp && isJWTExpired(token)) {
      warnings.push('Token is expired');
    }
    
    // Check issuer format
    if (payload.iss && !payload.iss.includes('keycloak')) {
      warnings.push('Issuer does not appear to be Keycloak');
    }
    
  } catch (error) {
    errors.push(`Failed to decode token: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Create a cache key from JWT token (for caching purposes)
 */
export function createJWTCacheKey(token: string): string {
  try {
    const payload = decodeJWTUnsafe(token);
    return `${payload.sub}_${payload.sid}_${payload.iat}`;
  } catch (error) {
    // Fallback to token hash if decoding fails
    return Buffer.from(token).toString('base64').substring(0, 32);
  }
}

/**
 * Pretty print JWT payload for debugging
 */
export function prettyPrintJWT(token: string): string {
  try {
    const payload = decodeJWTUnsafe(token);
    const sessionInfo = extractSessionInfo(token);
    
    return `
JWT Token Information:
=====================
User ID: ${sessionInfo.userId}
Session ID: ${sessionInfo.sessionId}
Client ID: ${sessionInfo.clientId || 'N/A'}
Email: ${sessionInfo.email || 'N/A'}
Username: ${sessionInfo.username || 'N/A'}
Issuer: ${sessionInfo.issuer}
Issued At: ${sessionInfo.issuedAt.toISOString()}
Expires At: ${sessionInfo.expiresAt.toISOString()}
Scopes: ${sessionInfo.scopes.join(', ') || 'None'}
Roles: ${sessionInfo.roles.join(', ') || 'None'}
Status: ${isJWTExpired(token) ? 'EXPIRED' : 'VALID'}
`.trim();
  } catch (error) {
    return `Failed to parse JWT token: ${error instanceof Error ? error.message : 'Unknown error'}`;
  }
}
