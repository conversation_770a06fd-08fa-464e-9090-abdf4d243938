package com.ikea.mas.permission.exception;

public class AuthException extends RuntimeException {
	
	private static final long serialVersionUID = 1L;

	private Integer code;

	public AuthException() {
		super();
	}

	public AuthException(String message) {
		super(message);
	}
	
	public AuthException(Integer code,String message) {
		super(message);
		this.code = code;
	}

	public AuthException(String message, Throwable cause) {
		super(message, cause);
	}

	public AuthException(Throwable cause) {
		super(cause);
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}
	

}
