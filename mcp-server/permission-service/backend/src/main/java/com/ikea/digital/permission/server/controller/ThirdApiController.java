package com.ikea.digital.permission.server.controller;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ikea.digital.permission.server.common.BaseResponse;
import com.ikea.digital.permission.server.common.RespCodeEnum;
import com.ikea.digital.permission.server.common.util.ApplicationHolder;
import com.ikea.digital.permission.server.dto.AzureUserQueryDto;
import com.ikea.digital.permission.server.dto.OrganizationUserDto;
import com.ikea.digital.permission.server.dto.UserSearchDto;
import com.ikea.digital.permission.server.resource.domain.model.User;
import com.ikea.digital.permission.server.service.AzureUserService;
import com.ikea.digital.permission.server.service.OrgService;
import com.ikea.digital.permission.server.service.UserService;
import com.ikea.digital.permission.server.vo.AzureUserPageVo;
import com.ikea.digital.permission.server.vo.MicrosoftUserResVo;
import com.ikea.digital.permission.server.vo.OrgUserInfoVo;

@RestController
@RequestMapping("/third/api")
public class ThirdApiController {

	@Autowired
	private OrgService orgService;
	
	@Autowired
	private AzureUserService azureUserService;
	
	@Autowired
	private UserService userService;
	
	@GetMapping("/org/microsoft/user/{email}")
	public BaseResponse<MicrosoftUserResVo> info(@PathVariable String email)  {
		MicrosoftUserResVo user = azureUserService.get(email);
		if(Objects.isNull(user)) {
			return BaseResponse.error(RespCodeEnum.USER_NOT_EXIST);
		}
		return BaseResponse.success(user);	
	}
	
	@GetMapping("/org/microsoft/users/{netWorkIdOrEmployeeId}")
	public BaseResponse<List<MicrosoftUserResVo>> users(@PathVariable String netWorkIdOrEmployeeId)  {
		List<MicrosoftUserResVo> users = azureUserService.users(netWorkIdOrEmployeeId);
		return BaseResponse.success(users);
	}
	
	@PostMapping("/org/microsoft/users")
	public BaseResponse<AzureUserPageVo> usersByDepartment(@Valid @RequestBody AzureUserQueryDto query)  {
		if(Objects.isNull(query.getPageSize()) || query.getPageSize() < 0) {
			query.setPageSize(10);
		}
		if(!"department".equals(query.getType()) && !"officeLocation".equals(query.getType())) {
			return BaseResponse.error(RespCodeEnum.BAD_REQUEST);
		}
		AzureUserPageVo users = azureUserService.users(query);
		return BaseResponse.success(users);
	}
	
	@GetMapping("/org/user/child/{email}")
	public BaseResponse<Map<String, List<OrgUserInfoVo>>> childUser(@PathVariable @NotEmpty String email)  {
		OrganizationUserDto dto = new OrganizationUserDto();
		dto.setClientId(ApplicationHolder.get());
		dto.setUserEmail(Collections.singleton(email));
		List<OrgUserInfoVo> data = orgService.childUser(dto);
		Map<String, List<OrgUserInfoVo>> map = new HashMap<>();
		map.put("userList", data);
		return BaseResponse.success(map);
	}
	
	@GetMapping("/org/users")
	public BaseResponse<List<String>> users(
			@RequestHeader("clientId") String clientId,
			@RequestParam @NotEmpty List<Long> orgIds)  {
		List<String> data = orgService.getUsers(clientId, orgIds);
		return BaseResponse.success(data);
	}
	
	@GetMapping("/role/{roleName}/users")
	public BaseResponse<List<User>> users(
			@PathVariable("roleName") @NotBlank String roleName,
			@RequestHeader("clientId") String clientId,
			@RequestParam(name = "storeId",required = false) String storeId,
			@RequestParam(name = "start", defaultValue = "1") int start, 
			@RequestParam(name = "size", defaultValue = "10") int size) {
		if(start < 1) start = 1;
		if(size < 1) start = 10;
		UserSearchDto dto = new UserSearchDto(clientId, StringUtils.EMPTY, roleName, start, size);
		return BaseResponse.success(userService.searchUser(dto,storeId));
	}
 
}
