#!/usr/bin/env node

/**
 * 🧪 Dynamic Cookies Test Script
 *
 * Purpose:
 * 1. Test dynamic cookie acquisition flow
 * 2. Verify OAuth2/OIDC flow implementation
 * 3. Check cookie caching and refresh logic
 * 4. Test integration with service adapter
 */

import { globalDynamicCookieManager } from '../dist/auth/dynamic-cookie-manager.js';
import { getEnvironmentConfig } from '../dist/utils/env.js';

/**
 * Test dynamic cookie configuration
 */
async function testDynamicCookieConfig() {
  console.log('🔧 ==================== DYNAMIC COOKIE CONFIG TEST ====================');
  
  const env = getEnvironmentConfig();
  
  console.log('🌍 Dynamic Cookie Configuration:');
  console.log(`   Enabled: ${env.DYNAMIC_COOKIES_ENABLED ? '✅' : '❌'}`);
  console.log(`   Initial JSESSIONID: ${env.INITIAL_JSESSIONID ? '✅ Configured' : '❌ Missing'}`);
  console.log(`   Keycloak Auth Session: ${env.KEYCLOAK_AUTH_SESSION_ID ? '✅ Configured' : '❌ Missing'}`);
  console.log(`   Keycloak Identity: ${env.KEYCLOAK_IDENTITY ? '✅ Configured' : '❌ Missing'}`);
  console.log(`   Keycloak Session: ${env.KEYCLOAK_SESSION ? '✅ Configured' : '❌ Missing'}`);
  
  if (!env.DYNAMIC_COOKIES_ENABLED) {
    console.log('\n❌ Dynamic cookies are disabled. Set DYNAMIC_COOKIES_ENABLED=true to test.');
    return false;
  }
  
  const missingConfigs = [];
  if (!env.INITIAL_JSESSIONID) missingConfigs.push('INITIAL_JSESSIONID');
  if (!env.KEYCLOAK_AUTH_SESSION_ID) missingConfigs.push('KEYCLOAK_AUTH_SESSION_ID');
  if (!env.KEYCLOAK_IDENTITY) missingConfigs.push('KEYCLOAK_IDENTITY');
  if (!env.KEYCLOAK_SESSION) missingConfigs.push('KEYCLOAK_SESSION');
  
  if (missingConfigs.length > 0) {
    console.log(`\n❌ Missing required configurations: ${missingConfigs.join(', ')}`);
    console.log('Please configure these environment variables to test dynamic cookies.');
    return false;
  }
  
  console.log('\n✅ All dynamic cookie configurations are present');
  return true;
}

/**
 * Test cookie acquisition
 */
async function testCookieAcquisition() {
  console.log('\n🍪 ==================== COOKIE ACQUISITION TEST ====================');
  
  try {
    console.log('🔄 Attempting to acquire session cookie...');
    const startTime = Date.now();
    
    const cookie = await globalDynamicCookieManager.getSessionCookie();
    
    const duration = Date.now() - startTime;
    
    if (cookie) {
      console.log(`✅ Cookie acquisition successful! (${duration}ms)`);
      console.log(`   Cookie: ${cookie.substring(0, 50)}...`);
      
      // Test cache stats
      const stats = globalDynamicCookieManager.getCacheStats();
      console.log('📊 Cache Statistics:');
      console.log(`   Has Cached: ${stats.hasCached ? '✅' : '❌'}`);
      if (stats.createdAt) {
        console.log(`   Created: ${new Date(stats.createdAt).toISOString()}`);
      }
      if (stats.expiresAt) {
        console.log(`   Expires: ${new Date(stats.expiresAt).toISOString()}`);
        const ttl = Math.round((stats.expiresAt - Date.now()) / 1000);
        console.log(`   TTL: ${ttl} seconds`);
      }
      
      return cookie;
    } else {
      console.log('❌ Cookie acquisition returned null');
      return null;
    }
  } catch (error) {
    console.error('❌ Cookie acquisition failed:', error.message);
    console.error('   Stack:', error.stack);
    return null;
  }
}

/**
 * Test cookie caching
 */
async function testCookieCaching(firstCookie) {
  console.log('\n💾 ==================== COOKIE CACHING TEST ====================');
  
  if (!firstCookie) {
    console.log('❌ Skipping cache test - no cookie to test with');
    return;
  }
  
  try {
    console.log('🔄 Testing cached cookie retrieval...');
    const startTime = Date.now();
    
    const cachedCookie = await globalDynamicCookieManager.getSessionCookie();
    
    const duration = Date.now() - startTime;
    
    if (cachedCookie === firstCookie) {
      console.log(`✅ Cache hit successful! (${duration}ms)`);
      console.log('   Same cookie returned from cache');
    } else if (cachedCookie) {
      console.log(`⚠️ Different cookie returned (${duration}ms)`);
      console.log('   This might indicate cache expiry or refresh');
    } else {
      console.log('❌ Cache returned null');
    }
  } catch (error) {
    console.error('❌ Cache test failed:', error.message);
  }
}

/**
 * Test cache clearing
 */
async function testCacheClearing() {
  console.log('\n🗑️ ==================== CACHE CLEARING TEST ====================');
  
  try {
    console.log('🔄 Clearing cache...');
    globalDynamicCookieManager.clearCache();
    
    const stats = globalDynamicCookieManager.getCacheStats();
    if (!stats.hasCached) {
      console.log('✅ Cache cleared successfully');
    } else {
      console.log('❌ Cache clearing failed');
    }
    
    // Test acquisition after cache clear
    console.log('🔄 Testing acquisition after cache clear...');
    const startTime = Date.now();
    const newCookie = await globalDynamicCookieManager.getSessionCookie();
    const duration = Date.now() - startTime;
    
    if (newCookie) {
      console.log(`✅ New cookie acquired after cache clear (${duration}ms)`);
    } else {
      console.log('❌ Failed to acquire new cookie after cache clear');
    }
  } catch (error) {
    console.error('❌ Cache clearing test failed:', error.message);
  }
}

/**
 * Test service integration
 */
async function testServiceIntegration() {
  console.log('\n🔧 ==================== SERVICE INTEGRATION TEST ====================');
  
  try {
    // Import service modules
    const { SERVICE_MODULES } = await import('../dist/adapters/service-adapter.js');
    
    console.log('🔄 Testing service call with dynamic cookies...');
    const startTime = Date.now();
    
    // Test a simple service call that should use dynamic cookies
    const result = await SERVICE_MODULES.global.getCurrentUser({});
    const duration = Date.now() - startTime;
    
    console.log(`✅ Service call successful! (${duration}ms)`);
    console.log('   Result:', JSON.stringify(result, null, 2));
    
  } catch (error) {
    console.error('❌ Service integration test failed:', error.message);
    console.error('   This might be expected if the service requires specific authentication');
  }
}

/**
 * Main test function
 */
async function runDynamicCookiesTest() {
  console.log('🧪 ==================== DYNAMIC COOKIES TEST SUITE ====================');
  console.log('🎯 Purpose: Test dynamic cookie acquisition and integration\n');

  // Test 1: Configuration
  const configValid = await testDynamicCookieConfig();
  if (!configValid) {
    console.log('\n❌ Configuration test failed. Exiting.');
    return;
  }

  // Test 2: Cookie acquisition
  const firstCookie = await testCookieAcquisition();

  // Test 3: Cookie caching
  await testCookieCaching(firstCookie);

  // Test 4: Cache clearing
  await testCacheClearing();

  // Test 5: Service integration
  await testServiceIntegration();

  console.log('\n🏁 ==================== TEST SUITE COMPLETE ====================');
  console.log('✅ Dynamic cookies test suite finished');
  console.log('💡 Check the logs above for detailed results');
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runDynamicCookiesTest().catch(error => {
    console.error('💥 Test suite crashed:', error);
    process.exit(1);
  });
}

export { runDynamicCookiesTest };
