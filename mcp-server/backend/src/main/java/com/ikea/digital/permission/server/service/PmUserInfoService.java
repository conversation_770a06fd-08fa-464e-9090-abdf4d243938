/**
 * Project Name:permission-server
 * File Name:PermissionServiceUserInfoService.java
 * Package Name:com.ikea.digital.permission.server.resource.service
 * Date:Feb 26, 20244:47:28 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.ikea.digital.permission.server.common.util.JacksonUtils;
import com.ikea.digital.permission.server.resource.domain.model.UserRole;
import com.ikea.digital.permission.server.vo.UserDetailVo;

/**
 * ClassName:PmUserInfoService <br/>
 * Function: Permission service后端权限验证不需要门店, 但是需要返回用户拥有的所有client的Role、资源等. <br/>
 * 目前仅permission service服务通过sdk获取用户信息用于权限校验时候需要用到此接口.<br/>
 * Date:     Feb 26, 2024 4:47:28 PM <br/>
 * <AUTHOR>	 
 */
@Service("pmUserInfoService")
public class PmUserInfoService extends AbstractUserInfoService {

	@SuppressWarnings("unchecked")
	@Override
	protected void fillUserRolesAndStores(UserDetailVo user, Map<String, List<UserRole>> userRoleStoreMap) {
		user.setClientRoleModelMap(Collections.emptyMap());
		List<UserRole> userClientRoles = userRoleStoreMap.get(pmClientConfig.getClientId());
		if(CollectionUtils.isNotEmpty(userClientRoles)) {
			Map<String,List<String>> roleStoreMap = userClientRoles.stream().collect(Collectors.toMap(UserRole::getRoleName, po -> { return JacksonUtils.readValue(po.getStoreIds(), List.class);}, (e1, e2) -> e1));
			user.setRoleStoreMap(roleStoreMap);
			fillMenu(user, pmClientConfig.getClientId());
		}
	}
	
	@Override
	protected UserDetailVo loadUserInfoFromCache(String userToken, String userId, String clientId) {
		return null;
	}
	
	@Override
	protected void cacheUserInfo(UserDetailVo userDetail, String clientId) {
	}
	
	@Override
	public boolean supportClient(String clientId) {
		return pmClientConfig.getClientId().equals(clientId);
	}

}