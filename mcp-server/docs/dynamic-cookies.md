# Dynamic Cookie Management

This document describes the dynamic cookie acquisition system that replaces static `AUTH_COOKIES` configuration with on-the-fly cookie generation through OAuth2/OIDC flow.

## Overview

The Dynamic Cookie Manager implements the complete browser-based authentication flow programmatically to obtain session cookies that can be used for API authentication. This eliminates the need to manually extract and configure cookies in environment variables.

## Authentication Flow

The system implements a 4-step OAuth2/OIDC flow:

### Step 1: Initial Authentication Request
```
GET https://api-dev-mpp-fe.ingka-dt.cn/prm-auth/auth/toLogin
Cookie: JSESSIONID=<initial_session_id>
```
**Response**: 302 redirect to Keycloak OAuth2 endpoint

### Step 2: Keycloak OAuth2 Authentication
```
GET https://keycloak.ingka-dt.cn/auth/realms/master/protocol/openid-connect/auth
Cookie: KEYCLOAK_IDENTITY=<jwt_token>; KEYCLOAK_SESSION=<session_id>; ...
```
**Response**: 302 redirect with authorization code

### Step 3: Complete OAuth2 Flow
```
GET https://api-dev-mpp-fe.ingka-dt.cn/prm-auth/auth/loginComplete
Cookie: JSESSIONID=<initial_session_id>
```
**Response**: 302 redirect with `pm_user_token`

### Step 4: Exchange Token for Session Cookie
```
GET https://fe-dev-i.ingka-dt.cn/order-web/user/current?pm_user_token=<token>
```
**Response**: `Set-Cookie: test_orders-portal=<session_cookie>`

## Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# Enable dynamic cookie acquisition
DYNAMIC_COOKIES_ENABLED=true

# Initial JSESSIONID (can be any valid session ID)
INITIAL_JSESSIONID=AFFF8C65A616C396F7292C4E25C65D57

# Keycloak session cookies (extract from browser)
KEYCLOAK_AUTH_SESSION_ID=7ab42bd9-bac2-480d-b375-7ba58de23239.keycloak-dev-857f5d85dc-zkl9k-27830
KEYCLOAK_IDENTITY=eyJhbGciOiJIUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICIwYTQ4ZGU0Yy1jMDU4LTQ1YWUtYjkxZS03MGRkODRjMDQ4MDMifQ...
KEYCLOAK_SESSION="master/3d2e265c-f895-4e32-b3be-5a139fa856c1/7ab42bd9-bac2-480d-b375-7ba58de23239"
KEYCLOAK_LOCALE=en
```

### Obtaining Keycloak Cookies

1. **Login to Keycloak** in your browser
2. **Open Developer Tools** → Network tab
3. **Navigate to any protected resource** that triggers Keycloak authentication
4. **Find a request to keycloak.ingka-dt.cn** in the network log
5. **Copy the cookie values** from the request headers:
   - `AUTH_SESSION_ID` or `AUTH_SESSION_ID_LEGACY`
   - `KEYCLOAK_IDENTITY` or `KEYCLOAK_IDENTITY_LEGACY`
   - `KEYCLOAK_SESSION` or `KEYCLOAK_SESSION_LEGACY`

## Usage

### Automatic Integration

The dynamic cookie manager is automatically integrated into the service adapter:

```typescript
// Authentication priority:
// 1. OAuth2 Bearer token (if available)
// 2. Dynamic cookies (if enabled and configured)
// 3. Static AUTH_COOKIES (fallback)
```

### Manual Usage

```typescript
import { globalDynamicCookieManager } from './auth/dynamic-cookie-manager.js';

// Get a session cookie
const cookie = await globalDynamicCookieManager.getSessionCookie();

// Use the cookie for API calls
const response = await fetch('/api/endpoint', {
  headers: {
    'Cookie': cookie
  }
});
```

### Cache Management

```typescript
// Get cache statistics
const stats = globalDynamicCookieManager.getCacheStats();
console.log('Cache hit rate:', stats.hasCached);

// Clear cache (force refresh)
globalDynamicCookieManager.clearCache();
```

## Features

### 🚀 Automatic Cookie Acquisition
- Implements complete OAuth2/OIDC flow programmatically
- No manual cookie extraction required
- Seamless integration with existing authentication

### 💾 Intelligent Caching
- Caches cookies for 1 hour (based on server TTL)
- Automatic refresh before expiration (5-minute buffer)
- Prevents concurrent acquisition requests

### 🔄 Graceful Fallback
- Falls back to static `AUTH_COOKIES` if dynamic acquisition fails
- Maintains backward compatibility
- Detailed error logging for troubleshooting

### 🛡️ Security Features
- Respects cookie expiration times
- Secure cookie handling
- No sensitive data logged

## Testing

### Quick Test
```bash
npm run test:dynamic-cookies
```

### Manual Testing
```bash
node test/dynamic-cookies.js
```

### Test Coverage
- ✅ Configuration validation
- ✅ Cookie acquisition flow
- ✅ Cache hit/miss scenarios
- ✅ Cache expiration handling
- ✅ Service integration
- ✅ Error handling and fallbacks

## Troubleshooting

### Common Issues

#### 1. "Dynamic cookies disabled"
**Solution**: Set `DYNAMIC_COOKIES_ENABLED=true` in your `.env` file

#### 2. "Missing required configurations"
**Solution**: Ensure all Keycloak cookie values are configured:
- `INITIAL_JSESSIONID`
- `KEYCLOAK_AUTH_SESSION_ID`
- `KEYCLOAK_IDENTITY`
- `KEYCLOAK_SESSION`

#### 3. "Cookie acquisition failed"
**Possible causes**:
- Expired Keycloak cookies (re-extract from browser)
- Network connectivity issues
- Changed authentication endpoints
- Invalid JSESSIONID

#### 4. "No redirect URL from Keycloak"
**Solution**: Check if Keycloak cookies are still valid and re-extract if needed

### Debug Logging

Enable detailed logging to troubleshoot issues:

```bash
DEBUG_SERVICE_ADAPTER=true npm run dev
```

Look for log entries with `[DynamicCookieManager]` prefix.

### Cookie Refresh

Keycloak cookies typically expire after a few hours. When you see authentication failures:

1. **Re-login to Keycloak** in your browser
2. **Extract new cookie values** using developer tools
3. **Update your `.env` file** with new values
4. **Restart the MCP server**

## Performance

### Benchmarks
- **First acquisition**: ~500-1000ms (full OAuth2 flow)
- **Cache hits**: <1ms
- **Cache size**: ~1KB per cached cookie
- **Memory usage**: Minimal (single cookie cache)

### Optimization
- Cookies are cached for maximum TTL (1 hour)
- Automatic refresh prevents expiration
- Concurrent requests share the same acquisition promise

## Migration Guide

### From Static AUTH_COOKIES

1. **Keep existing setup** (for fallback)
2. **Add dynamic cookie configuration**
3. **Enable dynamic cookies**: `DYNAMIC_COOKIES_ENABLED=true`
4. **Test thoroughly** before removing static cookies
5. **Remove AUTH_COOKIES** once confident in dynamic system

### Rollback Plan

If issues occur, simply set `DYNAMIC_COOKIES_ENABLED=false` to revert to static cookies.

## Security Considerations

1. **Cookie Storage**: Cookies are stored in memory only (not persisted)
2. **Expiration**: Respects server-provided TTL values
3. **Logging**: Sensitive cookie values are not logged
4. **Network**: Uses HTTPS for all authentication requests
5. **Isolation**: Each server instance maintains its own cookie cache

## Future Enhancements

- [ ] **Automatic cookie refresh** before expiration
- [ ] **Multiple user support** with user-specific cookie caches
- [ ] **Persistent cookie storage** for server restarts
- [ ] **Health checks** for cookie validity
- [ ] **Metrics and monitoring** for cookie acquisition success rates
