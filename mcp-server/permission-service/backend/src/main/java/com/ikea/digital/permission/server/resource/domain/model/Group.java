package com.ikea.digital.permission.server.resource.domain.model;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.keycloak.representations.idm.GroupRepresentation;

import com.ikea.digital.permission.server.vo.GroupVo;

import lombok.Data;

@Data
public class Group {

	String id;
	
	String name;
	
	List<Group> subGroup;
	
	public Group(){}
	
	public Group(GroupRepresentation group){
		initWithKcGroup(group);
	}
	
	public void initWithKcGroup(GroupRepresentation group){
		this.setId(group.getId());
		this.setName(group.getName());
		this.setSubGroup(this.conver(group.getSubGroups()));
	}
	
	private List<Group> conver(List<GroupRepresentation> subGroups) {
		if(CollectionUtils.isEmpty(subGroups)) {
			return Collections.emptyList();
		}
		return subGroups.stream().map(Group::new).collect(Collectors.toList());
	}
	
	public static List<GroupVo> toGroupVoList(List<Group> list) {
		if(CollectionUtils.isEmpty(list)) {
			return Collections.emptyList();
		}
		
		return list.stream().map(group -> {
			GroupVo vo = new GroupVo();
			vo.setId(group.getId());
			vo.setName(group.getName());
			vo.setSubGroup(toGroupVoList(group.getSubGroup()));
			return vo;
		}).collect(Collectors.toList());
		
	}
	
}
