package com.ikea.digital.permission.core;

import com.ikea.digital.permission.core.config.PermissionClientConfig;

import lombok.extern.slf4j.Slf4j;

import org.apache.hc.client5.http.ConnectionKeepAliveStrategy;
import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.config.ConnectionConfig;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.io.HttpClientConnectionManager;
import org.apache.hc.core5.http.HeaderElement;
import org.apache.hc.core5.http.message.BasicHeaderElementIterator;
import org.apache.hc.core5.util.TimeValue;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(PermissionClientConfig.class)
@ConditionalOnProperty(value = "permission.client.enable", havingValue = "true", matchIfMissing = true)
public class PermissionFilterConfig {
	
	private int maxTotalConnect = 10; //连接池的最大连接数
    private int maxConnectPerRoute = 10; //单个route的最大连接数
    private int timeToLive = 2; // 连接最大存活时间120s
    private int connectTimeout = 5000; //连接超时5s
    private int readTimeout = 5000; //读取超时5s
    private int keepAliveTime = 60; //默认长链接时间60s

    @Bean
    public PreAuthorizeAspect preAuthorizeAspect() {
        return new PreAuthorizeAspect();
    }
    
    private ConnectionConfig connectionConfig() {
		return ConnectionConfig.custom().setConnectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
				.setSocketTimeout(readTimeout, TimeUnit.MILLISECONDS)
				.setTimeToLive(timeToLive, TimeUnit.SECONDS)
				.build();
	}
    
    private HttpClientConnectionManager httpClientConMgr() {
		PoolingHttpClientConnectionManager httpClientConMgr = new PoolingHttpClientConnectionManager();
		httpClientConMgr.setMaxTotal(maxTotalConnect);
		httpClientConMgr.setDefaultMaxPerRoute(maxConnectPerRoute);
		httpClientConMgr.setDefaultConnectionConfig(connectionConfig());
		return httpClientConMgr;
	}
    
    private ConnectionKeepAliveStrategy connectionKeepAliveStrategy() {
		return (response, context) -> {
			BasicHeaderElementIterator it = new BasicHeaderElementIterator(response.headerIterator("Keep-Alive"));
			while (it.hasNext()) {
				HeaderElement he = it.next();
				String value = he.getValue();
				if (value != null && "timeout".equalsIgnoreCase(he.getName())) {
					try {
						return TimeValue.ofSeconds(Long.valueOf(value));
					} catch (NumberFormatException ignore) {
						log.error("解析长连接过期时间异常", ignore);
					}
				}
			}
			// 长连接保持时间
			return TimeValue.ofSeconds(keepAliveTime);
		};
	}
    
    private ClientHttpRequestFactory clientHttpRequestFactory() {
		HttpClient httpClient = HttpClientBuilder.create()
				.setConnectionManager(httpClientConMgr())
				.setKeepAliveStrategy(connectionKeepAliveStrategy())
				.evictExpiredConnections()
				.evictIdleConnections(TimeValue.ofMinutes(1))
                .build();
        return new HttpComponentsClientHttpRequestFactory(httpClient);
	}
    
    private RestTemplate pmRestTemplate(RestTemplateBuilder builder, ObjectProvider<ClientHttpRequestFactory> clientHttpRequestFactoryProvider) {
    	Supplier<ClientHttpRequestFactory> clientHttpRequestFactory = () -> Optional.ofNullable(clientHttpRequestFactoryProvider.getIfAvailable()).orElseGet(() -> clientHttpRequestFactory());
    	return builder.requestFactory(clientHttpRequestFactory).build();
    }
    
    @ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.REACTIVE)
    @Bean
    public PermissionWebFluxSender permissionWebFluxSender(PermissionClientConfig pmClientConfig, 
    		RestTemplateBuilder builder,
    		@Qualifier("pmClientHttpRequestFactory") ObjectProvider<ClientHttpRequestFactory> pmClientHttpRequestFactory) {
        return new PermissionWebFluxSender(pmClientConfig, pmRestTemplate(builder, pmClientHttpRequestFactory));
    }
    
    @Order(-1)
    @ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.REACTIVE)
    @Bean
    public PermissionWebFilter registerPermissionWebFluxFilter(PermissionClientConfig pmClientConfig, PermissionWebFluxSender permissionSender) {
    	return new PermissionWebFilter(pmClientConfig, permissionSender);
    }
    
    @ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
	@Configuration
	class PermissionAuthFilterAutoConfiguration {
    	
    	@Bean
        public PermissionSender permissionSender(PermissionClientConfig pmClientConfig, 
        		RestTemplateBuilder builder,
        		@Qualifier("pmClientHttpRequestFactory") ObjectProvider<ClientHttpRequestFactory> pmClientHttpRequestFactory) {
            return new PermissionSender(pmClientConfig, pmRestTemplate(builder, pmClientHttpRequestFactory));
        }
    	
    	@Bean
        public LogoutHandler logoutHandler(PermissionSender permissionSender) {
            return new LogoutHandler(permissionSender);
        }
    	
        @Bean
        public FilterRegistrationBean<PermissionAuthFilter> registerPermissionFilter(PermissionClientConfig pmClientConfig, PermissionSender permissionSender) {
            FilterRegistrationBean<PermissionAuthFilter> filter = new FilterRegistrationBean<>();
            PermissionAuthFilter permissionAuthFilter = new PermissionAuthFilter(pmClientConfig, permissionSender);
            filter.setFilter(permissionAuthFilter);
            List<String> includePath = pmClientConfig.getIncludeAuthenticationPath();
            filter.setUrlPatterns(CollectionUtils.isEmpty(includePath) ? Collections.singleton("/*") : includePath);
            filter.setName(StringUtils.uncapitalize(PermissionAuthFilter.class.getSimpleName()));
            filter.setOrder(1);
            return filter;
        }
	
	}
    
}