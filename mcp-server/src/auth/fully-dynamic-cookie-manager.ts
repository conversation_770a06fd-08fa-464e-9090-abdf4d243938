import axios from 'axios';
import { getEnvironmentConfig } from '../utils/env.js';

/**
 * Configuration for fully dynamic cookie acquisition
 */
export interface FullyDynamicCookieConfig {
  enabled: boolean;
  baseUrls: {
    authService: string;
    keycloak: string;
    orderWeb: string;
  };
  clientId: string;
  redirectUrl: string;
  // Credentials for initial authentication
  credentials: {
    username: string;
    password: string;
  } | {
    serviceAccountId: string;
    serviceAccountSecret: string;
  } | null;
}

/**
 * Session state for tracking the authentication flow
 */
interface SessionState {
  jsessionId?: string;
  keycloakCookies?: {
    authSessionId: string;
    keycloakIdentity: string;
    keycloakSession: string;
  };
  state: string;
  createdAt: number;
}

/**
 * Fully Dynamic Cookie Manager
 * Obtains ALL required credentials automatically without manual configuration
 */
export class FullyDynamicCookieManager {
  private config: FullyDynamicCookieConfig;
  private sessionState: SessionState | null = null;
  private cookieCache = new Map<string, { cookie: string; expiresAt: number }>();

  constructor(config: FullyDynamicCookieConfig) {
    this.config = config;
  }

  /**
   * Get session cookie with fully automatic credential acquisition
   */
  async getSessionCookie(): Promise<string | null> {
    if (!this.config.enabled) {
      console.log('🍪 [FullyDynamicCookieManager] Fully dynamic cookies disabled');
      return null;
    }

    try {
      // Check cache first
      const cached = this.getCachedCookie();
      if (cached) {
        console.log('🎯 [FullyDynamicCookieManager] Using cached cookie');
        return cached;
      }

      // Method 1: Try service account authentication (if configured)
      if (this.hasServiceAccountCredentials()) {
        console.log('🔐 [FullyDynamicCookieManager] Trying service account authentication');
        const cookie = await this.tryServiceAccountAuth();
        if (cookie) {
          this.cacheCookie(cookie);
          return cookie;
        }
      }

      // Method 2: Try username/password authentication (if configured)
      if (this.hasUserCredentials()) {
        console.log('🔐 [FullyDynamicCookieManager] Trying user credential authentication');
        const cookie = await this.tryUserCredentialAuth();
        if (cookie) {
          this.cacheCookie(cookie);
          return cookie;
        }
      }

      // Method 3: Try anonymous session creation
      console.log('🔐 [FullyDynamicCookieManager] Trying anonymous session creation');
      const cookie = await this.tryAnonymousSessionCreation();
      if (cookie) {
        this.cacheCookie(cookie);
        return cookie;
      }

      console.log('❌ [FullyDynamicCookieManager] All automatic methods failed');
      return null;

    } catch (error) {
      console.error('❌ [FullyDynamicCookieManager] Fully dynamic cookie acquisition failed:', error);
      return null;
    }
  }

  /**
   * Method 1: Service Account Authentication
   * Uses client credentials flow to get tokens
   */
  private async tryServiceAccountAuth(): Promise<string | null> {
    try {
      const credentials = this.config.credentials as { serviceAccountId: string; serviceAccountSecret: string };
      
      // Step 1: Get access token using client credentials
      const tokenResponse = await axios.post(
        `${this.config.baseUrls.keycloak}/auth/realms/master/protocol/openid-connect/token`,
        new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: credentials.serviceAccountId,
          client_secret: credentials.serviceAccountSecret,
          scope: 'openid profile'
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      const accessToken = tokenResponse.data.access_token;
      if (!accessToken) {
        throw new Error('No access token received');
      }

      // Step 2: Use access token to get session cookie
      const cookieResponse = await axios.get(
        `${this.config.baseUrls.orderWeb}/order-web/user/current`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'User-Agent': 'Mozilla/5.0 (compatible; MCP-Server/1.0)'
          },
          maxRedirects: 0,
          validateStatus: (status) => status === 302 || status === 200
        }
      );

      const sessionCookie = this.extractSessionCookie(cookieResponse.headers['set-cookie'] || []);
      if (sessionCookie) {
        console.log('✅ [FullyDynamicCookieManager] Service account authentication successful');
        return sessionCookie;
      }

      return null;
    } catch (error) {
      console.log('⚠️ [FullyDynamicCookieManager] Service account authentication failed:', error);
      return null;
    }
  }

  /**
   * Method 2: User Credential Authentication
   * Uses username/password to authenticate and get session
   */
  private async tryUserCredentialAuth(): Promise<string | null> {
    try {
      const credentials = this.config.credentials as { username: string; password: string };

      // Step 1: Get initial JSESSIONID by accessing auth service
      const initialResponse = await axios.get(
        `${this.config.baseUrls.authService}/prm-auth/auth/toLogin?clientId=${this.config.clientId}&redirectUrl=${encodeURIComponent(this.config.redirectUrl)}&state=${this.generateState()}`,
        {
          maxRedirects: 0,
          validateStatus: (status) => status === 302 || status === 200
        }
      );

      const jsessionId = this.extractJSessionId(initialResponse.headers['set-cookie'] || []);
      if (!jsessionId) {
        throw new Error('Could not obtain JSESSIONID');
      }

      // Step 2: Authenticate with Keycloak using username/password
      const keycloakAuthUrl = initialResponse.headers.location;
      if (!keycloakAuthUrl) {
        throw new Error('No Keycloak auth URL received');
      }

      // Get Keycloak login form
      const loginFormResponse = await axios.get(keycloakAuthUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; MCP-Server/1.0)'
        }
      });

      // Extract form action and session cookies
      const formAction = this.extractFormAction(loginFormResponse.data);
      const keycloakCookies = this.extractKeycloakCookies(loginFormResponse.headers['set-cookie'] || []);

      // Submit login form
      const loginResponse = await axios.post(formAction, new URLSearchParams({
        username: credentials.username,
        password: credentials.password,
        credentialId: ''
      }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cookie': this.buildKeycloakCookieString(keycloakCookies),
          'User-Agent': 'Mozilla/5.0 (compatible; MCP-Server/1.0)'
        },
        maxRedirects: 5,
        validateStatus: (status) => status < 400
      });

      // Extract pm_user_token from final URL
      const finalUrl = loginResponse.request.res.responseUrl || loginResponse.config.url;
      const pmUserToken = this.extractPmUserToken(finalUrl);
      if (!pmUserToken) {
        throw new Error('Could not obtain pm_user_token');
      }

      // Step 3: Exchange pm_user_token for session cookie
      const sessionCookie = await this.exchangePmUserTokenForCookie(pmUserToken);
      if (sessionCookie) {
        console.log('✅ [FullyDynamicCookieManager] User credential authentication successful');
        return sessionCookie;
      }

      return null;
    } catch (error) {
      console.log('⚠️ [FullyDynamicCookieManager] User credential authentication failed:', error);
      return null;
    }
  }

  /**
   * Method 3: Anonymous Session Creation
   * Creates anonymous session and tries to get basic access
   */
  private async tryAnonymousSessionCreation(): Promise<string | null> {
    try {
      // Step 1: Create anonymous session by accessing public endpoints
      const publicResponse = await axios.get(
        `${this.config.baseUrls.authService}/prm-auth/health`,
        {
          headers: {
            'User-Agent': 'Mozilla/5.0 (compatible; MCP-Server/1.0)'
          }
        }
      );

      const jsessionId = this.extractJSessionId(publicResponse.headers['set-cookie'] || []);
      if (!jsessionId) {
        throw new Error('Could not create anonymous session');
      }

      // Step 2: Try to use anonymous session for limited access
      const limitedAccessResponse = await axios.get(
        `${this.config.baseUrls.orderWeb}/order-web/public/health`,
        {
          headers: {
            'Cookie': `JSESSIONID=${jsessionId}`,
            'User-Agent': 'Mozilla/5.0 (compatible; MCP-Server/1.0)'
          },
          maxRedirects: 0,
          validateStatus: (status) => status === 302 || status === 200
        }
      );

      const sessionCookie = this.extractSessionCookie(limitedAccessResponse.headers['set-cookie'] || []);
      if (sessionCookie) {
        console.log('✅ [FullyDynamicCookieManager] Anonymous session creation successful');
        return sessionCookie;
      }

      return null;
    } catch (error) {
      console.log('⚠️ [FullyDynamicCookieManager] Anonymous session creation failed:', error);
      return null;
    }
  }

  /**
   * Helper methods for credential checking
   */
  private hasServiceAccountCredentials(): boolean {
    return this.config.credentials !== null && 
           'serviceAccountId' in this.config.credentials &&
           'serviceAccountSecret' in this.config.credentials;
  }

  private hasUserCredentials(): boolean {
    return this.config.credentials !== null && 
           'username' in this.config.credentials &&
           'password' in this.config.credentials;
  }

  /**
   * Extract JSESSIONID from Set-Cookie headers
   */
  private extractJSessionId(setCookieHeaders: string[]): string | null {
    for (const header of setCookieHeaders) {
      const match = header.match(/JSESSIONID=([^;]+)/);
      if (match) {
        return match[1];
      }
    }
    return null;
  }

  /**
   * Extract Keycloak cookies from Set-Cookie headers
   */
  private extractKeycloakCookies(setCookieHeaders: string[]): any {
    const cookies: any = {};
    for (const header of setCookieHeaders) {
      if (header.includes('AUTH_SESSION_ID=')) {
        const match = header.match(/AUTH_SESSION_ID=([^;]+)/);
        if (match) cookies.authSessionId = match[1];
      }
      if (header.includes('KEYCLOAK_IDENTITY=')) {
        const match = header.match(/KEYCLOAK_IDENTITY=([^;]+)/);
        if (match) cookies.keycloakIdentity = match[1];
      }
      if (header.includes('KEYCLOAK_SESSION=')) {
        const match = header.match(/KEYCLOAK_SESSION=([^;]+)/);
        if (match) cookies.keycloakSession = match[1];
      }
    }
    return cookies;
  }

  /**
   * Extract form action from HTML
   */
  private extractFormAction(html: string): string {
    const match = html.match(/<form[^>]+action="([^"]+)"/);
    return match ? match[1] : '';
  }

  /**
   * Extract pm_user_token from URL
   */
  private extractPmUserToken(url: string): string | null {
    try {
      const urlObj = new URL(url);
      return urlObj.searchParams.get('pm_user_token');
    } catch {
      return null;
    }
  }

  /**
   * Build Keycloak cookie string
   */
  private buildKeycloakCookieString(cookies: any): string {
    const parts = [];
    if (cookies.authSessionId) parts.push(`AUTH_SESSION_ID=${cookies.authSessionId}`);
    if (cookies.keycloakIdentity) parts.push(`KEYCLOAK_IDENTITY=${cookies.keycloakIdentity}`);
    if (cookies.keycloakSession) parts.push(`KEYCLOAK_SESSION=${cookies.keycloakSession}`);
    return parts.join('; ');
  }

  /**
   * Exchange pm_user_token for session cookie
   */
  private async exchangePmUserTokenForCookie(pmUserToken: string): Promise<string> {
    const exchangeUrl = `${this.config.baseUrls.orderWeb}/order-web/user/current`;
    const params = new URLSearchParams({
      pm_user_token: pmUserToken,
      biz_origin_url: 'https://admin.ingka-dt.cn/app/orders-portal/oms/index?sso=true'
    });

    const response = await axios.get(`${exchangeUrl}?${params}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; MCP-Server/1.0)'
      },
      maxRedirects: 0,
      validateStatus: (status) => status === 302
    });

    const setCookieHeaders = response.headers['set-cookie'];
    if (!setCookieHeaders) {
      throw new Error('No Set-Cookie headers in response');
    }

    const sessionCookie = this.extractSessionCookie(setCookieHeaders);
    if (!sessionCookie) {
      throw new Error('No test_orders-portal cookie found in response');
    }

    return sessionCookie;
  }

  /**
   * Extract session cookie from Set-Cookie headers
   */
  private extractSessionCookie(setCookieHeaders: string[]): string | null {
    for (const header of setCookieHeaders) {
      if (header.includes('test_orders-portal=')) {
        const match = header.match(/test_orders-portal=([^;]+)/);
        if (match) {
          return `test_orders-portal=${match[1]}`;
        }
      }
    }
    return null;
  }

  /**
   * Get cached cookie if valid
   */
  private getCachedCookie(): string | null {
    const entry = this.cookieCache.get('session_cookie');
    if (!entry) return null;

    if (entry.expiresAt <= Date.now()) {
      this.cookieCache.delete('session_cookie');
      return null;
    }

    return entry.cookie;
  }

  /**
   * Cache the session cookie
   */
  private cacheCookie(cookie: string): void {
    const ttl = 3600 * 1000; // 1 hour
    this.cookieCache.set('session_cookie', {
      cookie,
      expiresAt: Date.now() + ttl
    });
  }

  /**
   * Generate random state for OAuth2 flow
   */
  private generateState(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Clear all caches
   */
  clearCache(): void {
    this.cookieCache.clear();
    this.sessionState = null;
  }
}

/**
 * Create fully dynamic cookie configuration from environment
 */
export function createFullyDynamicCookieConfig(): FullyDynamicCookieConfig {
  const env = getEnvironmentConfig();
  
  // Determine credentials type
  let credentials = null;
  if (env.SERVICE_ACCOUNT_ID && env.SERVICE_ACCOUNT_SECRET) {
    credentials = {
      serviceAccountId: env.SERVICE_ACCOUNT_ID,
      serviceAccountSecret: env.SERVICE_ACCOUNT_SECRET
    };
  } else if (env.AUTH_USERNAME && env.AUTH_PASSWORD) {
    credentials = {
      username: env.AUTH_USERNAME,
      password: env.AUTH_PASSWORD
    };
  }

  return {
    enabled: env.FULLY_DYNAMIC_COOKIES_ENABLED || false,
    clientId: 'orders-portal',
    redirectUrl: 'https://fe-dev-i.ingka-dt.cn/order-web/user/current',
    baseUrls: {
      authService: 'https://api-dev-mpp-fe.ingka-dt.cn',
      keycloak: 'https://keycloak.ingka-dt.cn',
      orderWeb: 'https://fe-dev-i.ingka-dt.cn'
    },
    credentials
  };
}

/**
 * Global fully dynamic cookie manager instance
 */
export const globalFullyDynamicCookieManager = new FullyDynamicCookieManager(createFullyDynamicCookieConfig());
