/**
 * Project Name:permission-server
 * File Name:OrgUserRepository.java
 * Package Name:com.ikea.digital.permission.server.resource.domain.repository
 * Date:Jan 31, 202410:32:40 AM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.repository;

import java.util.Collection;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.ikea.digital.permission.server.resource.domain.mapper.OrgUserPOMapper;
import com.ikea.digital.permission.server.resource.domain.model.persistent.OrgUserPO;

/**
 * ClassName:OrgUserRepository <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jan 31, 2024 10:32:40 AM <br/>
 * <AUTHOR>	 
 */
@Repository
public class OrgUserRepository {
	
	@Autowired
	private OrgUserPOMapper mapper;
	
	public List<OrgUserPO> getByClientIdAndOrgIdIn(String clientId, List<Long> orgIds) {
		Assert.hasText(clientId, "clientId cannot be blank");
		Assert.notEmpty(orgIds, "orgIds cannot be null or empty");
		return mapper.selectByClientIdAndOrgIdIn(clientId, orgIds);
	}
	
	public List<OrgUserPO> getByClientIdAndOrgId(String clientId, Long orgId) {
		Assert.hasText(clientId, "clientId cannot be blank");
		Assert.notNull(orgId, "orgId cannot be null");
		OrgUserPO orgUser = new OrgUserPO();
		orgUser.setClientId(clientId);
		orgUser.setOrgId(orgId);
		return mapper.selectSelective(orgUser);
	}
	
	public List<OrgUserPO> getByClientIdAndUserEmailIn(String clientId, Collection<String> userEmails) {
		Assert.hasText(clientId, "clientId cannot be blank");
		Assert.notEmpty(userEmails, "userEmails cannot be null or empty");
		return mapper.selectSelectiveWithUserEmailIn(clientId, null, userEmails);
	}
	
	public List<OrgUserPO> getByClientIdAndOrgIdAndUserEmailIn(String clientId, Long orgId, Collection<String> userEmails) {
		Assert.hasText(clientId, "clientId cannot be blank");
		Assert.notNull(orgId, "orgId cannot be null");
		Assert.notEmpty(userEmails, "userEmails cannot be null or empty");
		return mapper.selectSelectiveWithUserEmailIn(clientId, orgId, userEmails);
	}
	
	public int deleteOrgUser(String clientId, Long orgId, Collection<String> userEmails) {
		Assert.hasText(clientId, "clientId cannot be blank");
		Assert.notNull(orgId, "orgId cannot be null");
		Assert.notEmpty(userEmails, "userEmails cannot be null or empty");
		return mapper.deleteOrgUser(clientId, orgId, userEmails);
	}
	
	public int insertList(List<OrgUserPO> orgUsers) {
		Assert.notEmpty(orgUsers, "orgUsers cannot be null or empty");
		return mapper.insertList(orgUsers);
	}

}