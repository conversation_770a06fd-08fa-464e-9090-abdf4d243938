package com.ikea.mas.permission.config.prop;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import org.springframework.boot.context.properties.ConfigurationProperties;

import lombok.Data;

@Data
@ConfigurationProperties(prefix = "permission.keycloak")
public class KeycloakIdpProperties {
	
	String idpHint;
	
	String keycloakScope;
	
	String keycloakAuthEndpoint;
	
	String keycloakInternalAuthEndpoint;
	
	private String idpLogoutUrl;
	
	public String getIdpLogoutUrl(String redirectUrl) {
		StringBuilder strBuilder = new StringBuilder(idpLogoutUrl)
				.append("?post_logout_redirect_uri=")
				.append(URLEncoder.encode(redirectUrl, StandardCharsets.UTF_8));
		return strBuilder.toString();
	}
	
}