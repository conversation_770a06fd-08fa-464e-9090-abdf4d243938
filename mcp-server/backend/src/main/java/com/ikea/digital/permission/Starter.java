package com.ikea.digital.permission;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.bind.annotation.CrossOrigin;

@EnableAsync
@SpringBootApplication(scanBasePackages = {"com.ikea.digital", "com.ikea.mas"})
@CrossOrigin
@EnableFeignClients(basePackages = {"com.ikea.digital", "com.ikea.mas"})
@MapperScan("com.ikea.digital.permission.server.resource.domain.mapper")
public class Starter {
	
	public static void main(String[] args) {
		SpringApplication.run(Starter.class, args);
	}
	
}
