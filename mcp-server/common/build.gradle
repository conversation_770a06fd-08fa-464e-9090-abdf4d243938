group = 'com.ikea.digital'
version = '1.0.0-SNAPSHOT'

bootJar.enabled = false

java {
    //withJavadocJar()
    withSourcesJar()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-aop'

    implementation 'org.apache.commons:commons-lang3'
    implementation 'org.apache.commons:commons-collections4'
    implementation 'org.apache.httpcomponents.client5:httpclient5'
    
    compileOnly 'org.springframework.boot:spring-boot-starter-web'
    compileOnly 'org.springframework.boot:spring-boot-starter-webflux'
    
    
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    
    api group: 'org.keycloak', name: 'keycloak-admin-client', version: '23.0.4'
    
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
}