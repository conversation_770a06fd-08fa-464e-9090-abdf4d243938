package com.ikea.digital.permission.server.resource.domain.mapper;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.ikea.digital.permission.server.resource.domain.model.persistent.OrgPO;

public interface OrgPOMapper {
	
    int deleteByPrimaryKey(Long id);
    
    int deleteWithIdIn(@Param("ids") List<Long> ids);

    long insert(OrgPO row);

    long insertSelective(OrgPO row);

    OrgPO selectByPrimaryKey(Long id);
    
    List<OrgPO> selectSelective(OrgPO org);
    
    List<OrgPO> selectByClientIdAndIdIn(@Param("clientId")String clientId, @Param("ids") List<Long> ids);

    List<OrgPO> selectByClientIdAndPathLikeOr(@Param("clientId")String clientId, 
    		@Param("pathList") Collection<String> pathList);
    
    int updateByPrimaryKeySelective(OrgPO row);

    int updateByPrimaryKey(OrgPO row);
    
	int updatePath(@Param("clientId") String clientId, @Param("oldPath") String oldPath, @Param("newPath") String newPath);

	List<OrgPO> selectByClientIdAndPathLike(@Param("clientId") String clientId,  @Param("path") String path);

}