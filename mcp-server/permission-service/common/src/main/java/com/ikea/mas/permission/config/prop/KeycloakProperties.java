package com.ikea.mas.permission.config.prop;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;

import lombok.Data;

/**
 * ClassName:KeycloakDeploymentConfig <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jan 29, 2024 1:27:22 PM <br/>
 * <AUTHOR>	 
 */
@ConfigurationProperties(prefix = "keycloak")
@Data
public class KeycloakProperties {
	
	private String realm;
    private String authServerUrl;
    private String tokenUri;
    private String logoutUrl;
    private String sslRequired;
    private String resource;
    private Map<String, String> credentials;
    private boolean useResourceRoleMappings;

}