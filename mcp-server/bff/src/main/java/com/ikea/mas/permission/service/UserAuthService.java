package com.ikea.mas.permission.service;

import java.net.URLDecoder;
//import java.io.UnsupportedEncodingException;
//import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
//import java.util.Objects;
import java.util.UUID;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.keycloak.representations.AccessTokenResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
//import com.ikea.mas.permission.common.BaseResponse;
import com.ikea.mas.permission.common.PmToken;
//import com.ikea.mas.permission.common.RespCodeEnum;
import com.ikea.mas.permission.common.Result;
import com.ikea.mas.permission.config.prop.PermissionServerProperties;
import com.ikea.mas.permission.config.prop.PermissionServerProperties.BizSystemInfo;
import com.ikea.mas.permission.dto.LoginCompleteDto;
import com.ikea.mas.permission.dto.StateCacheDto;
import com.ikea.mas.permission.dto.LoginCompleteDto.SsoSession;
import com.ikea.mas.permission.dto.SsoCacheDto;
//import com.ikea.mas.permission.service.feign.PmBackendClient;
import com.ikea.mas.permission.utils.RedisHelper;
import com.ikea.mas.permission.utils.RequestParamsUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class UserAuthService {
	
//	private static final String CHARSET = "utf-8";

    @Autowired
    private RedisHelper redisHelper;

    @Autowired
    private PermissionServerProperties pmServerConfig;

    @Autowired
    private KeycloakService keycloakService;
    
    @Autowired
    private MicrosoftAuthService microsoftAuthService;
    
//    @Autowired
//    private PmBackendClient pmBackendClient;
    
//    public BaseResponse<String> getLoginUrl(String redirectUrl, String referer, Boolean internalNet) {
//    	try {
//    		return pmBackendClient.getLoginUrl("admin_portal", redirectUrl, referer, 
//    			Objects.isNull(internalNet) ? false : internalNet);
//    	} catch (Exception e) {
//    		log.error("redirectUrl:{}, referer:{}", e);
//			return BaseResponse.error(RespCodeEnum.UNKNOW_ERROR);
//		}
//    }
    
//    public String createPmLoginUrl(String clientId, String redirectUrl, String referer, boolean internalNet) {
//        String state = UUID.randomUUID().toString();
//        Map<String,String> cacheMap = new HashMap<>();
//        cacheMap.put("clientId", clientId);
//        cacheMap.put("redirectUrl", redirectUrl);
//        cacheMap.put("referer", referer == null ? "" : referer);
//        cacheMap.put("internalNet", String.valueOf(internalNet));
//        redisHelper.hmset(state, cacheMap, pmServerConfig.getUrlTimeout());
//
//        Map<String, String> queryParams = new HashMap<>();
//        queryParams.put("clientId", clientId);
//        queryParams.put("redirectUrl", redirectUrl);
//        queryParams.put("state", state);
//        return buildUrl(pmServerConfig.getLoginUrl(), queryParams);
//    }
    
    public Result<String> createKeycloakLoginUrl(StateCacheDto stateDto, String clientId, String redirectUrl) {
		String loginUrl = keycloakService.generateKeycloakAuthEndpoinUrl(pmServerConfig.getLoginCompleteCallbackUrl(), stateDto.getState(),Boolean.valueOf(stateDto.getCache().get("internalNet")));
		return Result.ok(loginUrl);
    }
    
	public Result<LoginCompleteDto> loginComplete(String code, StateCacheDto stateDto, String redirectUrl, String sessionId) {
		AccessTokenResponse tokenRes;
        try {
        	tokenRes = keycloakService.getKeycloakAccessToken(code, redirectUrl, sessionId);
        } catch (Exception e) {
			log.error("state:" + stateDto.getState(), e);
			return Result.fail("get keycloak token error!");
		}
    	Map<String, String> stateMap = stateDto.getCache();
    	
    	DecodedJWT authJwt = JWT.decode(tokenRes.getIdToken());
    	String userId = authJwt.getClaim("sub").asString();
    	String clientId = stateMap.get("clientId");
    	
    	// 1、设置SSO登录会话
    	BizSystemInfo bizSys = pmServerConfig.getBizSystemByClientId(clientId);
    	
    	Long currMill = System.currentTimeMillis();
    	Integer ssoLoginTimeOut = pmServerConfig.getSsoLoginTimeOut();
    	Integer clientIdleTime = bizSys.getIdleTime();
    	
    	String ssoUUID = UUID.randomUUID().toString();
        Map<String,String> ssoValueMap = new HashMap<>();
        ssoValueMap.put(clientId, String.valueOf(currMill + clientIdleTime * 1000));
        ssoValueMap.put("kc_user_id", userId);
        // 需要比cookie时间略长,防止用户在cookie到期前静默登录,而在登录过程中ssoUUID却过期了
        redisHelper.hmset(ssoUUID,  ssoValueMap, ssoLoginTimeOut + pmServerConfig.getUrlTimeout() + 600);
        
        // 2、设置client会话
        String userTag;
		try {
			Integer clientSessionTimeOut = bizSys.getSessionTimeOut();
            PmToken pmToken = new PmToken(ssoUUID, userId, clientSessionTimeOut, currMill);
			userTag = pmToken.token();
			redisHelper.set(userTag, userId, clientSessionTimeOut);
		} catch (Exception e) {
			log.error("state: " + stateDto.getState(), e);
			return Result.fail("create pmToken error!");
		}
        
        // 3、return
        SsoSession ssoInfo = new SsoSession();
        ssoInfo.setValue(ssoUUID);
        ssoInfo.setTimeOut(ssoLoginTimeOut);
        LoginCompleteDto result = new LoginCompleteDto();

		String url = generateRedirectUrl(stateMap, clientId, userTag);
        result.setRedirectBizUrl(url);
        result.setSsoSession(ssoInfo);
        
        clearLoginCache(stateDto.getState());
        return Result.ok(result);
    }
	
    public Result<String> loginCompleteSilentFlow(StateCacheDto stateDto, SsoCacheDto ssoDto) {
    	Map<String,String> stateMap = stateDto.getCache();
    	// 1.添加至SSO登录会话中(或延长时间)
    	String clientId = stateMap.get("clientId");
    	BizSystemInfo bizSys = pmServerConfig.getBizSystemByClientId(clientId);
    	Integer clientIdleTime = bizSys.getIdleTime();
    	Integer clientSessionTimeOut = bizSys.getSessionTimeOut();
        redisHelper.hset(ssoDto.getSsoUUID(), clientId, 
        		String.valueOf(System.currentTimeMillis() + clientIdleTime * 1000),
        		ssoDto.getExpireTime());
        
        // 2.生成新的Token
        String userId = ssoDto.getCache().get("kc_user_id");
        if(StringUtils.isBlank(userId)) {
        	return Result.fail("state: " + stateDto.getState() + " 超过ssoLoginTimeOut时间!");
        }
        PmToken pmToken = new PmToken(ssoDto.getSsoUUID(), userId, clientSessionTimeOut, System.currentTimeMillis());
        String pmTokenStr = StringUtils.EMPTY;
		try {
			pmTokenStr = pmToken.token();
		} catch (Exception e) {
			log.error("创建token失败！", e);
			return Result.fail("创建token失败！");
		}
        redisHelper.set(pmTokenStr, userId, clientSessionTimeOut); 
        
        // 3.return
        String url = generateRedirectUrl(stateMap, clientId, pmTokenStr);
				
		// 4.登录成功，清除sate缓存
		clearLoginCache(stateDto.getState());
		return Result.ok(url.toString());
    }
    
	public boolean checkLoginStatus(String clientId, final Map<String, String> ssoMap) {
		if(MapUtils.isEmpty(ssoMap)) {
			return false;
		}
		String timeOutStr = ssoMap.get(clientId);
		if(timeOutStr == null) { // SSO已登录,只是第一次登录该client,走静默登录。
			return true;
		}
		Long timeOut = Long.parseLong(timeOutStr);
		// TODO 此处应该更具当前时间判断是否timeOut
		if(timeOut == -1) {
			return false;
		}
		return true;
	}
	
	public Result<String> thirdpartyLoginComplete(SsoCacheDto ssoDto, String code, StateCacheDto stateDto,
			String sessionState, String referer) {
		// 获取AAD token 以此验证code
		try {
			microsoftAuthService.getMicrosoftToken(code, stateDto.getState(), sessionState, referer);
		} catch (Exception e) {
			log.error("state:" + stateDto.getState(), e);
			return Result.fail("get microsoft token error!");
		}
		
		return loginCompleteSilentFlow(stateDto, ssoDto);
	}
    
	public void logout(String userCookie) {
		if(StringUtils.isNotBlank(userCookie)) {
			PmToken pmToken = PmToken.from(userCookie);
			redisHelper.delete(userCookie);
			redisHelper.delete(pmToken.getSsoUUID());
//			try {
//				pmBackendClient.logout(userCookie);
//			} catch(Exception e) {
//				log.warn("user:{} keycloak logout failed!", pmToken.getUserId());
//			}
			log.info("用户:{}登出成功！", pmToken.getUserId());
		}
		
	}
	
	/**
	 * check if the {@code url} is in allowed logout redirect urls list
	 * @param url redirect url after user logout sucess
	 * @return true or false
	 */
	@SuppressWarnings("deprecation")
	public boolean checkAllowedLogoutRedirectUrl(String url) {
		List<String> allowedLogoutURLs = pmServerConfig.getAllowedLogoutURLs();
		if(CollectionUtils.isEmpty(allowedLogoutURLs)) {
			return false;
		}
		String origin = RequestParamsUtil.fetchOrigin(URLDecoder.decode(url));
		if(StringUtils.isBlank(origin)) {
			return false;
		}
		return allowedLogoutURLs.stream().anyMatch(s -> s.equals(origin));
	}
	
	private void clearLoginCache(String state) {
    	redisHelper.delete(state);
    }

	private String generateRedirectUrl(Map<String, String> stateMap, String clientId, String pmTokenStr) {
		StringBuilder queryString = new StringBuilder()
		.append("pm_user_token=").append(pmTokenStr).append("&")
		.append("biz_origin_url=").append(stateMap.get("referer"));

		// url中的biz_origin_url在redirect到客户端后可能被篡改，需要加一个MD5
		String md5Key = pmServerConfig.getBizSystemByClientId(clientId).getSecret();
		String md5 = hmacString(queryString.toString(), md5Key);
		if(StringUtils.isNotBlank(md5)) {
			queryString.append("&").append("code=").append(md5);
		}

		return new StringBuilder(stateMap.get("redirectUrl"))
			.append("?").append(queryString).toString();
	}

	private String hmacString(String url, String md5Key) {
		try {
            Mac hmac = Mac.getInstance("HmacSHA256");
			SecretKeySpec secretKey = new SecretKeySpec(md5Key.getBytes("UTF-8"), "HmacSHA256");
            hmac.init(secretKey);
            byte[] hash = hmac.doFinal(url.getBytes("UTF-8"));
            return toHexString(hash);
        } catch (Exception e) {
            log.error("生成HMAC-SHA256摘要时出错", e);
        }
		return StringUtils.EMPTY;
	}

	/**
     * 将字节数组转换为十六进制字符串。
     *
     * @param bytes 要转换的字节数组
     * @return 转换后的十六进制字符串
     */
	private String toHexString(byte[] bytes) {
        StringBuilder hs = new StringBuilder();
        String stmp;
		for (byte b : bytes) {
            stmp = Integer.toHexString(b & 0xFF);
            if (stmp.length() == 1)
                hs.append('0');
            hs.append(stmp);
        }
        return hs.toString().toLowerCase();
    }
	
//	private String buildUrl(String url, Map<String, String> queryParam) {
//        return url + "?" + this.buildQueryParameters(queryParam);
//    }

//    private String buildQueryParameters(Map<String, String> params) {
//        if (MapUtils.isEmpty(params)) {
//            return StringUtils.EMPTY;
//        }
//
//        StringBuilder sb = new StringBuilder();
//        for (Map.Entry<String, String> entry : params.entrySet()) {
//            if (entry.getKey() == null) {
//                continue;
//            }
//            String value = entry.getValue() == null ? "" : entry.getValue();
//            try {
//                String encodedKey = URLEncoder.encode(entry.getKey(), CHARSET);
//                String encodedValue = URLEncoder.encode(value, CHARSET);
//                if (sb.length() > 0) {
//                    sb.append('&');
//                }
//                sb.append(encodedKey);
//                sb.append('=');
//                sb.append(encodedValue);
//            } catch (UnsupportedEncodingException e) {
//                throw new RuntimeException(e.getMessage(), e);
//            }
//        }
//
//        return sb.toString();
//    }

}
