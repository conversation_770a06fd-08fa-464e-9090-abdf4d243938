<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ikea.digital.permission.server.resource.domain.mapper.MenuPOMapper">
  <resultMap id="BaseResultMap" type="com.ikea.digital.permission.server.resource.domain.model.Menu">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="menu_type" jdbcType="SMALLINT" property="menuType" />
    <result column="parent_menu_id" jdbcType="BIGINT" property="parentMenuId" />
    <result column="sort_index" jdbcType="SMALLINT" property="sortIndex" />
    <result column="client_id" jdbcType="VARCHAR" property="clientId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_gmt" jdbcType="TIMESTAMP" property="createGmt" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="modify_gmt" jdbcType="TIMESTAMP" property="modifyGmt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, description, path, icon, menu_type, parent_menu_id, sort_index, client_id, 
    creator, create_gmt, modifier, modify_gmt
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from menu
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from menu
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.ikea.digital.permission.server.resource.domain.model.Menu"
    useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into menu (name, description, 
      path, icon, menu_type, 
      parent_menu_id, sort_index, client_id, 
      creator, create_gmt, modifier, 
      modify_gmt)
    values (#{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{path,jdbcType=VARCHAR}, #{icon,jdbcType=VARCHAR}, #{menuType,jdbcType=SMALLINT}, 
      #{parentMenuId,jdbcType=BIGINT}, #{sortIndex,jdbcType=SMALLINT}, #{clientId,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{createGmt,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, 
      #{modifyGmt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.ikea.digital.permission.server.resource.domain.model.Menu"
    useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into menu
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="path != null">
        path,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="menuType != null">
        menu_type,
      </if>
      <if test="parentMenuId != null">
        parent_menu_id,
      </if>
      <if test="sortIndex != null">
        sort_index,
      </if>
      <if test="clientId != null">
        client_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createGmt != null">
        create_gmt,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="modifyGmt != null">
        modify_gmt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="path != null">
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="menuType != null">
        #{menuType,jdbcType=SMALLINT},
      </if>
      <if test="parentMenuId != null">
        #{parentMenuId,jdbcType=BIGINT},
      </if>
      <if test="sortIndex != null">
        #{sortIndex,jdbcType=SMALLINT},
      </if>
      <if test="clientId != null">
        #{clientId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createGmt != null">
        #{createGmt,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyGmt != null">
        #{modifyGmt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ikea.digital.permission.server.resource.domain.model.Menu">
    update menu
    <set>
      <if test="name != null and name != ''">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null and description != ''">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="path != null and path != ''">
        path = #{path,jdbcType=VARCHAR},
      </if>
      <if test="icon != null and icon != ''">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="menuType != null">
        menu_type = #{menuType,jdbcType=SMALLINT},
      </if>
      <if test="parentMenuId != null">
        parent_menu_id = #{parentMenuId,jdbcType=BIGINT},
      </if>
      <if test="sortIndex != null">
        sort_index = #{sortIndex,jdbcType=SMALLINT},
      </if>
      <if test="clientId != null and clientId != ''">
        client_id = #{clientId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null and creator != ''">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createGmt != null">
        create_gmt = #{createGmt,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null and modifier != ''">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyGmt != null">
        modify_gmt = #{modifyGmt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ikea.digital.permission.server.resource.domain.model.Menu">
    update menu
    set name = #{name,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      path = #{path,jdbcType=VARCHAR},
      icon = #{icon,jdbcType=VARCHAR},
      menu_type = #{menuType,jdbcType=SMALLINT},
      parent_menu_id = #{parentMenuId,jdbcType=BIGINT},
      sort_index = #{sortIndex,jdbcType=SMALLINT},
      client_id = #{clientId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_gmt = #{createGmt,jdbcType=TIMESTAMP},
      modifier = #{modifier,jdbcType=VARCHAR},
      modify_gmt = #{modifyGmt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="selectSelective" parameterType="com.ikea.digital.permission.server.resource.domain.model.Menu" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from menu
    <where>
      <if test="name != null and name != ''">
        and name = #{name,jdbcType=VARCHAR}
      </if>
      <if test="description != null and description != ''">
        and description = #{description,jdbcType=VARCHAR}
      </if>
      <if test="menuType != null">
        and menu_type = #{menuType,jdbcType=SMALLINT}
      </if>
      <if test="parentMenuId != null">
        and parent_menu_id = #{parentMenuId,jdbcType=BIGINT}
      </if>
      <if test="sortIndex != null">
        and sort_index = #{sortIndex,jdbcType=SMALLINT}
      </if>
      <if test="clientId != null and clientId != ''">
        and client_id = #{clientId,jdbcType=VARCHAR}
      </if>
      <if test="path != null and path != ''">
        and path = #{path,jdbcType=VARCHAR}
      </if>
      <if test="icon != null and icon != ''">
        and icon = #{icon,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
  
  <select id="selectByClientIdAndIdsSelective" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from menu
    <where>
      <if test="clientId != null and clientId != ''">
        and client_id = #{clientId,jdbcType=VARCHAR}
      </if>
      <if test="ids != null">
        and id in 
        <foreach collection="ids" item = "id" separator="," open="(" close=")">
            #{id, jdbcType=BIGINT}
        </foreach>
      </if>
    </where>
  </select>
  
  <select id="selectByClientIdAndParentIds" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from menu
    <where>
      <if test="clientId != null and clientId != ''">
        and client_id = #{clientId,jdbcType=VARCHAR}
      </if>
      <if test="ids != null">
        and parent_menu_id in 
        <foreach collection="ids" item = "id" separator="," open="(" close=")">
            #{id, jdbcType=BIGINT}
        </foreach>
      </if>
    </where>
  </select>
  
  <delete id="deleteByClientIdAndIds">
    delete from menu
    <where>
      <if test="clientId != null and clientId != ''">
        and client_id = #{clientId,jdbcType=VARCHAR}
      </if>
      <if test="ids != null">
        and id in 
        <foreach collection="ids" item = "id" separator="," open="(" close=")">
            #{id, jdbcType=BIGINT}
        </foreach>
      </if>
    </where>
  </delete>
</mapper>