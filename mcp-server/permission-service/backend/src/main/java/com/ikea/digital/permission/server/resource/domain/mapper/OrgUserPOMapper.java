package com.ikea.digital.permission.server.resource.domain.mapper;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.ikea.digital.permission.server.resource.domain.model.persistent.OrgUserPO;

public interface OrgUserPOMapper {
	
    int deleteByPrimaryKey(Long id);

    long insert(OrgUserPO row);

    long insertSelective(OrgUserPO row);
    
    int insertList(@Param("orgUsers") List<OrgUserPO> orgUsers);

    OrgUserPO selectByPrimaryKey(Long id);
    
    List<OrgUserPO> selectByClientIdAndOrgIdIn(@Param("clientId") String clientId, 
    		@Param("orgIds") List<Long> orgIds);
    
    List<OrgUserPO> selectSelective(OrgUserPO orgUser);
    
    List<OrgUserPO> selectSelectiveWithUserEmailIn(@Param("clientId") String clientId, 
    		@Param("orgId") Long orgId, @Param("userEmails") Collection<String> userEmails);

    int updateByPrimaryKeySelective(OrgUserPO row);

    int updateByPrimaryKey(OrgUserPO row);
    
    int deleteOrgUser(@Param("clientId") String clientId, @Param("orgId") Long orgId,
    		@Param("userEmails") Collection<String> userEmail);
}