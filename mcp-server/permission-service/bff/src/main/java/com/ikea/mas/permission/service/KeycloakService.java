/**
 * Project Name:bff
 * File Name:KecloakService.java
 * Package Name:com.ikea.mas.permission.service
 * Date:May 3, 20242:27:16 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.mas.permission.service;

import org.keycloak.OAuth2Constants;
import org.keycloak.common.util.HostUtils;
import org.keycloak.common.util.KeycloakUriBuilder;
import org.keycloak.constants.AdapterConstants;
import org.keycloak.representations.AccessTokenResponse;
import org.keycloak.util.BasicAuthHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClient;

import com.ikea.mas.permission.config.prop.KeycloakIdpProperties;
import com.ikea.mas.permission.config.prop.KeycloakProperties;


/**
 * ClassName:KecloakService <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     May 3, 2024 2:27:16 PM <br/>
 * <AUTHOR>	 
 */
@Service
public class KeycloakService {
	
	@Autowired
    private KeycloakProperties keycloakConfig;
	
	@Autowired
	private KeycloakIdpProperties keycloakIdpConfig;
	
	@Autowired
    @Qualifier("kcAuthRestClient")
    private RestClient restClient;
	
	public static String generateIdpBorkerEndpointUrl(String idpBorkerEndpointUrl, String code, String state) {
        KeycloakUriBuilder redirectUriBuilder = KeycloakUriBuilder
                .fromUri(idpBorkerEndpointUrl)
                .queryParam(OAuth2Constants.STATE, state)
                .queryParam(OAuth2Constants.CODE, code);
        return redirectUriBuilder.buildAsString();
    }
	
	public String generateKeycloakAuthEndpoinUrl(String clientRedirectUrl, String state, boolean internalNet) {
        KeycloakUriBuilder redirectUriBuilder = KeycloakUriBuilder.fromUri(internalNet ? keycloakIdpConfig.getKeycloakInternalAuthEndpoint() : keycloakIdpConfig.getKeycloakAuthEndpoint()); 
        redirectUriBuilder.queryParam(OAuth2Constants.RESPONSE_TYPE, OAuth2Constants.CODE)
        		.queryParam(OAuth2Constants.CLIENT_ID, keycloakConfig.getResource())
                .queryParam(OAuth2Constants.REDIRECT_URI, clientRedirectUrl)
                .queryParam(OAuth2Constants.STATE, state)
                .queryParam(AdapterConstants.KC_IDP_HINT, keycloakIdpConfig.getIdpHint())
                .queryParam(OAuth2Constants.SCOPE, OAuth2Constants.SCOPE_OPENID)
                .queryParam("login", "true");
        return redirectUriBuilder.buildAsString();
    }
	
	public AccessTokenResponse getKeycloakAccessToken(String code, String redirectUrl, String sessionId) {
		HttpHeaders headers = new HttpHeaders();
    	headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    	String authorization = BasicAuthHelper.RFC6749.createHeader(keycloakConfig.getResource(), keycloakConfig.getCredentials().get("secret"));
    	
    	MultiValueMap<String, String> map = new LinkedMultiValueMap<String, String>();
    	map.add("grant_type", "authorization_code");
    	map.add("code", code);
    	map.add("redirect_uri", stripOauthParametersFromRedirect(redirectUrl));
    	if (sessionId != null) {
    		map.add("client_session_state", sessionId);
    		map.add("client_session_host", HostUtils.getHostName());
        }
    	
    	ResponseEntity<AccessTokenResponse> tokenRes = restClient.post().uri(keycloakConfig.getTokenUri())
    		.contentType(MediaType.APPLICATION_FORM_URLENCODED)
    		.header("Authorization", authorization)
    		.body(map)
    		.retrieve()
    		.toEntity(AccessTokenResponse.class);
    	return tokenRes.getBody();
	}
	
	private static String stripOauthParametersFromRedirect(String uri) {
   		Object[] nullObjs = null;
        KeycloakUriBuilder builder = KeycloakUriBuilder.fromUri(uri)
                .replaceQueryParam(OAuth2Constants.CODE, nullObjs)
                .replaceQueryParam(OAuth2Constants.STATE, nullObjs)
                .replaceQueryParam(OAuth2Constants.ISSUER, nullObjs);
        return builder.buildAsString();
    }

}