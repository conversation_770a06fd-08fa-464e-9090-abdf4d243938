/**
 * Project Name:permission-server
 * File Name:AuthValidator.java
 * Package Name:com.ikea.digital.permission.server.infrastructure.validator
 * Date:Dec 1, 20232:48:21 PM
 * Copyright (c) 2023, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.validator;

import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ikea.mas.permission.common.PmToken;
import com.ikea.mas.permission.utils.RedisHelper;

import lombok.extern.slf4j.Slf4j;

/**
 * ClassName:AuthValidator <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Dec 1, 2023 2:48:21 PM <br/>
 * <AUTHOR>	 
 */
@Component
@Slf4j
public class AuthValidator {
	
	@Autowired
	private RedisHelper redisOptHelper;
	
	/**
	 * 校验用户token是否合法，是否超时.<br/>
	 * @param userToken
	 * @param clientId
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public boolean checkToken(String userToken, String clientId) {
		
		PmToken pmToken = PmToken.from(userToken);
		if(Objects.isNull(pmToken)) {
			log.error("token invalid {}", userToken);
			return false;
		}
		
		String ssoKey = pmToken.getSsoUUID();
		Map<String,String> ssoValueMap = (Map<String, String>) redisOptHelper.hgetall(ssoKey);
		if(MapUtils.isEmpty(ssoValueMap) || !ssoValueMap.containsKey(clientId)) {
			log.warn("SSO未登录或登录超时,需要重新登录");
			return false;
		}
		
		Long expireTime = redisOptHelper.getExpire(ssoKey);
		if(expireTime <= 0) {
			log.warn("客户端会话超时,需要重新登录");
			return false;
		}
		
		String userId = redisOptHelper.get(userToken);
		if(StringUtils.isBlank(userId)) {
			redisOptHelper.hset(ssoKey, clientId, "-1", expireTime);
			log.warn("客户端会话超时,需要重新登录");
			return false;
		}
		Long clientTimeOut = Long.valueOf(ssoValueMap.get(clientId));
        long currTime = System.currentTimeMillis();
        if(currTime > clientTimeOut) {
        	redisOptHelper.hset(ssoKey, clientId, "-1", expireTime);
        	log.warn("长时间未操作,需要重新登录");
        	return false;
        }
        
        return true;
	}
	
	

}

