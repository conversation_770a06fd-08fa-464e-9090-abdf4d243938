#!/usr/bin/env node

/**
 * 🔄 OAuth2 Flow Mimic
 *
 * Purpose:
 * 1. Use existing JWT token and client credentials to mimic OAuth2 flow
 * 2. Get valid pm_user_token from permission service
 * 3. Test the resulting session cookie
 */

import axios from 'axios';
import { getEnvironmentConfig } from '../dist/utils/env.js';
import { decodeJWTUnsafe } from '../dist/utils/jwt-utils.js';
import fs from 'fs';
import path from 'path';

/**
 * Load configuration from token file and .env
 */
function loadConfiguration() {
  const env = getEnvironmentConfig();
  
  let tokenData = null;
  try {
    const tokenFile = path.join(process.cwd(), 'mcp-mpc-odi.token.json');
    const tokenContent = fs.readFileSync(tokenFile, 'utf8');
    tokenData = JSON.parse(tokenContent);
  } catch (error) {
    console.error('⚠️ Could not load token file:', error.message);
  }

  return {
    // From token file
    JWT_TOKEN: tokenData?.access_token || '',
    REFRESH_TOKEN: tokenData?.refresh_token || '',
    SESSION_STATE: tokenData?.session_state || '',
    TOKEN_DATA: tokenData,
    
    // From .env
    CLIENT_ID: env.OAUTH2_CLIENT_ID || 'mcp-mpc-odi',
    CLIENT_SECRET: env.OAUTH2_CLIENT_SECRET || '',
    KEYCLOAK_BASE_URL: env.KEYCLOAK_BASE_URL || 'https://keycloak.ingka-dt.cn/auth',
    KEYCLOAK_REALM: env.KEYCLOAK_REALM || 'master',
    
    // Permission service endpoints
    PERMISSION_SERVICE_BASE: 'https://api-dev-mpp-fe.ingka-dt.cn',
    ORDERS_API_BASE: 'https://fe-dev-i.ingka-dt.cn/order-web',
    
    // OAuth2 flow parameters
    REDIRECT_URI: 'https://api-dev-mpp-fe.ingka-dt.cn/prm-auth/auth/loginComplete',
    TARGET_CLIENT_ID: 'permission-service'
  };
}

const CONFIG = loadConfiguration();

/**
 * Step 1: Analyze existing JWT token
 */
function analyzeJWTToken() {
  console.log('🔍 ==================== JWT TOKEN ANALYSIS ====================');
  
  try {
    const payload = decodeJWTUnsafe(CONFIG.JWT_TOKEN);
    
    console.log('✅ JWT token analyzed successfully');
    console.log('📊 Token information:');
    console.log(`   User ID (sub): ${payload.sub}`);
    console.log(`   Session ID (sid): ${payload.sid}`);
    console.log(`   Client ID (azp): ${payload.azp}`);
    console.log(`   Email: ${payload.email || 'N/A'}`);
    console.log(`   Name: ${payload.name || 'N/A'}`);
    console.log(`   Issuer: ${payload.iss}`);
    console.log(`   Expires: ${new Date(payload.exp * 1000).toISOString()}`);
    console.log(`   Is Expired: ${new Date() > new Date(payload.exp * 1000) ? '❌ Yes' : '✅ No'}`);
    console.log(`   Session State: ${CONFIG.SESSION_STATE}`);
    
    return {
      userId: payload.sub,
      sessionId: payload.sid,
      clientId: payload.azp,
      email: payload.email,
      name: payload.name,
      issuer: payload.iss,
      expiresAt: new Date(payload.exp * 1000),
      isExpired: new Date() > new Date(payload.exp * 1000)
    };
  } catch (error) {
    console.error('❌ Failed to analyze JWT token:', error.message);
    return null;
  }
}

/**
 * Step 2: Try direct permission service authentication
 */
async function tryPermissionServiceAuth(sessionInfo) {
  console.log('\n🔄 ==================== PERMISSION SERVICE AUTH ====================');
  
  try {
    console.log('🔄 Trying permission service authentication...');
    console.log(`   Target: ${CONFIG.TARGET_CLIENT_ID}`);
    console.log(`   User: ${sessionInfo.userId}`);
    console.log(`   Session: ${sessionInfo.sessionId}`);
    
    // Try to get login URL first
    const loginUrlEndpoint = `${CONFIG.PERMISSION_SERVICE_BASE}/permission-service/user/auth/getLoginUrl`;
    
    console.log(`\n📋 Step 1: Get login URL`);
    console.log(`   Endpoint: ${loginUrlEndpoint}`);
    
    const loginResponse = await axios.get(loginUrlEndpoint, {
      headers: {
        'clientId': CONFIG.TARGET_CLIENT_ID,
        'Accept': 'application/json',
        'Authorization': `Bearer ${CONFIG.JWT_TOKEN}`,
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
      },
      params: {
        redirectUrl: 'https://fe-dev-i.ingka-dt.cn/order-web/user/current',
        referer: 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${loginResponse.status}`);
    console.log(`   Response data: ${JSON.stringify(loginResponse.data, null, 2)}`);
    
    if (loginResponse.data?.success && loginResponse.data?.data) {
      const loginUrl = loginResponse.data.data;
      console.log('✅ Got login URL from permission service');
      console.log(`   Login URL: ${loginUrl}`);
      
      // Extract state from login URL
      const urlParams = new URLSearchParams(loginUrl.split('?')[1]);
      const state = urlParams.get('state');
      
      return {
        success: true,
        loginUrl,
        state
      };
    } else {
      console.log('❌ Failed to get login URL - trying alternative approach');
      return { success: false };
    }
    
  } catch (error) {
    console.error('❌ Permission service auth failed:', error.message);
    return { success: false };
  }
}

/**
 * Step 3: Try Keycloak token exchange for permission-service
 */
async function tryKeycloakTokenExchange(sessionInfo) {
  console.log('\n🔄 ==================== KEYCLOAK TOKEN EXCHANGE ====================');
  
  try {
    console.log('🔄 Trying Keycloak token exchange...');
    console.log(`   Source client: ${CONFIG.CLIENT_ID}`);
    console.log(`   Target client: ${CONFIG.TARGET_CLIENT_ID}`);
    console.log(`   Using existing JWT token`);
    
    const tokenEndpoint = `${CONFIG.KEYCLOAK_BASE_URL}/realms/${CONFIG.KEYCLOAK_REALM}/protocol/openid-connect/token`;
    
    console.log(`   Token endpoint: ${tokenEndpoint}`);
    
    // Try token exchange
    const params = new URLSearchParams({
      grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',
      subject_token: CONFIG.JWT_TOKEN,
      subject_token_type: 'urn:ietf:params:oauth:token-type:access_token',
      client_id: CONFIG.CLIENT_ID,
      client_secret: CONFIG.CLIENT_SECRET,
      audience: CONFIG.TARGET_CLIENT_ID,
      requested_token_type: 'urn:ietf:params:oauth:token-type:access_token'
    });

    const response = await axios.post(tokenEndpoint, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      timeout: 10000,
      validateStatus: () => true
    });

    console.log(`   Response status: ${response.status}`);
    console.log(`   Response data: ${JSON.stringify(response.data, null, 2)}`);

    if (response.data.access_token) {
      console.log('✅ Token exchange successful!');
      console.log(`   New token expires in: ${response.data.expires_in}s`);
      console.log(`   New token preview: ${response.data.access_token.substring(0, 50)}...`);
      
      return {
        success: true,
        accessToken: response.data.access_token,
        expiresIn: response.data.expires_in
      };
    } else {
      console.log('❌ Token exchange failed');
      return { success: false };
    }
    
  } catch (error) {
    console.error('❌ Keycloak token exchange failed:', error.message);
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Data: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    return { success: false };
  }
}

/**
 * Step 4: Try to simulate the loginComplete flow
 */
async function simulateLoginComplete(sessionInfo, exchangeResult) {
  console.log('\n🔄 ==================== SIMULATE LOGIN COMPLETE ====================');
  
  try {
    console.log('🔄 Simulating loginComplete flow...');
    
    // Generate a fake authorization code (this is the tricky part)
    const fakeCode = `${sessionInfo.sessionId}.${sessionInfo.userId}.${Date.now()}`;
    const state = `fake-state-${Date.now()}`;
    
    console.log(`   Fake authorization code: ${fakeCode.substring(0, 50)}...`);
    console.log(`   State: ${state}`);
    
    // Try to call loginComplete endpoint
    const loginCompleteUrl = `${CONFIG.PERMISSION_SERVICE_BASE}/prm-auth/auth/loginComplete`;
    
    const params = new URLSearchParams({
      state: state,
      session_state: sessionInfo.sessionId,
      iss: sessionInfo.issuer,
      code: fakeCode
    });
    
    const fullUrl = `${loginCompleteUrl}?${params.toString()}`;
    console.log(`   Login complete URL: ${fullUrl}`);
    
    const response = await axios.get(fullUrl, {
      headers: {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'cache-control': 'no-cache',
        'dnt': '1',
        'pragma': 'no-cache',
        'priority': 'u=0, i',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-site',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
      },
      maxRedirects: 0,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    
    if (response.status === 302 && response.headers.location) {
      const redirectUrl = response.headers.location;
      console.log('✅ Got redirect from loginComplete');
      console.log(`   Redirect URL: ${redirectUrl}`);
      
      // Check if redirect contains pm_user_token
      if (redirectUrl.includes('pm_user_token=')) {
        const urlParams = new URLSearchParams(redirectUrl.split('?')[1]);
        const pmUserToken = urlParams.get('pm_user_token');
        
        if (pmUserToken) {
          console.log('🎉 SUCCESS! Got pm_user_token from loginComplete!');
          console.log(`   pm_user_token: ${pmUserToken}`);
          
          return {
            success: true,
            pmUserToken,
            redirectUrl
          };
        }
      }
    }
    
    console.log('❌ LoginComplete simulation failed');
    if (response.data) {
      console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
    }
    
    return { success: false };
    
  } catch (error) {
    console.error('❌ LoginComplete simulation failed:', error.message);
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Data: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    return { success: false };
  }
}

/**
 * Step 5: Test the resulting pm_user_token
 */
async function testPmUserToken(pmUserToken) {
  console.log('\n🧪 ==================== TEST PM_USER_TOKEN ====================');
  
  if (!pmUserToken) {
    console.log('❌ No pm_user_token to test');
    return false;
  }
  
  try {
    console.log('🔄 Testing pm_user_token with orders API...');
    console.log(`   Token: ${pmUserToken.substring(0, 50)}...`);
    
    const testUrl = `${CONFIG.ORDERS_API_BASE}/orders/search`;
    const requestData = {
      "storeIds": [],
      "baseSize": 10,
      "page": 1,
      "size": 10,
      "timestamp": Date.now()
    };
    
    const response = await axios.post(testUrl, requestData, {
      headers: {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'Cookie': `test_orders-portal=${pmUserToken}`,
        'dnt': '1',
        'origin': 'https://admin.ingka-dt.cn',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://admin.ingka-dt.cn/',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'x-custom-referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    
    if (response.status === 200 && response.data && response.data.code !== 401) {
      console.log('✅ pm_user_token works!');
      console.log('   API returned successful response');
      if (response.data.data) {
        console.log(`   Data preview: ${JSON.stringify(response.data.data, null, 2).substring(0, 200)}...`);
      }
      return true;
    } else {
      console.log('❌ pm_user_token failed');
      console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
      return false;
    }
    
  } catch (error) {
    console.error('❌ pm_user_token test failed:', error.message);
    return false;
  }
}

/**
 * Main function
 */
async function mimicOAuth2Flow() {
  console.log('🔄 ==================== OAUTH2 FLOW MIMIC ====================');
  console.log('🎯 Purpose: Use existing JWT token to mimic OAuth2 flow and get pm_user_token\n');
  
  console.log('📋 Configuration:');
  console.log(`   Token File: ${CONFIG.TOKEN_DATA ? 'Loaded ✅' : 'Missing ❌'}`);
  console.log(`   Client ID: ${CONFIG.CLIENT_ID}`);
  console.log(`   Client Secret: ${CONFIG.CLIENT_SECRET ? 'Configured ✅' : 'Missing ❌'}`);
  console.log(`   JWT Token: ${CONFIG.JWT_TOKEN ? CONFIG.JWT_TOKEN.substring(0, 50) + '...' : 'Not available ❌'}`);
  console.log(`   Target Client: ${CONFIG.TARGET_CLIENT_ID}`);
  console.log('');
  
  if (!CONFIG.JWT_TOKEN || !CONFIG.CLIENT_SECRET) {
    console.log('❌ Missing required configuration');
    console.log('   Make sure mcp-mpc-odi.token.json and .env are properly configured');
    return;
  }
  
  // Step 1: Analyze JWT token
  const sessionInfo = analyzeJWTToken();
  if (!sessionInfo || sessionInfo.isExpired) {
    console.log('❌ JWT token is invalid or expired');
    return;
  }
  
  // Step 2: Try permission service auth
  const permissionAuth = await tryPermissionServiceAuth(sessionInfo);
  
  // Step 3: Try Keycloak token exchange
  const exchangeResult = await tryKeycloakTokenExchange(sessionInfo);
  
  // Step 4: Try to simulate loginComplete
  const loginResult = await simulateLoginComplete(sessionInfo, exchangeResult);
  
  // Step 5: Test the pm_user_token
  const tokenWorks = await testPmUserToken(loginResult.pmUserToken);
  
  console.log('\n🏁 ==================== OAUTH2 FLOW MIMIC COMPLETE ====================');
  console.log('✅ OAuth2 flow mimic finished');
  console.log('');
  console.log('🎯 **Results Summary:**');
  console.log(`   JWT Analysis: ${sessionInfo ? 'Success ✅' : 'Failed ❌'}`);
  console.log(`   Permission Auth: ${permissionAuth.success ? 'Success ✅' : 'Failed ❌'}`);
  console.log(`   Token Exchange: ${exchangeResult.success ? 'Success ✅' : 'Failed ❌'}`);
  console.log(`   Login Complete: ${loginResult.success ? 'Success ✅' : 'Failed ❌'}`);
  console.log(`   Token Test: ${tokenWorks ? 'Success ✅' : 'Failed ❌'}`);
  console.log('');
  
  if (loginResult.success && tokenWorks) {
    console.log('🎉 **SUCCESS! OAuth2 flow mimic worked!**');
    console.log(`   pm_user_token: ${loginResult.pmUserToken}`);
    console.log('   You can now use this token in your MCP server');
  } else {
    console.log('⚠️ **OAuth2 flow mimic partially successful**');
    console.log('   Some steps worked, but full flow needs refinement');
    console.log('   Consider using browser token extraction as fallback');
  }
}

// Run the OAuth2 flow mimic if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  mimicOAuth2Flow().catch(error => {
    console.error('💥 OAuth2 flow mimic failed:', error);
    process.exit(1);
  });
}

export { mimicOAuth2Flow };
