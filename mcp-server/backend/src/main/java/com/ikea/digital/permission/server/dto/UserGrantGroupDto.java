package com.ikea.digital.permission.server.dto;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.ikea.digital.permission.server.validator.Validatable;

import lombok.Data;

@Data
public class UserGrantGroupDto implements Validatable {
	
	String userId;
	
	List<String> groupIds;
	
	@Override
	public void validate() {
		if(CollectionUtils.isEmpty(groupIds)) {
			return;
		}
//		UserContext userContext = UserHolder.getUserInfo();
//		List<String> groups = userContext.getGroups();
//		if(CollectionUtils.isEmpty(groups) || !groups.containsAll(groupIds)) {
//			throw new ResourceException("您无分配相关组的权限");
//		}
	}
	
}
