package com.ikea.digital.permission.core.context;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.ikea.digital.permission.dto.ResourceDto;

import lombok.Data;

@Data
public class UserContext {

	private String userId;
	private String name;
	private String email;
	private String networkId;
	private String avatar;

	private List<String> storeIds;
	private List<String> groups;

	private Map<String, List<String>> roleMap; // K:clientId V:roleNames
	private Map<String, List<String>> roleStoreMap; // K:roleName V:storeIds
	private Map<String, List<ResourceDto>> resourceMap; // K:clientId V:rsDtos
	
	/**
	 * List of organization IDs to which the user belongs
	 */
	private List<String> orgIdList;
	
	// ============================= has authority ================================
	public final boolean hasAuthority(String authority) {
		return hasAnyAuthority(authority);
	}

	public final boolean hasAnyAuthority(String... authorities) {
		if (MapUtils.isEmpty(this.resourceMap)) {
			return false;
		}

		if (authorities == null || authorities.length == 0) {
			return true;
		}

		List<String> resource = this.flatResourceMap().stream().map(ResourceDto::getName).collect(Collectors.toList());

		return !Collections.disjoint(resource, Arrays.asList(authorities));
	}

	@SuppressWarnings("unchecked")
	public final boolean hasAuthority(String authority, Object storeId) {
		List<String> storeIds = Collections.emptyList();
		if (storeId instanceof String) {
			storeIds = Arrays.asList(((String) storeId).split(","));
		}

		if (storeId instanceof List) {
			storeIds = (List<String>) storeId;
		}

		return this.hasAuthority(authority, storeIds);
	}
	
	
	private final boolean hasAuthority(String authority, List<String> storeId) {
		if (MapUtils.isEmpty(this.resourceMap)) {
			return false;
		}

		List<String> storeIds = this.flatResourceMap().stream().filter(rs -> rs.getName().equals(authority)).map(ResourceDto::getStoreIds).findFirst().orElse(null);
		if (CollectionUtils.isEmpty(storeIds)) {
			return false;
		}
		
		return storeIds.containsAll(storeId);
	}
	

//	private final boolean hasAnyAuthority(String authority, List<String> storeId) {
//		if (MapUtils.isEmpty(this.resourceMap)) {
//			return false;
//		}
//
//		List<String> storeIds = this.flatResourceMap().stream().filter(rs -> rs.getName().equals(authority)).map(ResourceDto::getStoreIds).findFirst().orElse(null);
//		if (CollectionUtils.isEmpty(storeIds)) {
//			return false;
//		}
//
//		return !Collections.disjoint(storeIds, storeId);
//	}

	public final List<String> getStoreIdsWithAuthorities(String authority) {
		if (StringUtils.isEmpty(authority)) {
			return null;
		}

		return this.flatResourceMap().stream().filter(rs -> rs.getName().equals(authority)).map(ResourceDto::getStoreIds).findFirst().orElse(null);
	}

	private List<ResourceDto> flatResourceMap() {
		List<ResourceDto> flatResourceList = new ArrayList<>();
		resourceMap.values().stream().flatMap(List::stream).forEach(topResource -> {
			flatResourceList.addAll(this.flatResource(topResource));
		});
		return flatResourceList;
	}

	private List<ResourceDto> flatResource(ResourceDto topResource) {
		List<ResourceDto> subRsList = topResource.getSubResource();
		if (CollectionUtils.isEmpty(subRsList)) {
			return Collections.singletonList(topResource);
		}
		List<ResourceDto> result = new ArrayList<>();
		result.add(topResource);
		subRsList.stream().forEach(subRs -> {
			result.addAll(this.flatResource(subRs));
		});
		return result;
	}

	// ============================= has role ================================
	public final boolean hasRole(String role) {
		return hasAnyRole(role);
	}

	public final boolean hasAnyRole(String... needRoles) {
		if (MapUtils.isEmpty(this.roleMap)) {
			return false;
		}
		
		if (needRoles == null || needRoles.length == 0) {
			return true;
		}

		List<String> roles = roleMap.values().stream().flatMap(List::stream).collect(Collectors.toList());

		return !Collections.disjoint(roles, Arrays.asList(needRoles));
	}


	public final boolean hasAnyRoleWithClient(List<String> roles, String clientId) {
		if (MapUtils.isEmpty(roleMap)) {
			return false;
		}
		
		if (CollectionUtils.isEmpty(roles)) {
			return true;
		}
		
		List<String> clientRoles = roleMap.get(clientId);
		if (CollectionUtils.isEmpty(clientRoles)) {
			return false;
		}

		return !Collections.disjoint(clientRoles, roles);
	}

}