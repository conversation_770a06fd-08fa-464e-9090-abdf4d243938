/**
 * Project Name:backend
 * File Name:ClientRepository.java
 * Package Name:com.ikea.digital.permission.server.resource.domain.repository
 * Date:Jun 14, 202411:33:01 AM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.repository;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.ikea.digital.permission.server.common.util.KeycloakManager;
import com.ikea.digital.permission.server.resource.domain.model.Client;

/**
 * ClassName:ClientRepository <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jun 14, 2024 11:33:01 AM <br/>
 * <AUTHOR>	 
 */
@Repository
public class ClientRepository {
	
	@Autowired
	private KeycloakManager keycloakManager;

	public List<Client> fetchClientList() {
		return keycloakManager.fetchClientList();
	}

	public String fetchClientPkIdByClientId(String clientId) {
		return keycloakManager.getClientPkId(clientId);
	}

}