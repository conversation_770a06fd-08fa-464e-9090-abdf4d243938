package com.ikea.digital.permission.server.controller;

import jakarta.validation.Valid;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ikea.digital.permission.core.config.PmPreAuthorize;
import com.ikea.digital.permission.server.common.BaseResponse;
import com.ikea.digital.permission.server.dto.GroupCreateOrUpdateDto;
import com.ikea.digital.permission.server.dto.UpdateValidGroup;
import com.ikea.digital.permission.server.service.GroupService;
import com.ikea.digital.permission.server.vo.GroupVo;

@RestController
@RequestMapping("/rs/group")
public class GroupController {
	
	@Autowired
	GroupService groupService;
	
	@PostMapping("/create")
	@PmPreAuthorize("hasAnyRoleWithClient('groupManager', 'permission-service')")
	public BaseResponse<?> createGroup(@RequestBody @Valid GroupCreateOrUpdateDto vo){
		groupService.createGroup(vo);
		return BaseResponse.success();
	}
	
	@PostMapping("/update")
	@PmPreAuthorize("hasAnyRoleWithClient('groupManager', 'permission-service')")
	public BaseResponse<?> updateGroup(@RequestBody @Validated(UpdateValidGroup.class) GroupCreateOrUpdateDto vo) {
		groupService.updateGroup(vo);
		return BaseResponse.success();
	}
	
	@GetMapping("/delete/{groupId}")
	@PmPreAuthorize("hasAnyRoleWithClient('deleteManager', 'permission-service')")
	public BaseResponse<?> deleteGroup(@PathVariable("groupId") String groupId) {
		groupService.deleteGroup(groupId);
		return BaseResponse.success();
	}
	
	@GetMapping("/list")
	@PmPreAuthorize("hasAnyRoleWithClient('groupManager', 'permission-service')")
	public BaseResponse<List<GroupVo>> groupList(){
		List<GroupVo> result = groupService.groupList();
		return BaseResponse.success(result);
	}
	
}
