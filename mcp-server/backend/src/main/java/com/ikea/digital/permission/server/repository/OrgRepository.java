/**
 * Project Name:permission-server
 * File Name:OrgRepository.java
 * Package Name:com.ikea.digital.permission.server.resource.domain.repository
 * Date:Jan 31, 202410:30:22 AM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.repository;

import java.util.Collection;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.ikea.digital.permission.server.resource.domain.mapper.OrgPOMapper;
import com.ikea.digital.permission.server.resource.domain.model.persistent.OrgPO;

/**
 * ClassName:OrgRepository <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jan 31, 2024 10:30:22 AM <br/>
 * <AUTHOR>	 
 */
@Repository
public class OrgRepository {
	
	@Autowired
	private OrgPOMapper orgMapper;
	
	public List<OrgPO> getByClientId(String clientId){
		Assert.hasText(clientId, "clientId cannot be blank");
		OrgPO org = new OrgPO();
		org.setClientId(clientId);
		return orgMapper.selectSelective(org);
	}
	
	public OrgPO getOneByClientIdAndName(String clientId, String name) {
		Assert.hasText(clientId, "clientId cannot be blank");
		Assert.hasText(name, "name cannot be blank");
		OrgPO org = new OrgPO();
		org.setClientId(clientId);
		org.setName(name);
		List<OrgPO> list = orgMapper.selectSelective(org);
		if(CollectionUtils.isNotEmpty(list)) {
			return list.get(0);
		}
		return null;
	}
	
	public OrgPO getRootOrgByClientId(String clientId) {
		Assert.hasText(clientId, "clientId cannot be blank");
		OrgPO org = new OrgPO();
		org.setClientId(clientId);
		org.setDeep(1);
		List<OrgPO> list = orgMapper.selectSelective(org);
		if(CollectionUtils.isNotEmpty(list)) {
			return list.get(0);
		}
		return null;
	}
	
	public OrgPO getByPrimaryKey(Long id) {
		Assert.notNull(id, "primaryKey cannot be null");
		return orgMapper.selectByPrimaryKey(id);
	}
	
	public List<OrgPO> getByClientIdAndPathLike(String clientId, String path) {
		Assert.hasText(clientId, "clientId cannot be blank");
		Assert.hasText(path, "path cannot be blank");
		return orgMapper.selectByClientIdAndPathLike(clientId, path);
	}
	
	public List<OrgPO> getByClientIdAndIds(String clientId, List<Long> ids) {
		Assert.hasText(clientId, "clientId cannot be blank");
		Assert.notEmpty(ids, "ids cannot be null or empty");
		return orgMapper.selectByClientIdAndIdIn(clientId, ids);
	}
	
	public List<OrgPO> getByClientIdAndPathLikeOr(String clientId, Collection<String> pathList) {
		Assert.hasText(clientId, "clientId cannot be blank");
		Assert.notEmpty(pathList, "pathList cannot be null or empty");
		return orgMapper.selectByClientIdAndPathLikeOr(clientId, pathList);
	}
	
	public long insert(OrgPO org) {
		Assert.notNull(org, "org cannot be null");
		return orgMapper.insert(org);
	}
	
	public int updatePath(String clientId, String oldPath, String newPath) {
		return orgMapper.updatePath(clientId, oldPath, newPath);
	}
	
	public int updateByPrimaryKeySelective(OrgPO org) {
		return orgMapper.updateByPrimaryKeySelective(org);
	}
	
	public int deleteWithIdIn(List<Long> ids) {
		Assert.notEmpty(ids, "ids cannot be null or empty");
		return orgMapper.deleteWithIdIn(ids);
	}

}

