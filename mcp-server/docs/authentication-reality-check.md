# Authentication Reality Check: JWT vs HTTP Sessions

This document clarifies the fundamental challenge with JWT-based cookie acquisition and provides realistic solutions.

## The Core Problem

### What JWT Tokens Contain:
```json
{
  "sub": "3d2e265c-f895-4e32-b3be-5a139fa856c1",  // User ID
  "sid": "7ab42bd9-bac2-480d-b375-7ba58de23239",  // Keycloak Session ID
  "iss": "https://keycloak.ingka-dt.cn/auth/realms/master",
  "exp": 1754213559,
  "azp": "orders-portal"
}
```

### What We Actually Need:
```
JSESSIONID=AFFF8C65A616C396F7292C4E25C65D57  // HTTP session for auth service
AUTH_SESSION_ID=7ab42bd9-bac2-480d-b375-7ba58de23239.keycloak-dev-857f5d85dc-zkl9k-27830
KEYCLOAK_IDENTITY=eyJhbGciOiJIUzUxMiIsInR5cCIgOiAiSldU...  // Different from your JWT!
KEYCLOAK_SESSION="master/3d2e265c-f895-4e32-b3be-5a139fa856c1/7ab42bd9-bac2-480d-b375-7ba58de23239"
```

## The Fundamental Gap

**JWT Session ID ≠ HTTP Session Cookies**

Your JWT contains:
- `sid`: `"7ab42bd9-bac2-480d-b375-7ba58de23239"` (Keycloak's internal session ID)

But we need:
- `AUTH_SESSION_ID`: `"7ab42bd9-bac2-480d-b375-7ba58de23239.keycloak-dev-857f5d85dc-zkl9k-27830"` (HTTP cookie format)
- `KEYCLOAK_IDENTITY`: A completely different JWT token used for HTTP sessions
- `JSESSIONID`: HTTP session ID for the auth service (unrelated to Keycloak session)

## Why This Happens

### Browser Authentication Flow:
```
1. User visits frontend → Redirected to Keycloak
2. User logs in → Keycloak creates:
   - Internal session (sid in JWT)
   - HTTP session cookies (for browser)
   - JWT token (for frontend app)
3. Frontend gets JWT, browser gets cookies
4. These are DIFFERENT artifacts from the same login!
```

### The Missing Bridge:
- **JWT**: Designed for stateless API authentication
- **HTTP Cookies**: Designed for stateful browser sessions
- **No standard way** to convert between them

## Realistic Solutions

### Solution 1: Use JWT Tokens Directly (Recommended)

**If your backend supports JWT authentication:**

```javascript
// Instead of trying to get cookies, use JWT directly
const response = await fetch('/api/orders', {
  headers: {
    'Authorization': `Bearer ${jwtToken}`,
    'Content-Type': 'application/json'
  }
});
```

**Modify your service adapter to prefer JWT:**
```typescript
// In service-adapter.ts - this is already implemented
if (userToken) {
  // Use JWT token directly instead of trying to get cookies
  return http({ url, method, data, useKong: true, token: userToken });
}
```

### Solution 2: Backend Token Exchange

**If your backend has a token exchange endpoint:**

```javascript
// Exchange JWT for session cookie
const cookieResponse = await fetch('/auth/jwt-to-cookie', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${jwtToken}`,
    'Content-Type': 'application/json'
  }
});

const sessionCookie = cookieResponse.headers.get('set-cookie');
```

### Solution 3: Parallel Authentication

**Keep both JWT and cookie flows separate:**

```javascript
// Frontend: Use JWT for API calls
const apiResponse = await fetch('/api/data', {
  headers: { 'Authorization': `Bearer ${jwtToken}` }
});

// MCP Server: Use separate cookie-based authentication
// (with dynamic or fully-dynamic cookie managers)
```

### Solution 4: Service Account for MCP

**Use service account credentials for MCP server:**

```bash
# .env configuration
FULLY_DYNAMIC_COOKIES_ENABLED=true
SERVICE_ACCOUNT_ID=mcp-server-service-account
SERVICE_ACCOUNT_SECRET=your-service-account-secret
```

This way:
- **Frontend**: Uses user JWT tokens
- **MCP Server**: Uses service account authentication
- **Both**: Access the same backend with appropriate permissions

## What Actually Works

### ✅ Working Approaches:

1. **JWT Direct Usage**: Pass JWT in Authorization header
2. **Service Account**: MCP server authenticates independently
3. **Manual Cookie Setup**: Extract cookies once, configure statically
4. **Backend Token Exchange**: If backend provides JWT→Cookie endpoint

### ❌ What Doesn't Work:

1. **JWT Session Reconstruction**: Can't reliably convert JWT session to HTTP cookies
2. **Cookie Synthesis**: Can't generate valid HTTP cookies from JWT claims
3. **Session Bridging**: No standard way to bridge JWT and HTTP sessions

## Recommended Architecture

### For Your Use Case:

```
Frontend (React/Vue/Angular)
├─ Uses: JWT tokens from Keycloak
├─ API Calls: Authorization: Bearer <jwt>
└─ MCP Calls: Authorization: Bearer <jwt>

MCP Server
├─ Receives: JWT tokens from frontend
├─ Validates: JWT signature and claims
├─ API Calls: Uses JWT directly OR service account
└─ Fallback: Static/dynamic cookies if needed

Backend Services
├─ Accepts: JWT tokens (preferred)
├─ Accepts: Session cookies (fallback)
└─ Returns: Same data regardless of auth method
```

## Implementation Priority

### 1. Try JWT Direct (Highest Priority)
```typescript
if (userToken) {
  // Use JWT token directly - most reliable
  return http({ url, method, data, useKong: true, token: userToken });
}
```

### 2. Service Account Fallback
```typescript
if (serviceAccountConfigured) {
  // Use service account for MCP server
  const serviceToken = await getServiceAccountToken();
  return http({ url, method, data, useKong: true, token: serviceToken });
}
```

### 3. Cookie Methods (Last Resort)
```typescript
// Only if JWT and service account don't work
const cookie = await getCookieFromSomeMethod();
return http({ url, method, data, useKong: true, cookies: cookie });
```

## Testing Your Backend

### Test if your backend accepts JWT tokens:

```bash
# Get JWT token from your frontend
JWT_TOKEN="your-jwt-token-here"

# Test direct JWT usage
curl -H "Authorization: Bearer $JWT_TOKEN" \
     -H "Content-Type: application/json" \
     "https://fe-dev-i.ingka-dt.cn/order-web/api/orders"

# If this works, you don't need cookies at all!
```

### Test service account approach:

```bash
# Get service account token
SERVICE_TOKEN=$(curl -X POST \
  "https://keycloak.ingka-dt.cn/auth/realms/master/protocol/openid-connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=client_credentials" \
  -d "client_id=your-service-account" \
  -d "client_secret=your-secret" \
  | jq -r '.access_token')

# Test service account access
curl -H "Authorization: Bearer $SERVICE_TOKEN" \
     "https://fe-dev-i.ingka-dt.cn/order-web/api/orders"
```

## Conclusion

**The Reality**: JWT tokens and HTTP session cookies are different authentication artifacts that can't be easily converted between each other.

**The Solution**: Use JWT tokens directly where possible, and use service accounts or manual cookie setup for cases where JWT doesn't work.

**Your Best Bet**: Since you already have JWT tokens from your frontend, try using them directly in your MCP requests. This is likely to work and eliminates the need for complex cookie acquisition altogether.

The authentication system I've built provides multiple fallback options, but the JWT-direct approach is probably what you actually need! 🎯
