package com.ikea.mas.permission.utils;

import java.util.Enumeration;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import jakarta.servlet.http.HttpServletRequest;

public class RequestParamsUtil {

	public static String fetchParam(HttpServletRequest request) {
		StringBuilder paramInfo = new StringBuilder("{");
		Enumeration<String> e = request.getParameterNames();
		while (e.hasMoreElements()) {
			String name = (String) e.nextElement();
			paramInfo.append(name).append(":").append(request.getParameter(name)).append(",");
		}
		return paramInfo.replace(paramInfo.length()-1, paramInfo.length(), "}").toString();
	}
	
	public static String fetchOrigin(String urlStr) {
		if(StringUtils.isBlank(urlStr)) {
			return StringUtils.EMPTY;
		}
		UriComponents url = UriComponentsBuilder.fromHttpUrl(urlStr).build();
		String query = url.getQuery();
		if(StringUtils.isBlank(query)) {
			return urlStr;
		}
		return urlStr.substring(0, urlStr.indexOf("?"));
	}
	
}