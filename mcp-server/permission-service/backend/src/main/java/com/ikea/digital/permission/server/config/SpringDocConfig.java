/**
 * Project Name:permission-server
 * File Name:SpringDocConfig.java
 * Package Name:com.ikea.digital.permission.server.common.config
 * Date:Mar 25, 20243:37:43 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;

/**
 * ClassName:SpringDocConfig <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Mar 25, 2024 3:37:43 PM <br/>
 * <AUTHOR>	 
 */
@Configuration
public class SpringDocConfig {
	

	@Bean
    public OpenAPI customOpenAPI(){
        return new OpenAPI()
                .info(new Info()
                        .title("permission service")
                        .version("1.0.0")
                        .description("cn-digital-hub Authorization and authentication system")
                        .contact(new Contact()
                                .name("cn-digital-engineering-platform-support")
                                .email("<EMAIL>")));
    }
}