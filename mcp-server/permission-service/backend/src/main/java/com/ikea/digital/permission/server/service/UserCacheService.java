/**
 * Project Name:permission-server
 * File Name:UserCacheService.java
 * Package Name:com.ikea.digital.permission.server.resource.domain.service
 * Date:Nov 15, 20235:05:45 PM
 * Copyright (c) 2023, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.service;

import java.text.MessageFormat;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ikea.digital.permission.core.config.PermissionClientConfig;
import com.ikea.digital.permission.server.common.util.JacksonUtils;
import com.ikea.digital.permission.server.common.util.KeycloakManager;
import com.ikea.digital.permission.server.vo.UserDetailVo;
import com.ikea.mas.permission.config.prop.PermissionServerProperties;
import com.ikea.mas.permission.utils.RedisHelper;

/**
 * ClassName:UserCacheService <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Nov 15, 2023 5:05:45 PM <br/>
 * <AUTHOR>	 
 */
@Service
public class UserCacheService {
	
	@Autowired
	private PermissionClientConfig pmClientConfig;
	
	@Autowired
	private PermissionServerProperties pmServerConfig;
	
	@Autowired
	RedisHelper redisOptHelper;
	
	@Autowired
	KeycloakManager keycloakManager;
	
	private static final String USER_CACHE_KEY = "pm:{0}:{1}";
	
	public void refreshSsoTime(String ssoKey, String clientId) {
		Long expireTime = redisOptHelper.getExpire(ssoKey);
		if(expireTime > 0) {
			// 刷新token缓存时间
			long currTime = System.currentTimeMillis();
	        redisOptHelper.hset(ssoKey, clientId, 
	        		String.valueOf(currTime + pmServerConfig.getBizSystemByClientId(clientId).getIdleTime() * 1000)
	        		, expireTime);
		}
	}
	
	/**
	 * 缓存中获取用户
	 * @param userId
	 * @param clientId
	 * @return 用户信息json字符串
	 */
	private String getUser(String userId, String clientId) {
		String cacheKey = MessageFormat.format(USER_CACHE_KEY, clientId, userId);
		return redisOptHelper.get(cacheKey);
	}
	
	public void cacheUser(UserDetailVo userDetail, String clientId) {
		String cacheKey = MessageFormat.format(USER_CACHE_KEY, clientId, userDetail.getUserId());
		redisOptHelper.set(cacheKey, JacksonUtils.object2JsonString(userDetail), pmServerConfig.getUserInfoCacheTime());
	}
	
	/**
	 * 清除用户缓存.<br/>
	 * @param userToken
	 * @param userId
	 */
	public void clearUserCache(String userToken, String userId) {
		// 老的缓存
		String clientId = pmClientConfig.getClientId();
		String userInfoCacheKey = userToken + "_" + clientId;
		redisOptHelper.delete(userInfoCacheKey);
		
		// 新的缓存
		String cacheKey = MessageFormat.format(USER_CACHE_KEY, clientId, userId);
		redisOptHelper.delete(cacheKey);
	}
	
	/**
	 * 清除用户缓存.<br/>
	 * @param userToken
	 * @param userId
	 */
	public void clearNewUserCache(String clientId, String userId) {
		// 新的缓存
		String cacheKey = MessageFormat.format(USER_CACHE_KEY, clientId, userId);
		redisOptHelper.delete(cacheKey);
	}
	
	/**
	 * 从缓存中获取用户信息
	 * @param userToken
	 * @param userId
	 * @param clientId
	 * @return {@link UserDetailVo}
	 */
	public UserDetailVo getUser(String userToken, String userId, String clientId) {
		// 从老的缓存中获取
//		String userInfoCacheKey = userToken + "_" + clientId;
//		String userInfoStr = redisOptHelper.get(userInfoCacheKey);
		// 从新的缓存中获取
		String userInfoStr = getUser(userId, clientId);
		if(StringUtils.isNotBlank(userInfoStr)) {
			return JacksonUtils.readValue(userInfoStr, UserDetailVo.class);
		}
		return null;
	}
	
//	@Async("cacheExecutor")
//	public void cacheUser(String  userId) {
//		log.info("cache keycloak userId:{}", userId);
//		User user = keycloakManager.getUserById(userId);
//		if(Objects.isNull(user)) {
//			log.warn("user:{} not found in keycloak.", userId);
//			return;
//		}
//		
//		long cacheTime = covertToSeconds(pmServerConfig.getKcUserCacheTime());
//		redisOptHelper.set(keycloakUserCacheKey(user.getUserId()), JacksonUtils.object2JsonString(user), cacheTime);
//	}

}