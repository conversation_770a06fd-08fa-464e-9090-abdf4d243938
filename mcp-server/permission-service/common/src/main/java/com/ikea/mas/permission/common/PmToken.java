/**
 * Project Name:permission-server
 * File Name:PmToken.java
 * Package Name:com.ikea.digital.permission.server.common
 * Date:Nov 30, 20235:52:05 PM
 * Copyright (c) 2023, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.mas.permission.common;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Base64;
import java.util.Objects;
import java.util.Properties;
import java.util.regex.Pattern;

import com.ikea.mas.permission.utils.PlaceholderHelpers;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * ClassName:PmToken <br/>
 * Function: Permission service 颁发的Access Token, 和permission service交互必须带上此token. <br/>
 * Date:     Nov 30, 2023 5:52:05 PM <br/>
 * <AUTHOR>	 
 */
@Data
@AllArgsConstructor
public class PmToken {
	
	private static final String TOKEN_FORMAT = "${ssoUUID}@${loginTime}.${userId}.${clientSessionTimeOut}";
	
	private static final String UUID_REGEXP = "[a-f0-9]{8}(-[a-f0-9]{4}){3}-[a-f0-9]{12}";
	
	private static final String TOKEN_REGEXP = UUID_REGEXP + "@\\d{13}\\." + UUID_REGEXP + "\\.\\d+";
	
	private String ssoUUID;
	
	private String userId;
	
	private Integer clientSessionTimeOut;
	
	private Long loginTime;

	public String token() throws Exception {
		Properties pro = toProperties(this);
		String tokenStr = PlaceholderHelpers.getSpring().replacePlaceholders(TOKEN_FORMAT, pro);
        return Base64.getEncoder().encodeToString(tokenStr.getBytes());
	}
	
	public static Properties toProperties(Object object) throws IntrospectionException, IllegalAccessException, IllegalArgumentException, InvocationTargetException {
		if(Objects.isNull(object)) return null;
		Properties pro = new Properties();
		BeanInfo beanInfo = Introspector.getBeanInfo(object.getClass());
		String key;
		Method getter;
		for(PropertyDescriptor pd : beanInfo.getPropertyDescriptors()) {
			key = pd.getName();
			if("class".equals(key)) continue;
			getter = pd.getReadMethod();
			Object value = Objects.nonNull(getter) ? getter.invoke(object) : null;
			if(Objects.nonNull(value)) {
				pro.setProperty(key, value.toString());
			}
		}
		return pro;
	}

	public static PmToken from(String userToken) {
		String token = new String(Base64.getDecoder().decode(userToken));
		if(!Pattern.matches(TOKEN_REGEXP, token)) {
			return null;
		}
		String[] arr = token.split("@|\\.");
		return new PmToken(arr[0], arr[2], Integer.valueOf(arr[3]), Long.valueOf(arr[1]));
	}

}