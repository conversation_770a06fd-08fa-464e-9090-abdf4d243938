package com.ikea.digital.permission.server.service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ikea.digital.permission.server.repository.MenuRepository;
import com.ikea.digital.permission.server.repository.RoleRepository;
import com.ikea.digital.permission.server.resource.domain.model.Menu;
import com.ikea.digital.permission.server.resource.domain.model.Role;
import com.ikea.digital.permission.server.vo.ResourceVo;
import com.ikea.digital.permission.server.vo.SimpleRoleVo;

@Service
public class MenuService {
	
	@Autowired
	private MenuRepository menuRepository;
	
	private Integer MENU_MAX_DEEP = 5;
	
	@Autowired
	private RoleRepository roleRepository;
	
	public List<SimpleRoleVo> listRoles(String clientId, Long menuId) {
		List<Role> roles = roleRepository.getRolesWithMenuId(clientId, menuId);
		if(CollectionUtils.isEmpty(roles)) {
			return Collections.emptyList();
		}
		return roles.stream().map(Role::toSimpleRoleVo).collect(Collectors.toList());
	}
	
	public List<ResourceVo> listMenu(String clientId) {
		// 0、查询
		Menu criteria = new Menu();
        criteria.setClientId(clientId);
        List<Menu> menuList = menuRepository.selectSelective(criteria);
        
		if(CollectionUtils.isEmpty(menuList)) {
			return Collections.emptyList();
		}
        
		// 1、接菜单树结构
        List<ResourceVo> menuDtoList = Menu.toResourceVo(menuList);
        List<ResourceVo> topMenuList = menuDtoList.stream().filter(m -> m.getParentMenuId() == null).sorted(Comparator.comparing(ResourceVo::getOrder)).collect(Collectors.toList());
        Map<Long, List<ResourceVo>> subMenuMap = menuDtoList.stream().filter(m -> m.getParentMenuId() != null).collect(Collectors.groupingBy(ResourceVo::getParentMenuId));
		for(ResourceVo topMenu : topMenuList) {
			List<ResourceVo> subMenuDtoList = this.buildMenuTree(topMenu.getId(), subMenuMap, 1);
			topMenu.setSubResource(subMenuDtoList);
		}
		return topMenuList;
	}
	
	private List<ResourceVo> buildMenuTree(Long parentId, Map<Long,List<ResourceVo>> subMenuMap,int deep) {
		if(deep >= MENU_MAX_DEEP) {
			return Collections.emptyList();
		}
		deep = deep++;
		
		List<ResourceVo> subMenuList = subMenuMap.get(parentId);
		if(CollectionUtils.isEmpty(subMenuList)) {
			return Collections.emptyList();
		}
		
		for(ResourceVo subMenu : subMenuList) {
			List<ResourceVo> nextSubMenuList = this.buildMenuTree(subMenu.getId(), subMenuMap, deep);
			subMenu.setSubResource(nextSubMenuList);
		}
		
		return subMenuList.stream().sorted(Comparator.comparing(ResourceVo::getOrder)).collect(Collectors.toList());
	}
	
	public List<ResourceVo> buildResourceTree(List<Long> menuIds) {
		// 0、查询
        List<Menu> menuList = menuRepository.selectByClientIdAndIdsSelective(StringUtils.EMPTY, menuIds);
        
		if(CollectionUtils.isEmpty(menuList)) {
			return Collections.emptyList();
		}
        
		// 1、接菜单树结构
        List<ResourceVo> resourceVoList = Menu.toResourceVo(menuList);
        List<ResourceVo> topResourceList = resourceVoList.stream().filter(m -> m.getParentMenuId() == null).sorted(Comparator.comparing(ResourceVo::getOrder)).collect(Collectors.toList());
        Map<Long, List<ResourceVo>> subResourceMap = resourceVoList.stream().filter(m -> m.getParentMenuId() != null).collect(Collectors.groupingBy(ResourceVo::getParentMenuId));
		for(ResourceVo topMenu : topResourceList) {
			List<ResourceVo> subMenuDtoList = this.buildResourceTree(topMenu.getId(), subResourceMap, 1);
			topMenu.setSubResource(subMenuDtoList);
		}
		
		return topResourceList;
	}
	
	private List<ResourceVo> buildResourceTree(Long parentId, final Map<Long,List<ResourceVo>> subResourceMap, int deep) {
		if(deep >= MENU_MAX_DEEP) {
			return Collections.emptyList();
		}
		deep = deep++;
		
		List<ResourceVo> subResourceList = subResourceMap.get(parentId);
		if(CollectionUtils.isEmpty(subResourceList)) {
			return Collections.emptyList();
		}
		
		for(ResourceVo subMenu : subResourceList) {
			List<ResourceVo> nextSubMenuList = this.buildResourceTree(subMenu.getId(), subResourceMap, deep);
			subMenu.setSubResource(nextSubMenuList);
		}
		
		return subResourceList.stream().sorted(Comparator.comparing(ResourceVo::getOrder)).collect(Collectors.toList());
	}
	
}