/**
 * Project Name:permission-server
 * File Name:ImportUser.java
 * Package Name:com.ikea.digital.permission.server.resource.domain.model
 * Date:Sep 25, 20236:00:51 PM
 * Copyright (c) 2023, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.dto;

import lombok.Data;

/**
 * ClassName:ImportUserDto <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Sep 25, 2023 6:00:51 PM <br/>
 * <AUTHOR>	 
 */
@Data
public class ImportUserDto {
	
	private String user;
	
	private String roles;
	
	private String storeIds;

}