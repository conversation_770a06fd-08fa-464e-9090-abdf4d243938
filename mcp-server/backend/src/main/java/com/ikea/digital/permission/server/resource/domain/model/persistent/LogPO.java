package com.ikea.digital.permission.server.resource.domain.model.persistent;

import java.util.Date;

import com.ikea.digital.permission.core.context.UserHolder;

import lombok.Data;

@Data
public class LogPO {

	Long id;

	Integer optType;

	String optObj;

	String optCmd;
	
	String oldData;

	String newData;

	String creator;

	Date createGmt;

	public LogPO(Integer optType, String optObj, String optCmd, String oldData, String newData) {
		this.optType = optType;
		this.optObj = optObj;
		this.optCmd = optCmd;
		this.oldData = oldData;
		this.newData = newData;
		this.creator = UserHolder.getUserInfo() == null ? "" : UserHolder.getUserInfo().getEmail();
		this.createGmt = new Date();
	}

}
