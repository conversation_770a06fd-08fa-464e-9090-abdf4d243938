package com.ikea.digital.permission.server.resource.domain.model.persistent;

import java.util.Date;

import com.ikea.digital.permission.core.context.UserHolder;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgUserPO {
	
	Long id;
	
	Long orgId;
	
	String userEmail;
	
	String clientId;
	
	String creator = UserHolder.getUserInfo() == null ? "" : UserHolder.getUserInfo().getEmail();
	
	Date createGmt = new Date();
	
}
