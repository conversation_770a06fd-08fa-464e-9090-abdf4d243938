package com.ikea.digital.permission.server.dto;

import java.util.List;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class RoleCreateOrUpdateDto extends BaseDto {
	
	@NotEmpty(message = "角色名不能为空！")
	@NotBlank(message = "角色名不能为空！")
	@Size(max = 50, min = 2, message = "角色名长度需在(2-50)之间")
	String name;
	
	@NotEmpty(message = "角色描述不能为空！")
	@NotBlank(message = "角色描述不能为空！")
	@Size(max = 50,message = "描述长度需在50字符内")
	String description;
	
	List<String> menuIds;
}