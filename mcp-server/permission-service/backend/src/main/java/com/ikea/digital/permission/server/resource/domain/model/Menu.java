package com.ikea.digital.permission.server.resource.domain.model;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.ikea.digital.permission.server.vo.ResourceVo;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class Menu extends BaseModel {
	
	Long id;
	
	String clientId;
	
	String name;
	
	String description;
	
	String path;
	
	String icon;
	
	Integer menuType;
	
	Long parentMenuId;
	
	Integer sortIndex;
	
	public static ResourceVo toResouceVo(Menu po) {
		if(po == null) {
			return null;
		}
		ResourceVo vo = new ResourceVo();
		vo.setId(po.getId());
		vo.setName(po.getName());
		vo.setDescription(po.getDescription());
		vo.setIcon(po.getIcon());
		vo.setOrder(po.getSortIndex());
		vo.setResourceType(po.getMenuType());
		vo.setParentMenuId(po.getParentMenuId());
		vo.setPath(po.getPath());
		vo.setClientId(po.getClientId());
		return vo;
	}
	
	public static List<ResourceVo> toResourceVo(List<Menu> poList) {
		if(CollectionUtils.isEmpty(poList)) {
			return null;
		}
		return poList.stream().map(Menu::toResouceVo).collect(Collectors.toList());
	}
}
