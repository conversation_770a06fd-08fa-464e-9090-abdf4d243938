/**
 * Project Name:permission-server
 * File Name:SimpleRoleVo.java
 * Package Name:com.ikea.digital.permission.server.resource.web.response
 * Date:Jan 4, 20245:10:07 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.vo;

import lombok.Data;

/**
 * ClassName:SimpleRoleVo <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jan 4, 2024 5:10:07 PM <br/>
 * <AUTHOR>	 
 */
@Data
public class SimpleRoleVo {
	
	private String name;
	
	private String description;
	
}