package com.ikea.mas.permission.config.prop;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.util.AntPathMatcher;

import com.ikea.mas.permission.exception.AuthException;

import lombok.Data;

@Data
@ConfigurationProperties(prefix = "permission.server")
public class PermissionServerProperties {
	
	private int urlTimeout;
	
	private int ssoLoginTimeOut;
	
	private String loginUrl;
	
	private String logoutUrl;
	
	private String loginCompleteCallbackUrl;
	
	private List<BizSystemInfo> bizSystemList;
	
	private List<String> allowedOrigins;
	
	private Integer userInfoCacheTime;
	
	/**
	 * bff层的logout方法后允许跳转的路径列表
	 */
	private List<String> allowedLogoutURLs;
	
	/**
	 * get login url 时候允许的referer参数列表
	 */
	private List<String> allowedReferers;
	
	@Data
	public static class BizSystemInfo{
		
		private String clientId;
		
		private String secret;
		
		private List<String> redirectUrls;
		
		private int sessionTimeOut;
		
		private int idleTime;
		
		private String bizPortalCallbackUrl;
		
		public boolean validate(String clientId,String redirectUrl) {
			if(!this.clientId.equals(clientId)) {
				return false;
			}

			AntPathMatcher antPathMatcher = new AntPathMatcher();
			return redirectUrls.stream().anyMatch(t -> antPathMatcher.match(t, redirectUrl));
		}
		
		public boolean validateAccess(String clientId,String secret) {
			return this.clientId.equals(clientId) && this.secret.equals(secret);
		}
	}
	
	public BizSystemInfo getBizSystemByClientId(String clientId) {
		return bizSystemList.stream().filter(a -> a.getClientId().equals(clientId)).
				findFirst().orElseThrow(() -> new AuthException("invalid clientId:" + clientId));
	}
	
	public String getLogoutUrl(String redirectUrl) {
		StringBuilder strBuilder = new StringBuilder(logoutUrl)
				.append("?post_logout_redirect_uri=")
				.append(URLEncoder.encode(redirectUrl, StandardCharsets.UTF_8));
		return strBuilder.toString();
	}
	
	public boolean validateReferer(String referer) {
		AntPathMatcher antPathMatcher = new AntPathMatcher();
		return allowedReferers.stream().anyMatch(t -> antPathMatcher.match(t, referer));
	}
	
}