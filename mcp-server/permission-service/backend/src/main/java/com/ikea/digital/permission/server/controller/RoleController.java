package com.ikea.digital.permission.server.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ikea.digital.permission.core.config.PermissionClientConfig;
import com.ikea.digital.permission.core.config.PmPreAuthorize;
import com.ikea.digital.permission.server.common.BaseResponse;
import com.ikea.digital.permission.server.common.util.CookieUtils;
import com.ikea.digital.permission.server.dto.RoleCreateOrUpdateDto;
import com.ikea.digital.permission.server.dto.RoleSearchDto;
//import com.ikea.digital.permission.server.resource.facade.RoleFacade;
import com.ikea.digital.permission.server.service.RoleService;
import com.ikea.digital.permission.server.vo.RolePageVo;
import com.ikea.digital.permission.server.vo.SimpleRoleVo;

@RestController
@RequestMapping("/rs/role")
public class RoleController {
	
	@Autowired
	private RoleService roleService;
	
	@Autowired
	private PermissionClientConfig pmClientConfig;
	
	@PostMapping("/create")
	@PmPreAuthorize("hasAnyRoleWithClient({'_roleManager','application_admin'}, #vo.clientId)")
	public BaseResponse<?> createRole(HttpServletRequest request, @RequestBody @Valid RoleCreateOrUpdateDto vo){
		String userToken = CookieUtils.readCookie(request, pmClientConfig.getEnv() + "_" + pmClientConfig.getClientId()); 
		vo.setName(StringUtils.trim(vo.getName()));
		roleService.createRole(vo, userToken);
		return BaseResponse.success();
	}
	
	@PostMapping("/update")
	@PmPreAuthorize("hasAnyRoleWithClient({'_roleManager','application_admin'}, #vo.clientId)")
	public BaseResponse<?> updateRole(@RequestBody @Valid RoleCreateOrUpdateDto vo){
		roleService.updateRole(vo);
		return BaseResponse.success();
	}
	
	@GetMapping("/delete/{clientId}/{roleId}")
	@PmPreAuthorize("hasAnyRoleWithClient({'_roleManager','application_admin'},#clientId)")
	public BaseResponse<?> deleteRole(@PathVariable("clientId") String clientId, @PathVariable("roleId") String roleId){
		roleService.deleteRole(clientId, roleId);
		return BaseResponse.success();
	}
	
	@GetMapping("/search")
	@PmPreAuthorize("hasAnyRoleWithClient({'_roleManager','application_admin'}, #clientId)")
	public BaseResponse<?> roleList(@RequestParam("clientId") String clientId,
			@RequestParam(value = "name",required = false) String roleName, 
			@RequestParam(name = "pageIndex", defaultValue = "1") int pageIndx, 
			@RequestParam(name = "pageSize", defaultValue = "10") int pageSize){
		RoleSearchDto vo = new RoleSearchDto();
		vo.setClientId(clientId);
		vo.setName(roleName);
		vo.setPageIndex(pageIndx);
		vo.setPageSize(pageSize);
		RolePageVo result = roleService.searchRole(vo);
		return BaseResponse.success(result);
	}
	
	@GetMapping("/list/{clientId}")
	@PmPreAuthorize("hasAnyRoleWithClient({'_roleManager','application_admin'}, #clientId)")
	public BaseResponse<List<SimpleRoleVo>> roleList(@PathVariable("clientId") String clientId) {
		List<SimpleRoleVo> result = roleService.listRole(clientId);
		return BaseResponse.success(result);
	}
	
}
