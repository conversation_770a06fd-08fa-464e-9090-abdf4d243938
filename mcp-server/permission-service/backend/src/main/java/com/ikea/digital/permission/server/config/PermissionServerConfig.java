/**
 * Project Name:backend
 * File Name:PermissionServerConfig.java
 * Package Name:com.ikea.digital.permission.server.common.config
 * Date:May 21, 20245:42:31 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.config;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import com.ikea.mas.permission.config.prop.PermissionServerProperties;

/**
 * ClassName:PermissionServerConfig <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     May 21, 2024 5:42:31 PM <br/>
 * <AUTHOR>	 
 */
@EnableConfigurationProperties(PermissionServerProperties.class)
@Configuration
public class PermissionServerConfig {

}