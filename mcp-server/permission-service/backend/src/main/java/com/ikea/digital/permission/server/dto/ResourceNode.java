/**
 * Project Name:backend
 * File Name:ResourceNode.java
 * Package Name:com.ikea.digital.permission.server.resource.domain.model
 * Date:Jun 6, 20244:22:28 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.dto;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Deque;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;

import lombok.Data;

/**
 * ClassName:ResourceNode <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jun 6, 2024 4:22:28 PM <br/>
 * <AUTHOR>	 
 */
@Data
public class ResourceNode {
	
	private String id;
	
	private ResourceNode parent;
	
	private List<ResourceNode> children = new ArrayList<>();
	
	public void addChildren(ResourceNode child) {
		children.add(child);
	}
	
	public void removeChildren(ResourceNode child) {
		ResourceNode resourceNode;
		Iterator<ResourceNode> it = children.iterator();
		while(it.hasNext()) {
			resourceNode = it.next();
			if(resourceNode.getId().equals(child.getId())) {
				it.remove();
			}
		}
	}
	
	public void removeChildrenRecursive(ResourceNode child) {
		ResourceNode resourceNode;
		boolean flag = false;
		Iterator<ResourceNode> it = children.iterator();
		while(it.hasNext()) {
			resourceNode = it.next();
			if(resourceNode.getId().equals(child.getId())) {
				it.remove();
				flag = true;
			}
		}
		
		if(flag && CollectionUtils.isEmpty(children)
				&& !isRoot()) {
			this.parent.removeChildrenRecursive(this);
		}
	}
	
	public static ResourceNode buildRoot() {
		ResourceNode node = new ResourceNode();
		node.setId("-1");
		node.setParent(null);
		return node;
	}
	
	public boolean isRoot() {
		if(Objects.isNull(this.parent)) {
			return true;
		}
		return false;
	}
	
	// 广度优先
	public static ResourceNode findNode(String id, ResourceNode root) {
		Deque<ResourceNode> nodeDeque = new LinkedList<>();
		ResourceNode node = root;
		nodeDeque.add(node);
		while (!nodeDeque.isEmpty()) {
			node = nodeDeque.pop();
			if(node.getId().equals(id)) {
				return node;
			}
			nodeDeque.addAll(node.getChildren());
		}
		return null;
	}
	
	// 深度优先
	public static ResourceNode findNode2(String id, ResourceNode root) {
		Deque<ResourceNode> nodeDeque = new LinkedList<>();
		ResourceNode node = root;
		nodeDeque.push(node);
		while (!nodeDeque.isEmpty()) {
			node = nodeDeque.pop();
			if (node.getId().equals(id)) {
				return node;
			}
			List<ResourceNode> children = node.getChildren();
			// 注意这里要从后向前遍历
			for (int i = children.size() - 1; i >= 0; i--) {
				// 从头压入
				nodeDeque.push(children.get(i));
			}
		}
		return null;
	}
	
	public static List<String> getAllResourceIds(ResourceNode root) {
		if(Objects.isNull(root)) {
			return Collections.emptyList();
		}
		List<String> list = new ArrayList<>();
		Deque<ResourceNode> nodeDeque = new LinkedList<>();
		ResourceNode node = root;
		nodeDeque.add(node);
		while (!nodeDeque.isEmpty()) {
			node = nodeDeque.pop();
			list.add(node.getId());
			nodeDeque.addAll(node.getChildren());
		}
		
		return list;
	}
	

}