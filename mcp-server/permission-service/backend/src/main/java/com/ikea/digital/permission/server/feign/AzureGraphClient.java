package com.ikea.digital.permission.server.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import com.ikea.digital.permission.auth.dto.AzureUserDto;
import com.ikea.digital.permission.server.config.FeignConfig;

import feign.Response;

/**
 * client connect to Azure Graph API
 * Created by <PERSON> on 2021/5/25
 **/
@FeignClient(name = "azureGraphClient", url = "https://graph.microsoft.com", configuration = FeignConfig.class)
public interface AzureGraphClient {

    @GetMapping(value = "/v1.0/users/{email}")
    ResponseEntity<AzureUserDto> getUserDetail(@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
                                            @PathVariable(value = "email") String email,
                                            @RequestParam("$select") String filedNames);

    @GetMapping(value = "/v1.0/me/photo/$value")
    Response getUserAvatar(@RequestHeader(HttpHeaders.AUTHORIZATION) String token);

}
