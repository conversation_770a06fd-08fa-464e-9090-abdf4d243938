name: PUBLISH API SPEC # The name of your workflow
on: 
  workflow_dispatch:
    inputs:
      action:
        description: 'post - publish new api spec version or patch - update api spec without version change'     
        required: true
        default: 'post'
      api-env:
        description: 'dev,stage,prod - APIM environment'
        required: true
        default: 'dev'

jobs:
  generate-apispec:
    uses: china-digital-hub/shared-workflows/.github/workflows/generate-ApiSpec.yaml@feature/17
    with:
      module: backend
    secrets:
      RT_CDH_USERNAME: ${{ secrets.RT_CDH_USERNAME }}
      RT_CDH_TOKEN: ${{ secrets.RT_CDH_TOKEN }}
      BOOT_ARGS: ${{ secrets.BOOT_ARGS }}
      GRADLE_PROPERTIES: ${{ secrets.CDH_GRADLE_PROPERTIES }}

  upload-api-spec:
    uses: china-digital-hub/shared-workflows/.github/workflows/upload-ApiSpec.yaml@feature/17
    needs: [generate-apispec]
    with:
      environment: ${{ inputs.api-env }}
      specUrl: ${{ needs.generate-apispec.outputs.SPEC_URL}}
      method: ${{ inputs.action }}
    secrets:
      X_CLIENT_ID: ${{ secrets.X_CLIENT_ID }}
      API_SPEC_TOKEN: ${{ secrets.ALLEN_API_SPEC_READ_TOKEN }}
