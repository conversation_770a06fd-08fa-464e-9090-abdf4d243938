/**
// * Project Name:bff
// * File Name:PmBackendClient.java
// * Package Name:com.ikea.mas.permission.service.feign
// * Date:May 22, 202410:24:11 AM
// * Copyright (c) 2024, theodore.ni All Rights Reserved.
// *
// */
//package com.ikea.mas.permission.service.feign;
//
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestHeader;
//import org.springframework.web.bind.annotation.RequestParam;
//
//import com.ikea.mas.permission.common.BaseResponse;
//import com.ikea.mas.permission.common.Constants;
//import com.ikea.mas.permission.config.FeignConfiguration;
//
///**
// * ClassName:PmBackendClient <br/>
// * Function: TODO ADD FUNCTION. <br/>
// * Date:     May 22, 2024 10:24:11 AM <br/>
// * <AUTHOR>	 
// */
////@FeignClient(name = "pmBackendClient", url = "${pmfeign.url}", configuration = FeignConfiguration.class)
//public interface PmBackendClient {
//	
//	@GetMapping(value = "/user/auth/getLoginUrl", headers = {
//			Constants.CLIENT_SECRET + "=${pmfeign.getLoginUrl.secret}",
//			Constants.API_KEY + "=${pmfeign.apikey}"})
//	BaseResponse<String> getLoginUrl(@RequestHeader(Constants.CLIENT_ID) @RequestParam("clientId") String clientId,
//            @RequestParam("redirectUrl") String redirectUrl,
//            @RequestParam("referer") String referer,
//            @RequestParam(name =  "internalNet", required = false) boolean internalNet);
//
//}