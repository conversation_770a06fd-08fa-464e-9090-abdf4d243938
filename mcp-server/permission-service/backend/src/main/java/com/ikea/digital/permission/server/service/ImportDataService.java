/**
 * Project Name:backend
 * File Name:ImportDataService.java
 * Package Name:com.ikea.digital.permission.server.service
 * Date:Dec 5, 202410:28:36 AM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.service;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.type.TypeReference;
import com.ikea.digital.permission.server.common.util.JacksonUtils;
import com.ikea.digital.permission.server.dto.BatchUserAssignRoleDto;
import com.ikea.digital.permission.server.dto.ImportMenuDto;
import com.ikea.digital.permission.server.dto.ImportRoleDto;
import com.ikea.digital.permission.server.dto.ImportUserDto;
import com.ikea.digital.permission.server.dto.MenuCreateOrUpdateDto;
import com.ikea.digital.permission.server.dto.RoleCreateOrUpdateDto;
import com.ikea.digital.permission.server.dto.UserAssignRoleDto;
import com.ikea.digital.permission.server.dto.UserGrantRoleDto.RoleStoreDto;
import com.ikea.digital.permission.server.feign.model.StoreDO;
import com.ikea.digital.permission.server.repository.MenuRepository;
import com.ikea.digital.permission.server.repository.RoleRepository;
import com.ikea.digital.permission.server.repository.UserRepository;
import com.ikea.digital.permission.server.resource.domain.model.Menu;
import com.ikea.digital.permission.server.resource.domain.model.Role;
import com.ikea.digital.permission.server.resource.domain.model.User;

import lombok.extern.slf4j.Slf4j;

/**
 * ClassName:ImportDataService <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Dec 5, 2024 10:28:36 AM <br/>
 * <AUTHOR>	 
 */
@Slf4j
@Service
public class ImportDataService {
	
	@Value("classpath:/import/roles.json")
	private Resource roleAndMenuResource;
	
	@Value("classpath:/import/users.json")
	private Resource userResource;
	
	@Autowired
	private RoleRepository roleRepository;
	
	@Autowired
	private MenuRepository menuRepository;
	
	@Autowired
	private UserRepository userRepository;
	
	@Autowired
	private StoreService storeService;
	
	@Autowired
	private UserService userService;
	
	public boolean importRolesAndMenues(String clientId) {
		
		List<ImportRoleDto> roles = null;
		try(InputStream config = roleAndMenuResource.getInputStream()){
			roles = JacksonUtils.readValue(config, new TypeReference<List<ImportRoleDto>>() {});
		} catch (IOException e) {
			log.error("read json file error.", e);
		}
		
		if(CollectionUtils.isEmpty(roles)) {
			return false;
		}
		
		// import所有的menu
		Map<String, ImportMenuDto> imenuMap = new HashMap<>();
		for (ImportRoleDto importRole : roles) {
			putMenuIntoMap(importRole.getMenues(), imenuMap);
		}
		Map<String, Long> menuIdMap = importMenu(imenuMap, clientId);
		
		// import role
		for (ImportRoleDto importRole : roles) {
			importRole(clientId, importRole, menuIdMap);
		}
		
		return true;
		
	}
	
	@Transactional
	public int importUsers(String clientId) {
		
		List<ImportUserDto> iusers = null;
		try(InputStream config = userResource.getInputStream()){
			iusers = JacksonUtils.readValue(config, new TypeReference<List<ImportUserDto>>() {});
		} catch (IOException e) {
			log.error("read json file error.", e);
		}
		
		int failCount = 0;
		for (ImportUserDto iUser : iusers) {
			if(!assignUserRole(iUser, clientId)) {
				failCount++;
				log.error("user:{} role:{} storeIds:{}", iUser.getUser(), iUser.getRoles(), iUser.getStoreIds());
			}
		}
		
		return failCount;
	}
	
	@Transactional
	public List<ImportUserDto> importUsers(String clientId, List<ImportUserDto> iusers) {
		
		List<ImportUserDto> failedUsers = new ArrayList<>();
		
		for (ImportUserDto iUser : iusers) {
			if(!assignUserRole(iUser, clientId)) {
				failedUsers.add(iUser);
				log.error("user:{} role:{} storeIds:{}", iUser.getUser(), iUser.getRoles(), iUser.getStoreIds());
			}
		}
		return failedUsers;
	}
	
	private boolean assignUserRole(ImportUserDto iUser, String clientId) {
		
		Optional<User> userOpt = userRepository.searchSingleUserByUsername(iUser.getUser());
		if(userOpt.isEmpty()) {
			return false;
		}
		
		List<Role> clientAllRoles = roleRepository.getRoles(clientId);
		if(CollectionUtils.isEmpty(clientAllRoles)) {
			return false;
		}
		
		List<StoreDO> allStoreList = storeService.queryAllStores();
		if(CollectionUtils.isEmpty(allStoreList)) {
			log.error("query all store error.");
			return false;
		}
		
		String storeIds = iUser.getStoreIds();
		List<String> storeIdList;
		if("ALL".equals(storeIds)) {
			storeIdList = allStoreList.stream().map(StoreDO::getStoreId).collect(Collectors.toList());
		} else {
			storeIdList = Arrays.asList(storeIds.split(","));
		}
		
		String roles = iUser.getRoles();
		String[] roleArr = roles.split(",");
		
		List<RoleStoreDto> roleStoreDtoList = new ArrayList<>();
//		boolean hasAppWriteRole = false;
		for (String role : roleArr) {
			if(clientAllRoles.stream().noneMatch(kcRole -> kcRole.getName().equals(role))) {
				return false;
			}
			RoleStoreDto dto = new RoleStoreDto();
			dto.setName(role);
			dto.setStoreIds(storeIdList);
			roleStoreDtoList.add(dto);
		}
		
		UserAssignRoleDto bo = new BatchUserAssignRoleDto();
		bo.setAddedList(roleStoreDtoList);
		bo.setClientId(clientId);
		bo.setUserId(userOpt.get().getUserId());
		
		userService.assignRole(bo);
		
		return true;
	}
	
	private void putMenuIntoMap(List<ImportMenuDto> imenues, final Map<String, ImportMenuDto> map) {
		for (ImportMenuDto imenu : imenues) {
			if(!map.containsKey(imenu.getName())) {
				map.put(imenu.getName(), imenu);
				continue;
			}
			ImportMenuDto old = map.get(imenu.getName());
			if(StringUtils.isBlank(old.getDesc()) && StringUtils.isNotBlank(imenu.getDesc())) {
				map.put(imenu.getName(), imenu);
			}
		}
	}
	
	private Map<String, Long> importMenu(Map<String, ImportMenuDto> map, String clientId) {
		Map<String, Long> menuIdMap = new HashMap<>();
		for (ImportMenuDto imenu : map.values()) {
			Long id = createMenu(imenu, clientId);
			menuIdMap.putIfAbsent(imenu.getName(), id);
		}
		return menuIdMap;
	}
	
	private void importRole(String clientId, ImportRoleDto irole, final Map<String, Long> menuIdMap) {
		List<ImportMenuDto> imenues = irole.getMenues();
		List<String> menueIds;
		if(CollectionUtils.isEmpty(imenues)) {
			menueIds = Collections.emptyList();
			createRole(clientId, irole, menueIds);
			return;
		}
		menueIds = new ArrayList<>();
		for (ImportMenuDto imenu : imenues) {
			Long id = menuIdMap.get(imenu.getName());
			if(Objects.isNull(id)) {
				throw new RuntimeException("role:" + irole.getName() + " menu:" + imenu.getName() + " error.");
			}
			menueIds.add(String.valueOf(id));
		}
		createRole(clientId, irole, menueIds);
	}
	
	private Long createMenu(ImportMenuDto imune, String clientId) {
		Optional<Menu> menuOp = menuRepository.get(imune.getName(), clientId);
		if(menuOp.isPresent()) {
			return menuOp.get().getId();
		}
		
		MenuCreateOrUpdateDto dto = imune.toCmd(clientId);
		return menuRepository.createMenu(dto);
	}
	
	private void createRole(String clientId, ImportRoleDto irole, List<String> menueIds) {
		RoleCreateOrUpdateDto cmd = new RoleCreateOrUpdateDto();
		cmd.setClientId(clientId);
		cmd.setDescription(irole.getDesc());
		cmd.setName(irole.getName());
		cmd.setMenuIds(menueIds);
		roleRepository.createRole(cmd);
	}

}

