package com.ikea.digital.permission.server.common.util;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.ClientInitialAccessResource;
import org.keycloak.admin.client.resource.ClientsResource;
import org.keycloak.admin.client.resource.GroupResource;
import org.keycloak.admin.client.resource.GroupsResource;
import org.keycloak.admin.client.resource.RoleMappingResource;
import org.keycloak.admin.client.resource.RoleResource;
import org.keycloak.admin.client.resource.RolesResource;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.admin.client.resource.UsersResource;
import org.keycloak.representations.idm.ClientInitialAccessCreatePresentation;
import org.keycloak.representations.idm.ClientInitialAccessPresentation;
import org.keycloak.representations.idm.ClientRepresentation;
import org.keycloak.representations.idm.GroupRepresentation;
import org.keycloak.representations.idm.RoleRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.ikea.digital.permission.server.config.prop.KeycloakAdminProperties;

@Slf4j
@Component
public class KeycloakClient {

	@Autowired
	@Qualifier("keycloakAdmin")
	private Keycloak keycloakAdmin;
	
	@Autowired
	private KeycloakAdminProperties keycloakAdminConfig;
	
	public String newInitialAccessToken() {
		ClientInitialAccessResource resource = keycloakAdmin.realm(keycloakAdminConfig.getRealm()).clientInitialAccess();
		ClientInitialAccessPresentation result = resource.create(new ClientInitialAccessCreatePresentation());
		return result.getToken();
	}
	
	private ClientsResource getClientsResource() {
		return keycloakAdmin.realm(keycloakAdminConfig.getRealm()).clients();
	}
	
	private RolesResource getClientRolesResource(String clientPkId) {
		return this.getClientsResource().get(clientPkId).roles();
	}
	
	private UsersResource getUsersResource() {
		return keycloakAdmin.realm(keycloakAdminConfig.getRealm()).users();
	}
	
	private GroupsResource getGroupsResource() {
		return keycloakAdmin.realm(keycloakAdminConfig.getRealm()).groups();
	}
	
	private GroupResource getGroupResource(String groupId) {
		return keycloakAdmin.realm(keycloakAdminConfig.getRealm()).groups().group(groupId);
	}
	
	//============================== Client ===============================
	public List<ClientRepresentation> clientList(){
		return this.getClientsResource().findAll();
	}
	
	public String getClientPkId(String clientId) {
		return this.getClientsResource().findByClientId(clientId).get(0).getId();
	}
	
	//============================== Role ===============================
	public void createRole(String clientPkId,RoleRepresentation kcRole) {
		this.getClientRolesResource(clientPkId).create(kcRole);
	}
	
	public void updateRole(String clientPkId, RoleRepresentation kcRole) {
		this.getClientRolesResource(clientPkId).get(kcRole.getName()).update(kcRole);
	}
	
	public void deleteRole(String roleId) {
		try {
			keycloakAdmin.realm(keycloakAdminConfig.getRealm()).rolesById().deleteRole(roleId);
		}catch(jakarta.ws.rs.NotFoundException e) {
			log.error(roleId, e);
		}
	}
	
	public RoleRepresentation preciseSearchRole(String clientPkId, String name) {
		RoleResource roleRs = this.getClientRolesResource(clientPkId).get(name);
		try {
			return roleRs.toRepresentation();
		}catch(jakarta.ws.rs.NotFoundException e) {
			return null;
		}
	}
	
	public List<RoleRepresentation> searchRole(String clientPkId, String name) {
		return this.getClientRolesResource(clientPkId).list(name, false);
	}
	
	public RoleRepresentation getRole(String clientPkId, String name) {
		return this.getClientRolesResource(clientPkId).get(name).toRepresentation();
	}
	
	public RoleRepresentation getRoleById(String roleId) {
		return keycloakAdmin.realm(keycloakAdminConfig.getRealm()).rolesById().getRole(roleId);
	}
	
	public List<UserRepresentation> getRoleUsers(String clientPkId, String name) {
		return this.getClientRolesResource(clientPkId).get(name).getUserMembers();
	}
	
	public List<RoleRepresentation> getRoles(String clientPkId) {
		return this.getClientRolesResource(clientPkId).list(false);
	}
	
	//============================== User ===============================
	public String createUser(UserRepresentation user) {
		Response response = this.getUsersResource().create(user);
		return this.getIdFromLocationHeader(response.getLocation().toString());
	}
	
	private String getIdFromLocationHeader(String locationHeader) {
		String[] parts = locationHeader.split("/");
		return parts[parts.length - 1];
	}	
	
	public void updateUser(UserRepresentation user) {
		UserResource us = this.getUserRsById(user.getId());
		us.update(user);
	}
	
	public void deleteUser(String userId) {
		try {
			this.getUserRsById(userId).remove();
		}catch(jakarta.ws.rs.NotFoundException e) {
		}
	}
	
	public UserRepresentation getUserById(String userId) {
		UserResource us = this.getUserRsById(userId);
		try {
			List<GroupRepresentation> userGroups =  us.groups();
			List<String> groups = userGroups == null ? null : userGroups.stream().map(GroupRepresentation::getPath).collect(Collectors.toList());
			UserRepresentation kcUser = us.toRepresentation();
			kcUser.setGroups(groups);
			return kcUser;
		}catch(jakarta.ws.rs.NotFoundException e) {
			return null;
		}
	}
	
	public UserResource getUserRsById(String userId) {
		return this.getUsersResource().get(userId);
	}
	
	public List<UserRepresentation> searchUserByUsername(String name,Integer first,Integer max) {
		return this.getUsersResource().search(name, first, max, false);
	}
	
	public int searchUserCountByUsername(String name) {
		return this.getUsersResource().count(name);
	}
	
	public List<UserRepresentation> searchUserByRoleName(String clientPkId, String roleName, 
			Integer first, Integer max) {
		RoleResource roleRs = this.getClientRolesResource(clientPkId).get(roleName);
		List<UserRepresentation> list = Objects.isNull(first) ? roleRs.getUserMembers() : roleRs.getUserMembers(first, max);
		if(CollectionUtils.isEmpty(list)) {
			return Collections.emptyList();
		}
		return list;
	}
	
	//============================== logout ===============================
	public void logout(String userId) {
		// TODO: 控制台确实有单应用登出功能。但官方文档中并没提供单登出接口，且SDK中也不存在单logout方法。(18-21版本都不存在)
		// 所以这里先统一登出用户全部会话。
		this.getUserRsById(userId).logout();
	}
	
	//============================== Group ===============================
	public void createGroup(GroupRepresentation kcGroup) {
		this.getGroupsResource().add(kcGroup);
	}
	
	public void createSubGroup(String groupId,GroupRepresentation kcGroup) {
		this.getGroupResource(groupId).subGroup(kcGroup);
	}
	
	public void updateGroup(GroupRepresentation kcGroup) {
		this.getGroupResource(kcGroup.getId()).update(kcGroup);
	}
	
	public void deleteGroup(String groupId) {
		try {
			this.getGroupResource(groupId).remove();
		}catch(jakarta.ws.rs.NotFoundException e) {
			//...
		}
	}
	
	public GroupRepresentation getGroupById(String groupId) {
		try {
			return this.getGroupResource(groupId).toRepresentation();
		}catch(jakarta.ws.rs.NotFoundException e) {
			return null;
		}
	}
	
	public RoleMappingResource getGroupClientRoleMapping(String groupId) {
		return this.getGroupResource(groupId).roles();
	}
	
	public List<GroupRepresentation> groups() {
		return this.getGroupsResource().groups();
	}

	
}
