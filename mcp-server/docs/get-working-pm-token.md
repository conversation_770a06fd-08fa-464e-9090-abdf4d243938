# How to Get a Working pm_user_token

Based on the permission service code analysis, here's the **complete solution** for getting a valid `pm_user_token`.

## 🎯 **The Key Understanding**

After analyzing the permission service code, we now understand:

1. **`pm_user_token` is NOT just a token format** - it's a **Redis key** that stores user data
2. **The format is correct** - `Base64(<EMAIL>)`
3. **But the token must be stored in Redis** by the permission service to be valid
4. **Only the OAuth2 flow creates valid tokens** because it stores them in Redis

## 🔍 **The Permission Service Flow**

```java
// 1. OAuth2 code exchange
AccessTokenResponse tokenRes = keycloakService.getKeycloakAccessToken(code, redirectUrl, sessionId);
DecodedJWT authJwt = JWT.decode(tokenRes.getIdToken());
String userId = authJwt.getClaim("sub").asString();

// 2. Create SSO session in Redis
String ssoUUID = UUID.randomUUID().toString();
redisHelper.hmset(ssoUUID, ssoValueMap, ssoLoginTimeOut + 600);

// 3. Create pm_user_token
PmToken pmToken = new PmToken(ssoUUID, userId, clientSessionTimeOut, currMill);
String userTag = pmToken.token(); // Base64 encoded

// 4. Store pm_user_token in Redis (THIS IS THE KEY!)
redisHelper.set(userTag, userId, clientSessionTimeOut);
```

## 🚀 **Solution: Get Token from Browser**

Since we can't easily replicate the OAuth2 flow, the **practical solution** is to get a working token from your browser:

### **Step 1: Open Your Application**

1. Go to `https://admin.ingka-dt.cn/app/orders-portal/oms/index`
2. Make sure you're logged in
3. Open Developer Tools (F12)

### **Step 2: Capture the Authentication Flow**

1. **Go to Network tab** in Developer Tools
2. **Clear the network log**
3. **Refresh the page** or navigate to orders
4. **Look for requests** to `fe-dev-i.ingka-dt.cn/order-web/`

### **Step 3: Find the pm_user_token**

Look for one of these patterns:

#### **Option A: In Request Headers**
```
Cookie: test_orders-portal=NzhmM2IxMjAtNWU3NS00YzgxLThmMDEtMjAxZGMxYjVjNzEwQDE3NTQwMTU2MTkyNDQuM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA=
```

#### **Option B: In URL Parameters**
```
https://fe-dev-i.ingka-dt.cn/order-web/user/current?pm_user_token=NzhmM2IxMjAtNWU3NS00YzgxLThmMDEtMjAxZGMxYjVjNzEwQDE3NTQwMTU2MTkyNDQuM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA=
```

#### **Option C: In Response Set-Cookie Headers**
```
Set-Cookie: test_orders-portal=NzhmM2IxMjAtNWU3NS00YzgxLThmMDEtMjAxZGMxYjVjNzEwQDE3NTQwMTU2MTkyNDQuM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA=; Max-Age=3600; Domain=ingka-dt.cn; Path=/order-web; HttpOnly
```

### **Step 4: Extract the Token**

From any of the above, extract the Base64 token:
```
NzhmM2IxMjAtNWU3NS00YzgxLThmMDEtMjAxZGMxYjVjNzEwQDE3NTQwMTU2MTkyNDQuM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA=
```

### **Step 5: Test the Token**

Use our test script:

```bash
# Update the token in the script
# Replace REPLACE_WITH_ACTUAL_TOKEN_FROM_BROWSER with your token
npm run test:permission-auth
```

Or test directly with curl:

```bash
curl 'https://fe-dev-i.ingka-dt.cn/order-web/orders/search' \
  -H 'content-type: application/json' \
  -b 'test_orders-portal=YOUR_TOKEN_HERE' \
  --data-raw '{"storeIds":[],"baseSize":10,"page":1,"size":10,"timestamp":1754025553497}'
```

## 🔧 **Integration with MCP Server**

Once you have a working token:

### **Option 1: Static Configuration**
```bash
# Add to .env
STATIC_PM_USER_TOKEN=YOUR_WORKING_TOKEN_HERE
```

### **Option 2: Dynamic Token Refresh**
Create a script that:
1. **Monitors token expiration**
2. **Automatically refreshes** from browser/application
3. **Updates MCP server configuration**

### **Option 3: Frontend Integration**
Modify your frontend to:
1. **Extract pm_user_token** after OAuth2 flow
2. **Pass it to MCP server** via headers
3. **MCP server uses it** for backend API calls

## 📋 **Token Format Verification**

You can verify the token format:

```javascript
// Decode the Base64 token
const tokenString = Buffer.from(token, 'base64').toString();
console.log('Token content:', tokenString);

// Should show: <EMAIL>
// Example: 78f3b120-5e75-4c81-8f01-201dc1b5c710@1754015619244.3d2e265c-f895-4e32-b3be-5a139fa856c1.3600
```

## ⏰ **Token Expiration**

- **Default session timeout**: 3600 seconds (1 hour)
- **SSO timeout**: Usually longer (8+ hours)
- **Tokens expire** and need to be refreshed
- **Monitor expiration** and refresh as needed

## 🎯 **Why This Works**

1. **Browser OAuth2 flow** creates valid Redis entries
2. **Permission service stores** the token in Redis
3. **Backend APIs validate** by checking Redis
4. **Our generated tokens fail** because they're not in Redis
5. **Browser tokens work** because they're properly stored

## 🚀 **Production Solution**

For production, implement:

1. **OAuth2 flow in frontend** (already working)
2. **Token extraction** from OAuth2 response
3. **Token passing** to MCP server
4. **Automatic refresh** when tokens expire
5. **Fallback mechanisms** for token failures

This approach **eliminates the need** for complex token generation and focuses on **using the real tokens** from the actual authentication system.

## 🎊 **Summary**

- ✅ **Use real tokens** from browser OAuth2 flow
- ✅ **No need to generate tokens** - they must be stored in Redis
- ✅ **Simple integration** with existing authentication
- ✅ **Production-ready approach** with proper token management
- ✅ **Eliminates all the complexity** of trying to replicate the OAuth2 flow

The key insight is that **pm_user_token is a session identifier**, not just a formatted token, which is why only the real OAuth2 flow creates working tokens!
