package com.ikea.digital.permission.server.resource.domain.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.ikea.digital.permission.server.resource.domain.model.Menu;

public interface MenuPOMapper {
	
    int deleteByPrimaryKey(Long id);
    
    int deleteByClientIdAndIds(@Param("clientId") String clientId, 
    		@Param("ids") List<Long> ids);

    int insert(Menu row);

    int insertSelective(Menu row);

    Menu selectByPrimaryKey(Long id);
    
    List<Menu> selectSelective(Menu menu);
    
    List<Menu> selectByClientIdAndIdsSelective(@Param("clientId") String clientId, 
    		@Param("ids") List<Long> ids);
    
    List<Menu> selectByClientIdAndParentIds(@Param("clientId") String clientId, 
    		@Param("ids") List<Long> ids);

    int updateByPrimaryKeySelective(Menu row);

    int updateByPrimaryKey(Menu row);
}