version = '1.0-SNAPSHOT'

configurations {
    all*.exclude group: 'org.apache.logging.log4j', module: 'log4j-api'
    all*.exclude group: 'org.apache.logging.log4j', module: 'log4j-core'
}

dependencies {

    api project(':common')
    
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.cloud:spring-cloud-starter-bootstrap'

    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config'

    implementation 'org.hibernate.validator:hibernate-validator'

    implementation 'com.auth0:java-jwt:3.4.0'
    implementation 'com.microsoft.graph:microsoft-graph:5.+'
    implementation 'com.azure:azure-identity:1.+'

    implementation group: 'org.apache.commons', name: 'commons-lang3'
    implementation group: 'org.apache.commons', name: 'commons-collections4'

    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    
    implementation 'com.ikea.mas:ikea-log-tracing:2.0.0-SNAPSHOT'
    implementation 'org.zalando:logbook-okhttp:3.7.2'
    implementation 'org.zalando:logbook-jaxrs:3.7.2'
    
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'
    
    implementation 'org.apache.httpcomponents.client5:httpclient5'
    
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-api'
    
    
}
