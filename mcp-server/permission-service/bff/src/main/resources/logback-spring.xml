<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="false">
    <include resource="com/ikea/log/tracing/logging/base-appender.xml" />

    <!-- <appender name="LOGBOOK_OUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <jsonGeneratorDecorator class="net.logstash.logback.decorate.PrettyPrintingJsonGeneratorDecorator"/>
        </encoder>
    </appender> -->
    
    <appender name="LOGBOOK_OUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LogstashEncoder"/>
    </appender>

    <logger name="org.zalando.logbook" level="trace" additivity="false">
        <appender-ref ref="LOGBOOK_OUT"/>
    </logger>
    
    <root level="INFO">
        <appender-ref ref="IKEA_BASE"/>
    </root>
    
    <!-- dev,local,test环境日志级别为DEBUG -->
	<springProfile name="dev,local,test">
	   <logger name="com.ikea" level="DEBUG"></logger>
	</springProfile>
	
</configuration>