/**
 * Project Name:permission-server
 * File Name:MenuRepository.java
 * Package Name:com.ikea.digital.permission.server.resource.domain.repository
 * Date:Jan 30, 20244:57:06 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.ikea.digital.permission.core.context.UserHolder;
import com.ikea.digital.permission.server.common.OptTypeEnum;
import com.ikea.digital.permission.server.common.util.JacksonUtils;
import com.ikea.digital.permission.server.dto.MenuCreateOrUpdateDto;
import com.ikea.digital.permission.server.dto.MenuDeleteDto;
import com.ikea.digital.permission.server.exception.ResourceException;
import com.ikea.digital.permission.server.resource.domain.mapper.MenuPOMapper;
import com.ikea.digital.permission.server.resource.domain.model.Menu;
import com.ikea.digital.permission.server.resource.domain.model.persistent.LogPO;

/**
 * ClassName:MenuRepository <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jan 30, 2024 4:57:06 PM <br/>
 * <AUTHOR>	 
 */
@Repository
public class MenuRepository {
	
	@Autowired
	private MenuPOMapper mapper;
	
	@Autowired
	private LogRepository logRepository;
	
	public List<Menu> selectSelective(Menu po) {
		Assert.notNull(po, "query criteria MenuPO cannot be null");
		return mapper.selectSelective(po);
	}
	
	public Menu selectOneSelective(Menu po) {
		Assert.notNull(po, "query criteria MenuPO cannot be null");
		List<Menu> list = mapper.selectSelective(po);
		if(CollectionUtils.isNotEmpty(list)) {
			return list.get(0);
		}
		return null;
	}
	
	public Menu selectByPrimaryKey(Long id) {
		return mapper.selectByPrimaryKey(id);
	}
	
	public List<Menu> selectByClientIdAndIdsSelective(String clientId, List<Long> ids) {
		return mapper.selectByClientIdAndIdsSelective(clientId, ids);
	}
	
	// ----------------------------------------------------------------
	
	public Optional<Menu> get(String menuName, String clientId) {
		return get(menuName, clientId, null);
	}
	
	public Optional<Menu> get(String menuName, String clientId, Long parentId) {
        Menu criteria = new Menu();
        criteria.setClientId(clientId);
        criteria.setName(menuName);
        if(Objects.nonNull(parentId)) {
        	criteria.setParentMenuId(parentId);
        }
		Menu po = this.selectOneSelective(criteria);
		
		return Optional.ofNullable(po).map((Menu menuPo) -> {
			Menu menu = new Menu();
			menu.setClientId(clientId);
			menu.setDescription(menuPo.getDescription());
			menu.setIcon(menuPo.getIcon());
			menu.setId(menuPo.getId());
			menu.setMenuType(menuPo.getMenuType());
			menu.setName(menuPo.getName());
			menu.setParentMenuId(menuPo.getParentMenuId());
			menu.setPath(menuPo.getPath());
			menu.setSortIndex(menuPo.getSortIndex());
			return menu;
		});
	}
	
	public Long createMenu(MenuCreateOrUpdateDto dto) {
		checkParentMenuExists(dto.getParentMenuId());
		Menu menu = MenuCreateOrUpdateDto.toMenu(dto);
		mapper.insert(menu);
		
		logRepository.insert(new LogPO(OptTypeEnum.ADD_RS.getCode(), dto.getName(), JacksonUtils.object2JsonString(dto),
				null,JacksonUtils.object2JsonString(menu)));
		
		return menu.getId();
	}
	
	public void updateMenu(MenuCreateOrUpdateDto dto) {
		Menu menuPO = mapper.selectByPrimaryKey(dto.getId());
		if(Objects.isNull(menuPO)) {
			throw new ResourceException("资源不存在!");
		}
		this.checkParentMenuExists(dto.getParentMenuId());
		
		if(!menuPO.getClientId().equals(dto.getClientId())) {
			throw new ResourceException("资源与client不匹配");
		}
		
		Menu menuNewPO = new Menu();
		menuNewPO.setId(dto.getId());
		menuNewPO.setName(dto.getName());
		menuNewPO.setDescription(dto.getDescription());
		menuNewPO.setIcon(dto.getIcon());
		menuNewPO.setSortIndex(dto.getOrder());
		menuNewPO.setMenuType(dto.getMenuType());
		menuNewPO.setPath(dto.getPath());
		menuNewPO.setClientId(menuPO.getClientId());
		menuNewPO.setCreator(menuPO.getCreator());
		menuNewPO.setCreateGmt(menuPO.getCreateGmt());
		menuNewPO.setModifier(UserHolder.getUserInfo() == null ? "" : UserHolder.getUserInfo().getEmail());
		menuNewPO.setModifyGmt(new Date());
		mapper.updateByPrimaryKeySelective(menuNewPO);
		
		logRepository.insert(new LogPO(OptTypeEnum.EDIT_RS.getCode(), 
				String.valueOf(dto.getId()), 
				JacksonUtils.object2JsonString(dto), 
				JacksonUtils.object2JsonString(menuPO), 
				JacksonUtils.object2JsonString(menuNewPO)));
	}
	
	public List<Long> getBrotherMenuIds(String clientId, Long menuId) {
		Menu menuPo = mapper.selectByPrimaryKey(menuId);
		if(Objects.isNull(menuPo) || Objects.isNull(menuPo.getParentMenuId())) {
			return Collections.emptyList();
		}
		List<Menu> subMenues = mapper.selectByClientIdAndParentIds(clientId, Collections.singletonList(menuPo.getParentMenuId()));
		if(CollectionUtils.isEmpty(subMenues)) {
			return Collections.emptyList();
		}
		
		subMenues.removeIf(menu -> menu.getId().equals(menuId));
		return subMenues.stream().map(Menu::getId).collect(Collectors.toList());
	}
	
	public void deleteMenu(MenuDeleteDto dto) {
		List<Long> menuIds = getSubMenuIds(dto.getClientId(), dto.getId());
		menuIds.add(dto.getId());
		
		mapper.deleteByClientIdAndIds(dto.getClientId(), menuIds);
		
		logRepository.insert(new LogPO(OptTypeEnum.DELETE_RS.getCode(), String.valueOf(dto.getId()), JacksonUtils.object2JsonString(dto),
				null, null));
	}
	
	public List<Long> getSubMenuIds(String clientId, Long menuId) {
		List<Menu> subMenus = fetchSubMenu(clientId, Collections.singletonList(menuId));
		return subMenus.stream().map(Menu::getId).collect(Collectors.toList());
	}
	
	public List<Long> getParentMenuIds(String clientId, Long menuId) {
		Menu menuPo = mapper.selectByPrimaryKey(menuId);
		if(Objects.isNull(menuPo) || Objects.isNull(menuPo.getParentMenuId())) {
			return Collections.emptyList();
		}
		List<Long> parentIds = new ArrayList<>();
		parentIds.add(menuPo.getParentMenuId());
		parentIds.addAll(getParentMenuIds(clientId, menuPo.getParentMenuId()));
		return parentIds;
	}
	
	private void checkParentMenuExists(Long parentMenuId) {
		if(Objects.nonNull(parentMenuId)) {
			Menu parentMenuPo = mapper.selectByPrimaryKey(parentMenuId);
			if(Objects.isNull(parentMenuPo)) {
				throw new ResourceException("无效的parentMenuId");
			}
		}
	}
	
	private List<Menu> fetchSubMenu(String clientId,List<Long> menuIds) {
		
        List<Menu> subMenus = mapper.selectByClientIdAndParentIds(clientId, menuIds);
        
        if(CollectionUtils.isEmpty(subMenus)) {
        	return Collections.emptyList();
        }
        
        List<Menu> menus = new ArrayList<>();
        menus.addAll(subMenus);
        List<Long> subMenuIds = subMenus.stream().map(Menu::getId).collect(Collectors.toList());
        menus.addAll(this.fetchSubMenu(clientId, subMenuIds));
        return menus;
	}

}