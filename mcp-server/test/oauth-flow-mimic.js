#!/usr/bin/env node

/**
 * 🔄 OAuth2 Flow Mimic
 *
 * Purpose:
 * 1. Mimic the exact OAuth2 flow captured from browser
 * 2. Use existing Keycloak session cookies to get pm_user_token
 * 3. Follow the permission-service authentication flow
 */

import axios from 'axios';
import { getEnvironmentConfig } from '../dist/utils/env.js';
import { decodeJWTUnsafe } from '../dist/utils/jwt-utils.js';
import fs from 'fs';
import path from 'path';

/**
 * Load configuration from token file and .env
 */
function loadConfiguration() {
  const env = getEnvironmentConfig();
  
  // Load token from file
  let tokenData = null;
  try {
    const tokenFile = path.join(process.cwd(), 'mcp-mpc-odi.token.json');
    const tokenContent = fs.readFileSync(tokenFile, 'utf8');
    tokenData = JSON.parse(tokenContent);
  } catch (error) {
    console.error('⚠️ Could not load token file:', error.message);
  }

  return {
    JWT_TOKEN: tokenData?.access_token || '',
    KEYCLOAK_BASE_URL: env.KEYCLOAK_BASE_URL || 'https://keycloak.ingka-dt.cn/auth',
    KEYCLOAK_REALM: env.KEYCLOAK_REALM || 'master',
    TOKEN_DATA: tokenData
  };
}

const CONFIG = loadConfiguration();

/**
 * Extract Keycloak session info from JWT
 */
function extractKeycloakSessionInfo(jwtToken) {
  try {
    const payload = decodeJWTUnsafe(jwtToken);
    return {
      userId: payload.sub,
      sessionId: payload.sid,
      email: payload.email,
      issuer: payload.iss,
      expiresAt: new Date(payload.exp * 1000)
    };
  } catch (error) {
    console.error('Failed to extract session info:', error);
    return null;
  }
}

/**
 * Step 1: Start OAuth2 flow with permission-service client
 */
async function startOAuth2Flow(sessionInfo) {
  console.log('🔄 ==================== STEP 1: START OAUTH2 FLOW ====================');
  
  try {
    console.log('🔄 Starting OAuth2 flow with permission-service client...');
    
    // Build Keycloak session cookies from JWT session info
    const keycloakCookies = [
      `AUTH_SESSION_ID=${sessionInfo.sessionId}.keycloak-dev-857f5d85dc-zkl9k-27830`,
      `AUTH_SESSION_ID_LEGACY=${sessionInfo.sessionId}.keycloak-dev-857f5d85dc-zkl9k-27830`,
      `KEYCLOAK_LOCALE=en`,
      `KEYCLOAK_SESSION="master/${sessionInfo.userId}/${sessionInfo.sessionId}"`,
      `KEYCLOAK_SESSION_LEGACY="master/${sessionInfo.userId}/${sessionInfo.sessionId}"`
    ].join('; ');
    
    // Step 1: Auth request to Keycloak
    const authUrl = `${CONFIG.KEYCLOAK_BASE_URL}/realms/${CONFIG.KEYCLOAK_REALM}/protocol/openid-connect/auth`;
    const state = generateRandomState();
    
    const authParams = new URLSearchParams({
      response_type: 'code',
      client_id: 'permission-service',
      redirect_uri: 'https://api-dev-mpp-fe.ingka-dt.cn/prm-auth/auth/loginComplete',
      state: state,
      kc_idp_hint: 'PermissionCenterServerIDP',
      scope: 'openid',
      login: 'true'
    });
    
    const fullAuthUrl = `${authUrl}?${authParams.toString()}`;
    
    console.log(`   Auth URL: ${fullAuthUrl}`);
    console.log(`   State: ${state}`);
    console.log(`   Session cookies: ${keycloakCookies.substring(0, 100)}...`);
    
    const authResponse = await axios.get(fullAuthUrl, {
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Cookie': keycloakCookies,
        'DNT': '1',
        'Pragma': 'no-cache',
        'Referer': 'https://admin.ingka-dt.cn/',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-site',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"'
      },
      maxRedirects: 0,
      validateStatus: () => true // Accept any status code
    });
    
    console.log(`   Response status: ${authResponse.status}`);

    if ((authResponse.status === 302 || authResponse.status === 303) && authResponse.headers.location) {
      const redirectUrl = authResponse.headers.location;
      console.log('✅ Got redirect to loginComplete');
      console.log(`   Redirect URL: ${redirectUrl}`);
      
      // Check if this is a broker redirect or direct loginComplete redirect
      if (redirectUrl.includes('/broker/')) {
        console.log('🔄 Got broker redirect - following IDP flow');
        console.log(`   Broker URL: ${redirectUrl}`);

        // Follow the broker redirect
        const brokerResult = await followBrokerRedirect(redirectUrl);
        return brokerResult;
      } else {
        // Extract authorization code from direct redirect URL
        const urlParams = new URLSearchParams(redirectUrl.split('?')[1]);
        const code = urlParams.get('code');
        const sessionState = urlParams.get('session_state');

        if (code) {
          console.log(`   Authorization code: ${code.substring(0, 50)}...`);
          console.log(`   Session state: ${sessionState}`);

          return {
            code,
            state,
            sessionState,
            redirectUrl
          };
        }
      }
    }
    
    console.log('❌ Failed to get authorization code');
    return null;
    
  } catch (error) {
    console.error('❌ OAuth2 flow failed:', error.message);
    return null;
  }
}

/**
 * Follow broker redirect for IDP authentication
 */
async function followBrokerRedirect(brokerUrl) {
  try {
    console.log('🔄 Following broker redirect...');

    const response = await axios.get(brokerUrl, {
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'DNT': '1',
        'Pragma': 'no-cache',
        'Referer': 'https://admin.ingka-dt.cn/',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-site',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
      },
      maxRedirects: 0,
      validateStatus: () => true
    });

    console.log(`   Broker response status: ${response.status}`);

    if ((response.status === 302 || response.status === 303) && response.headers.location) {
      const finalRedirect = response.headers.location;
      console.log(`   Final redirect: ${finalRedirect}`);

      // Check if this is the loginComplete redirect
      if (finalRedirect.includes('loginComplete')) {
        const urlParams = new URLSearchParams(finalRedirect.split('?')[1]);
        const code = urlParams.get('code');
        const sessionState = urlParams.get('session_state');
        const state = urlParams.get('state');

        if (code) {
          console.log(`   Authorization code: ${code.substring(0, 50)}...`);
          console.log(`   Session state: ${sessionState}`);

          return {
            code,
            state,
            sessionState,
            redirectUrl: finalRedirect
          };
        }
      }
    }

    console.log('❌ Broker redirect did not result in authorization code');
    return null;

  } catch (error) {
    console.error('❌ Broker redirect failed:', error.message);
    return null;
  }
}

/**
 * Step 2: Complete login and get pm_user_token
 */
async function completeLogin(authResult) {
  console.log('\n🔄 ==================== STEP 2: COMPLETE LOGIN ====================');
  
  try {
    console.log('🔄 Completing login to get pm_user_token...');
    
    const loginCompleteUrl = authResult.redirectUrl;
    console.log(`   Login complete URL: ${loginCompleteUrl}`);
    
    const response = await axios.get(loginCompleteUrl, {
      headers: {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'cache-control': 'no-cache',
        'dnt': '1',
        'pragma': 'no-cache',
        'priority': 'u=0, i',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-site',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
      },
      maxRedirects: 0,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    
    if (response.status === 302 && response.headers.location) {
      const finalRedirect = response.headers.location;
      console.log('✅ Got final redirect with pm_user_token');
      console.log(`   Final redirect: ${finalRedirect}`);
      
      // Extract pm_user_token from URL
      const urlParams = new URLSearchParams(finalRedirect.split('?')[1]);
      const pmUserToken = urlParams.get('pm_user_token');
      const bizOriginUrl = urlParams.get('biz_origin_url');
      const code = urlParams.get('code');
      
      if (pmUserToken) {
        console.log(`   pm_user_token: ${pmUserToken}`);
        console.log(`   biz_origin_url: ${bizOriginUrl}`);
        console.log(`   code: ${code}`);
        
        // Check Set-Cookie headers for additional session info
        const setCookieHeaders = response.headers['set-cookie'] || [];
        console.log('   Set-Cookie headers:');
        setCookieHeaders.forEach(cookie => {
          console.log(`     ${cookie}`);
        });
        
        return {
          pmUserToken,
          bizOriginUrl,
          code,
          finalRedirect,
          cookies: setCookieHeaders
        };
      }
    }
    
    console.log('❌ Failed to get pm_user_token');
    return null;
    
  } catch (error) {
    console.error('❌ Login completion failed:', error.message);
    return null;
  }
}

/**
 * Step 3: Activate session and get final cookie
 */
async function activateSession(loginResult) {
  console.log('\n🔄 ==================== STEP 3: ACTIVATE SESSION ====================');
  
  try {
    console.log('🔄 Activating session with pm_user_token...');
    
    const currentUrl = loginResult.finalRedirect;
    console.log(`   Current URL: ${currentUrl}`);
    
    const response = await axios.get(currentUrl, {
      headers: {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'cache-control': 'no-cache',
        'dnt': '1',
        'pragma': 'no-cache',
        'priority': 'u=0, i',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-site',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
      },
      maxRedirects: 0,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    
    // Check for final session cookie
    const setCookieHeaders = response.headers['set-cookie'] || [];
    console.log('   Final Set-Cookie headers:');
    setCookieHeaders.forEach(cookie => {
      console.log(`     ${cookie}`);
    });
    
    // Look for test_orders-portal cookie
    const sessionCookie = setCookieHeaders.find(cookie => 
      cookie.includes('test_orders-portal=')
    );
    
    if (sessionCookie) {
      const cookieValue = sessionCookie.split('=')[1].split(';')[0];
      console.log('✅ Got final session cookie!');
      console.log(`   Session cookie: test_orders-portal=${cookieValue}`);
      
      return {
        sessionCookie: `test_orders-portal=${cookieValue}`,
        pmUserToken: loginResult.pmUserToken,
        allCookies: setCookieHeaders
      };
    } else {
      console.log('⚠️ No test_orders-portal cookie found, using pm_user_token directly');
      return {
        sessionCookie: `test_orders-portal=${loginResult.pmUserToken}`,
        pmUserToken: loginResult.pmUserToken,
        allCookies: setCookieHeaders
      };
    }
    
  } catch (error) {
    console.error('❌ Session activation failed:', error.message);
    return null;
  }
}

/**
 * Generate random state for OAuth2
 */
function generateRandomState() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Main function
 */
async function mimicOAuth2Flow() {
  console.log('🔄 ==================== OAUTH2 FLOW MIMIC ====================');
  console.log('🎯 Purpose: Mimic browser OAuth2 flow to get valid pm_user_token\n');
  
  console.log('📋 Configuration:');
  console.log(`   Token File: ${CONFIG.TOKEN_DATA ? 'Loaded ✅' : 'Missing ❌'}`);
  console.log(`   JWT Token: ${CONFIG.JWT_TOKEN ? CONFIG.JWT_TOKEN.substring(0, 50) + '...' : 'Not available ❌'}`);
  console.log('');
  
  if (!CONFIG.JWT_TOKEN) {
    console.log('❌ No JWT token available. Please update mcp-mpc-odi.token.json');
    return;
  }
  
  // Extract session info from JWT
  const sessionInfo = extractKeycloakSessionInfo(CONFIG.JWT_TOKEN);
  if (!sessionInfo) {
    console.log('❌ Failed to extract session info from JWT');
    return;
  }
  
  console.log('📊 Session info from JWT:');
  console.log(`   User ID: ${sessionInfo.userId}`);
  console.log(`   Session ID: ${sessionInfo.sessionId}`);
  console.log(`   Email: ${sessionInfo.email || 'N/A'}`);
  console.log('');
  
  // Step 1: Start OAuth2 flow
  const authResult = await startOAuth2Flow(sessionInfo);
  if (!authResult) {
    console.log('❌ Failed to start OAuth2 flow');
    return;
  }
  
  // Step 2: Complete login
  const loginResult = await completeLogin(authResult);
  if (!loginResult) {
    console.log('❌ Failed to complete login');
    return;
  }
  
  // Step 3: Activate session
  const sessionResult = await activateSession(loginResult);
  if (!sessionResult) {
    console.log('❌ Failed to activate session');
    return;
  }
  
  console.log('\n🏁 ==================== OAUTH2 FLOW COMPLETE ====================');
  console.log('✅ OAuth2 flow completed successfully!');
  console.log('');
  console.log('🎯 **Results:**');
  console.log(`   pm_user_token: ${sessionResult.pmUserToken}`);
  console.log(`   Session cookie: ${sessionResult.sessionCookie}`);
  console.log('');
  console.log('🧪 **Test the cookie:**');
  console.log(`curl 'https://fe-dev-i.ingka-dt.cn/order-web/orders/search' \\`);
  console.log(`  -H 'content-type: application/json' \\`);
  console.log(`  -b '${sessionResult.sessionCookie}' \\`);
  console.log(`  --data-raw '{"storeIds":[],"baseSize":10,"page":1,"size":10,"timestamp":${Date.now()}}'`);
}

// Run the OAuth2 flow mimic if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  mimicOAuth2Flow().catch(error => {
    console.error('💥 OAuth2 flow failed:', error);
    process.exit(1);
  });
}

export { mimicOAuth2Flow };
