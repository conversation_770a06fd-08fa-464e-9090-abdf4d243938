/**
 * Project Name:common
 * File Name:KeycloakConfiguration.java
 * Package Name:com.ikea.mas.permission.config
 * Date:May 3, 20244:28:31 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.mas.permission.config;

import java.util.concurrent.TimeUnit;

import org.apache.hc.client5.http.ConnectionKeepAliveStrategy;
import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.config.ConnectionConfig;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.io.HttpClientConnectionManager;
import org.apache.hc.core5.http.HeaderElement;
import org.apache.hc.core5.http.message.BasicHeaderElementIterator;
import org.apache.hc.core5.util.TimeValue;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestClient;
import org.zalando.logbook.Logbook;
import org.zalando.logbook.spring.LogbookClientHttpRequestInterceptor;

import com.ikea.mas.permission.config.prop.KeycloakIdpProperties;
import com.ikea.mas.permission.config.prop.KeycloakProperties;

import lombok.extern.slf4j.Slf4j;

/**
 * ClassName:KeycloakConfiguration <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     May 3, 2024 4:28:31 PM <br/>
 * <AUTHOR>	 
 */
@EnableConfigurationProperties(value = {
		KeycloakProperties.class,
		KeycloakIdpProperties.class})
@Configuration
@Slf4j
public class KeycloakConfiguration {
	
	private int maxTotalConnect = 10; //连接池的最大连接数
    private int maxConnectPerRoute = 10; //单个主机的最大连接数
    private int timeToLive = 2; // 连接最大存活时间120s
    private int connectTimeout = 5000; //连接超时5s
    private int readTimeout = 5000; //读取超时5s
    
    private int keepAliveTime = 60; //默认长链接时间60s
	
	private ConnectionConfig connectionConfig() {
		return ConnectionConfig.custom().setConnectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
				.setSocketTimeout(readTimeout, TimeUnit.MILLISECONDS)
				.setTimeToLive(timeToLive, TimeUnit.SECONDS)
				.build();
	}
	
	private HttpClientConnectionManager httpClientConMgr() {
		PoolingHttpClientConnectionManager httpClientConMgr = new PoolingHttpClientConnectionManager();
		httpClientConMgr.setMaxTotal(maxTotalConnect);
		httpClientConMgr.setDefaultMaxPerRoute(maxConnectPerRoute);
		httpClientConMgr.setDefaultConnectionConfig(connectionConfig());
		return httpClientConMgr;
	}
	
	private ConnectionKeepAliveStrategy connectionKeepAliveStrategy() {
		return (response, context) -> {
			BasicHeaderElementIterator it = new BasicHeaderElementIterator(response.headerIterator("Keep-Alive"));
			while (it.hasNext()) {
				HeaderElement he = it.next();
				String value = he.getValue();
				if (value != null && "timeout".equalsIgnoreCase(he.getName())) {
					try {
						return TimeValue.ofSeconds(Long.valueOf(value));
					} catch (NumberFormatException ignore) {
						log.error("解析长连接过期时间异常", ignore);
					}
				}
			}
			// 长连接保持时间
			return TimeValue.ofSeconds(keepAliveTime);
		};
	}
	
	private ClientHttpRequestFactory clientHttpRequestFactory() {
		HttpClient httpClient = HttpClientBuilder.create()
				.setConnectionManager(httpClientConMgr())
				.setKeepAliveStrategy(connectionKeepAliveStrategy())
				.evictExpiredConnections()
				.evictIdleConnections(TimeValue.ofMinutes(1))
                .build();
        return new HttpComponentsClientHttpRequestFactory(httpClient);
	}
	
	@Bean
	public RestClient kcAuthRestClient(RestClient.Builder builder, Logbook logbook, KeycloakProperties keycloakConfig) {
		RestClient restClient = builder.baseUrl(keycloakConfig.getAuthServerUrl())
				.requestFactory(clientHttpRequestFactory())
				.requestInterceptor(new LogbookClientHttpRequestInterceptor(logbook))
				.build();
		return restClient;
	}

}