/**
 * Project Name:backend
 * File Name:RoleRepository.java
 * Package Name:com.ikea.digital.permission.server.resource.domain.repository
 * Date:Jun 12, 20243:42:26 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.ikea.digital.permission.server.common.BkdConstants;
import com.ikea.digital.permission.server.common.OptTypeEnum;
import com.ikea.digital.permission.server.common.util.JacksonUtils;
import com.ikea.digital.permission.server.common.util.KeycloakManager;
import com.ikea.digital.permission.server.dto.RoleCreateOrUpdateDto;
import com.ikea.digital.permission.server.exception.ResourceException;
import com.ikea.digital.permission.server.resource.domain.model.Role;
import com.ikea.digital.permission.server.resource.domain.model.persistent.LogPO;

/**
 * ClassName:RoleRepository <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jun 12, 2024 3:42:26 PM <br/>
 * <AUTHOR>	 
 */
@Repository
public class RoleRepository {
	
	@Autowired
	private KeycloakManager keycloakManager;
	
	@Autowired
	private LogRepository logRepository;
	
	public List<Role> getRoles(String clientId) {
		String clientPkId = keycloakManager.getClientPkId(clientId);
		return keycloakManager.getRoles(clientPkId);
	}
	
	public Optional<Role> getRoleById(String clientId, String roleId) {
		return keycloakManager.getRoleById(clientId, roleId);
	}
	
	public List<Role> getRolesByClientPK(String clientPkId) {
		return keycloakManager.getRoles(clientPkId);
	}
	
	public List<String> getMenuIds(String roleName, String clientId) {
		String clientPkId = keycloakManager.getClientPkId(clientId);
		
		Role role = keycloakManager.preciseSearchRole(clientPkId, roleName);
		if(role == null) {
			throw new ResourceException("role:" + roleName + " 不存在!");
		}
		return Optional.ofNullable(role.getAttrMap()).map(attrMap -> attrMap.get(BkdConstants.ATTR_MENU_ID)).orElse(new ArrayList<>());
	}
	
	public void createRole(RoleCreateOrUpdateDto dto) {
		String clientPkId = keycloakManager.getClientPkId(dto.getClientId());
		Role role = keycloakManager.preciseSearchRole(clientPkId, dto.getName());
		if(role != null) {
			throw new ResourceException(dto.getName() + "[已存在！]");
		}
		keycloakManager.createRole(clientPkId,dto.getName(),dto.getDescription(),dto.getMenuIds());
		logRepository.insert(new LogPO(OptTypeEnum.CREATE_ROLE.getCode(), dto.getName(), JacksonUtils.object2JsonString(dto),
				null, null));
	}
	
	public void updateRoleMenuIds(String roleName, String clientId, List<String> menuIds) {
		String clientPkId = keycloakManager.getClientPkId(clientId);
		
		Role role = keycloakManager.preciseSearchRole(clientPkId, roleName);
		if(role == null) {
			throw new ResourceException("role:" + roleName + " 不存在!");
		}
		
		keycloakManager.updateRole(clientPkId, roleName, role.getDescription(), menuIds);
		
		RoleCreateOrUpdateDto cmd = new RoleCreateOrUpdateDto();
		cmd.setClientId(clientId);
		cmd.setDescription(role.getDescription());
		cmd.setMenuIds(menuIds);
		cmd.setName(roleName);
		
		logRepository.insert(new LogPO(OptTypeEnum.EDIT_ROLE.getCode(),	roleName, JacksonUtils.object2JsonString(cmd),
				JacksonUtils.object2JsonString(role), null));
	}
	
	public List<Role> getRolesWithMenuId(String clientId, Long menuId) {
		String clientPkId = keycloakManager.getClientPkId(clientId);
		if(StringUtils.isBlank(clientPkId)) {
			return Collections.emptyList();
		}
		
		List<Role> roles = keycloakManager.getRoles(clientPkId);
		if(CollectionUtils.isEmpty(roles)) {
			return Collections.emptyList();
		}
		
		return roles.stream().filter(role -> {
			Map<String,List<String>> arrtMap = role.getAttrMap();
			if(MapUtils.isEmpty(arrtMap)) {
				return false;
			}
			
			List<String> menuIds = arrtMap.get(BkdConstants.ATTR_MENU_ID);
			if(CollectionUtils.isEmpty(menuIds)) {
				return false;
			}
			
			if(menuIds.contains(String.valueOf(menuId))) {
				return true;
			}
			return false;
		}).collect(Collectors.toList());
	}
	
	/**
	 * 将当前menuId添加到role的menuIds中，如果当前menuId的关联menuIds不在role的menuIds中，也需要被添加
	 * @param roleName
	 * @param clientId
	 * @param menuId
	 * @param relationMenuIds 当前menu的parentIds和subIds
	 */
	public void addMenuIdToRole(String roleName, String clientId, String menuId, List<Long> relationMenuIds) {
		String clientPkId = keycloakManager.getClientPkId(clientId);
		Role role = keycloakManager.preciseSearchRole(clientPkId, roleName);
		if(Objects.isNull(role)) {
			throw new ResourceException("role:" + roleName + " 不存在!");
		}
		// 现有的menuId列表
		List<String> menuIds = Optional.ofNullable(role.getAttrMap()).map(attrMap -> attrMap.get(BkdConstants.ATTR_MENU_ID)).orElse(new ArrayList<>());
		
		if(menuIds.contains(menuId)) {
			return;
		}
		
		List<String> notContainMenuIds = relationMenuIds.stream().map(String::valueOf)
				.filter(id -> !menuIds.contains(id)).collect(Collectors.toList());
		
		menuIds.add(menuId);
		if(CollectionUtils.isNotEmpty(notContainMenuIds)) {
			menuIds.addAll(notContainMenuIds);
		}
		
		keycloakManager.updateRole(clientPkId, role.getName(), role.getDescription(), menuIds);
		
		RoleCreateOrUpdateDto cmd = new RoleCreateOrUpdateDto();
		cmd.setClientId(clientId);
		cmd.setDescription(role.getDescription());
		cmd.setName(role.getName());
		cmd.setMenuIds(menuIds);
		
		logRepository.insert(new LogPO(OptTypeEnum.EDIT_ROLE.getCode(),	role.getName(), JacksonUtils.object2JsonString(cmd),
				JacksonUtils.object2JsonString(role), null));
	}

	public void deleteRole(String roleId) {
		keycloakManager.deleteRole(roleId);
	}

}