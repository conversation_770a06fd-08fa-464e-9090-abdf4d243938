/**
 * Project Name:permission-server
 * File Name:UserAssignRoleDto.java
 * Package Name:com.ikea.digital.permission.server.dto
 * Date:Dec 4, 20235:56:35 PM
 * Copyright (c) 2023, theodore.ni All Rights Reserved.
 */
package com.ikea.digital.permission.server.dto;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import com.ikea.digital.permission.server.common.BkdConstants;
import com.ikea.digital.permission.server.dto.UserGrantRoleDto.RoleStoreDto;
import com.ikea.digital.permission.server.exception.ResourceException;

import lombok.Data;

/**
 * ClassName:UserAssignRoleDto <br/>
 * Function: 角色分配Service层对象. <br/>
 * Date:     Dec 4, 2023 5:56:35 PM <br/>
 * <AUTHOR>	 
 */
@Data
public class UserAssignRoleDto {

	String userId;
	
	String clientId;

	List<RoleStoreDto> addedList;
	
	List<RoleStoreDto> removedList;
	
	/** 
	 * 用户在keycloak中拥有的当前client的role
	 */
	List<String> kcRoleList;
	
	/**
	 * 用户在permission service中拥有的当前client的role和门店
	 */
	Map<String,List<String>> pmRoleStoreMap;
	
	public void validate() {
		if(CollectionUtils.isEmpty(kcRoleList)) {
			throw new ResourceException(401,"您无当前客户端的任何角色");
		}
		
		List<RoleStoreDto> roleStores = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(addedList)) {
			addedList.forEach(role -> {
				if(BkdConstants.SysRole.contains(role.getName())) {
					role.setStoreIds(Collections.singletonList(BkdConstants.DEFAULT_STORE));
				}
			});
			roleStores.addAll(addedList);
		}
		if(CollectionUtils.isNotEmpty(removedList)) {
			removedList.forEach(role -> {
				if(BkdConstants.SysRole.contains(role.getName())) {
					role.setStoreIds(Collections.singletonList(BkdConstants.DEFAULT_STORE));
				}
			});
			roleStores.addAll(removedList);
		}
		
		if(CollectionUtils.isEmpty(roleStores)) {
			return;
		}
		
		if(kcRoleList.contains(BkdConstants.AppAdmin)) {
			return;
		}
		
		if(MapUtils.isEmpty(pmRoleStoreMap)) {
			throw new ResourceException(401,"您无分配相关角色的权限");
		}
		
		List<String> assignRoles = roleStores.stream().map(RoleStoreDto::getName).collect(Collectors.toList());
		if(!kcRoleList.containsAll(assignRoles)) {
			throw new ResourceException(401,"您无分配相关角色的权限");
		}
		
		roleStores.stream().forEach(roleStore -> {
			String roleName = roleStore.getName();
			List<String> hasStores = pmRoleStoreMap.get(roleName);
			if(CollectionUtils.isEmpty(hasStores)) {
				throw new ResourceException(401,"您无任何门店的权限");
			}
			if(!hasStores.containsAll(roleStore.getStoreIds())) {
				throw new ResourceException(401,"您无分配相关门店的权限");
			}
		});
	}
	
}