/**
 * Project Name:permission-server
 * File Name:ClientValidator.java
 * Package Name:com.ikea.digital.permission.server.infrastructure.validator
 * Date:Mar 19, 20246:10:24 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.validator;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ikea.mas.permission.config.prop.PermissionServerProperties;
import com.ikea.mas.permission.config.prop.PermissionServerProperties.BizSystemInfo;


/**
 * ClassName:ClientValidator <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Mar 19, 2024 6:10:24 PM <br/>
 * <AUTHOR>	 
 */
@Component
public class ClientValidator {
	
	@Autowired
	private PermissionServerProperties pmServerConfig;
	
	public boolean validateAccess(String clientId,String clientSecret) {
    	for(BizSystemInfo biz : pmServerConfig.getBizSystemList()) {
    		if(biz.validateAccess(clientId, clientSecret)) {
				return true;
			}
		}
    	return false;
    }

}