package com.ikea.digital.permission.server.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class GroupCreateOrUpdateDto {
	
	@NotNull(groups = UpdateValidGroup.class)
	String id;
	
	@NotEmpty(message = "组名不能为空！")
	@Size(max = 20, min = 2, message = "组名长度需在(2-20)之间")
	String name;
	
	String parentGroupId;
	
}