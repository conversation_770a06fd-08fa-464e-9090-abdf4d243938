/**
 * Project Name:permission-server
 * File Name:ImportMenu.java
 * Package Name:com.ikea.digital.permission.server.resource.domain.model
 * Date:Sep 25, 20232:31:49 PM
 * Copyright (c) 2023, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.dto;

import lombok.Data;

/**
 * ClassName:ImportMenuDto <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Sep 25, 2023 2:31:49 PM <br/>
 * <AUTHOR>	 
 */
@Data
public class ImportMenuDto {
	
	private String name;
	
	private String desc;
	
	private String path = "/";
	
	private String icon;
	
	private Integer menuType = 2;
	
	private Integer sortIndex = 1;
	
	public MenuCreateOrUpdateDto toCmd(String clientId) {
		MenuCreateOrUpdateDto cmd = new MenuCreateOrUpdateDto();
		cmd.setClientId(clientId);
		cmd.setDescription(this.desc);
		cmd.setMenuType(this.menuType);
		cmd.setName(this.name);
		cmd.setPath(this.path);
		cmd.setOrder(this.sortIndex);
		return cmd;
	}

}