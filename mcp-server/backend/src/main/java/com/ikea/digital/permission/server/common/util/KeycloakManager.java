package com.ikea.digital.permission.server.common.util;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.keycloak.admin.client.resource.RoleMappingResource;
import org.keycloak.admin.client.resource.RoleScopeResource;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.representations.idm.ClientMappingsRepresentation;
import org.keycloak.representations.idm.ClientRepresentation;
import org.keycloak.representations.idm.GroupRepresentation;
import org.keycloak.representations.idm.MappingsRepresentation;
import org.keycloak.representations.idm.RoleRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ikea.digital.permission.server.common.BkdConstants;
import com.ikea.digital.permission.server.resource.domain.model.Client;
import com.ikea.digital.permission.server.resource.domain.model.Group;
import com.ikea.digital.permission.server.resource.domain.model.Role;
import com.ikea.digital.permission.server.resource.domain.model.User;
import com.ikea.mas.permission.config.prop.KeycloakProperties;

@Component
public class KeycloakManager {
	
	@Autowired
	private KeycloakClient kClient;
	
	@Autowired
	private KeycloakProperties keycloakProperties;
	
	//============================== Client ===============================
	public String getClientPkId(String clientId) {
		return kClient.getClientPkId(clientId);
	}
	
	public List<Client> fetchClientList(){
		List<ClientRepresentation> kcClients = kClient.clientList();
		return kcClients.stream().map(Client::new).collect(Collectors.toList());
	}
	
	//============================== Role ===============================
	public void createRole(String clientId, String name, String description, List<String> menuIds) {
		RoleRepresentation kcRole = new RoleRepresentation();
		kcRole.setClientRole(true);
		kcRole.setName(name);
		kcRole.setDescription(description);
		if(CollectionUtils.isNotEmpty(menuIds)) {
			Map<String,List<String>> attr = new HashMap<>();
			attr.put("menuIds", menuIds);
			kcRole.setAttributes(attr);
		}
		kClient.createRole(clientId,kcRole);
	}
	
	public void updateRole(String clientId, String name, String description, List<String> menuIds) {
		RoleRepresentation kcRole = new RoleRepresentation();
		kcRole.setClientRole(true);
		kcRole.setName(name);
		kcRole.setDescription(description);
		Map<String,List<String>> attr = new HashMap<>();
		attr.put(BkdConstants.ATTR_MENU_ID, menuIds);
		kcRole.setAttributes(attr);
		kClient.updateRole(clientId, kcRole);
	}
	
	public void deleteRole(String roleId) {
		kClient.deleteRole(roleId);
	}
	
	public Role preciseSearchRole(String clientId,String name) {
		RoleRepresentation kcRole = kClient.preciseSearchRole(clientId, name);
		if(kcRole == null) {
			return null;
		}
		return new Role(kcRole);
	}
	
	public List<Role> searchRole(String clientId,String name) {
		List<RoleRepresentation> kcRoles = kClient.searchRole(clientId, name);
		if(CollectionUtils.isEmpty(kcRoles)) {
			return Collections.emptyList();
		}
		return kcRoles.stream().map(Role::new).collect(Collectors.toList());
	}
	
	public Optional<Role> getRole(String clientId,String name) {
		RoleRepresentation kcRole = kClient.getRole(clientId, name);
		if(Objects.isNull(kcRole)) {
			return null;
		}
		return Optional.ofNullable(kcRole).map(Role::new);
	}
	
	public Optional<Role> getRoleById(String clientId, String roleId) {
		RoleRepresentation kcRole = kClient.getRoleById(roleId);
		String clientPkId = kClient.getClientPkId(clientId);
		if(Objects.isNull(kcRole) 
				|| StringUtils.isBlank(clientPkId)
				|| !clientPkId.equals(kcRole.getContainerId())) {
			return null;
		}
		return Optional.ofNullable(kcRole).map(Role::new);
	}
	
	public List<Role> getRoles(String clientPkId) {
		List<RoleRepresentation> kcRoles = kClient.getRoles(clientPkId);
		if(CollectionUtils.isEmpty(kcRoles)) {
			return Collections.emptyList();
		}
		return kcRoles.stream().map(Role::new).collect(Collectors.toList());
	}
	
	//============================== User ===============================
	public String createUser(String name,String email,Boolean enable) {
		UserRepresentation user = new UserRepresentation();
        user.setUsername(email);
        user.setEmail(email);
        user.setEnabled(enable);
        user.setFirstName(name);
		return kClient.createUser(user);
	}
	
	public void updateUser(String userId, String name, Boolean enable) {
		UserRepresentation user = new UserRepresentation();
		user.setId(userId);
		user.setEnabled(enable);
		user.setFirstName(name);
		kClient.updateUser(user);
	}
	
	public void deleteUser(String userId) {
		kClient.deleteUser(userId);
	}
	
	public User getUserSimpleInfoById (String userId) {
		UserRepresentation kcUser = kClient.getUserById(userId);
		if(kcUser == null) {
			return null;
		}
		return new User(kcUser);
	}
	
	public User getUserById(String userId) {
		UserResource us = kClient.getUserRsById(userId);
		UserRepresentation kcUser = us.toRepresentation();
		// query clientRole
		Map<String,List<Role>> clientRoleMap = new HashMap<>();
		clientRoleMap.putAll(this.fetchClientRoles(us.roles()));
		// query group/ groupRole
		List<GroupRepresentation> userGroups =  us.groups();
		if(CollectionUtils.isNotEmpty(userGroups)) {
			List<String> groups = userGroups.stream().map(GroupRepresentation::getPath).collect(Collectors.toList());
			kcUser.setGroups(groups);
			
			List<String> userGroupIds = userGroups.stream().map(GroupRepresentation::getId).collect(Collectors.toList());
			for(String groupId : userGroupIds){ 
				RoleMappingResource roleMappingRs = kClient.getGroupClientRoleMapping(groupId);
				Map<String, List<Role>> groupClientRoles = this.fetchClientRoles(roleMappingRs);
				clientRoleMap = MapUtils.unionMap(clientRoleMap, groupClientRoles);
			}
		}
		
		// merge
		Map<String,List<String>> clientRoles = new HashMap<>();
		clientRoleMap.entrySet().forEach(entry -> {
			clientRoles.put(entry.getKey(), entry.getValue().stream().map(Role::getName).collect(Collectors.toList()));
		});
		kcUser.setClientRoles(clientRoles);
		User user = new User(kcUser);
		user.setClientRoleMap(clientRoleMap);
		return user;
	}
	 
	public List<User> searchUserByUsername(String userName,Integer first,Integer max) {
		List<UserRepresentation> kcUser = kClient.searchUserByUsername(userName,first,max);
		if(CollectionUtils.isEmpty(kcUser)) {
			return Collections.emptyList();
		}
		return kcUser.stream().map(ku -> {ku.setLastName(null); return new User(ku);}).collect(Collectors.toList());
	}
	
	public Optional<User> searchSingleUserByUsername(String userName) {
		List<UserRepresentation> kcUser = kClient.searchUserByUsername(userName,0,1);
		if(CollectionUtils.isEmpty(kcUser)) {
			return Optional.empty();
		}
		return kcUser.stream().map(ku -> {return new User(ku);}).findFirst();
	}
	
	public int searchUserCountByUsername(String userName) {
		return kClient.searchUserCountByUsername(userName);
	}
	
	public List<User> searchUserByRoleName(String clientId,String roleName) {
		return searchUserByRoleName(clientId, roleName, null, null);
	}
	
	public List<User> searchUserByRoleName(String clientId, String roleName, 
			Integer first, Integer max) {
		List<UserRepresentation> kcUser = kClient.searchUserByRoleName(clientId, roleName, first, max);
		if(CollectionUtils.isEmpty(kcUser)) {
			return Collections.emptyList();
		}
		return kcUser.stream().map(ku -> {ku.setLastName(null); return new User(ku);}).collect(Collectors.toList());
	}
	
	private Map<String,List<Role>> fetchClientRoles(RoleMappingResource roleMappingRs) {
		Map<String,List<Role>> result = new HashMap<>();
		try {
			MappingsRepresentation mappingRepresentation = roleMappingRs.getAll();
			Map<String, ClientMappingsRepresentation> roleMap = mappingRepresentation.getClientMappings();
			if(roleMap == null) {
				return result;
			}
		
			for(Entry<String, ClientMappingsRepresentation> entry : roleMap.entrySet()) {
				String clientId = entry.getKey();
				ClientMappingsRepresentation clientRoleObj = entry.getValue();
				if(clientRoleObj != null) {
					List<RoleRepresentation> clientRoleList = clientRoleObj.getMappings();
					List<Role> roles = clientRoleList.stream().map(clientRole -> new Role(clientRole)).collect(Collectors.toList());
					result.put(clientId, roles);
				}
			}
			return result;
		}catch(jakarta.ws.rs.NotFoundException ex) {
			return result;
		}
	}
	
	public List<String> leaveGroup(String userId) {
		UserResource userRs = kClient.getUserRsById(userId);
		List<GroupRepresentation> userGroups = userRs.groups();
		List<String> oldGroupIds = userGroups.stream().map(GroupRepresentation::getId).collect(Collectors.toList());
		oldGroupIds.forEach(groupId -> userRs.leaveGroup(groupId));
		return oldGroupIds;
	}
	
	public void joinGroup(String userId,List<String> groupIds) {
		UserResource userRs = kClient.getUserRsById(userId);
//		List<GroupRepresentation> userGroups = userRs.groups();
//		List<String> oldGroupIds = userGroups.stream().map(GroupRepresentation::getId).collect(Collectors.toList());
//		oldGroupIds.forEach(groupId -> userRs.leaveGroup(groupId));
		
		if(CollectionUtils.isNotEmpty(groupIds)) {
			groupIds.forEach(newGroupId -> {
				userRs.joinGroup(newGroupId);
			});
		}
	}
	
	public void assignUserClientRole(String userId, String clientPkId, List<String> assignRoles, java.util.Collection<String> operatorRoles) {
		List<RoleRepresentation> clientAllRoles = kClient.searchRole(clientPkId,null);
		List<RoleRepresentation> assignKcClientRoles = clientAllRoles.stream().filter(a -> assignRoles.contains(a.getName())).collect(Collectors.toList());
		List<RoleRepresentation> operatorKcClientRoles = clientAllRoles.stream().filter(a -> operatorRoles.contains(a.getName())).collect(Collectors.toList());
		// remove all old
		RoleScopeResource roleScopRs = kClient.getUserRsById(userId).roles().clientLevel(clientPkId);
		roleScopRs.remove(operatorKcClientRoles);
		
		// add new
		if(CollectionUtils.isNotEmpty(assignKcClientRoles)) {
			roleScopRs.add(assignKcClientRoles);
		}
	}
	
	public void removeUserClientRole(String userId, String clientPkId, List<String> roles) {
		List<RoleRepresentation> clientAllRoles = kClient.searchRole(clientPkId,null);
		List<RoleRepresentation> removeKcClientRoles = clientAllRoles.stream().filter(a -> roles.contains(a.getName())).collect(Collectors.toList());
		RoleScopeResource roleScopRs = kClient.getUserRsById(userId).roles().clientLevel(clientPkId);
		roleScopRs.remove(removeKcClientRoles);
		
	}
	
	public void assignUserClientAllRole(String userId, String clientPkId, List<String> assignRoles) {
		List<RoleRepresentation> clientAllRoles = kClient.searchRole(clientPkId,null);
		RoleScopeResource roleScopRs = kClient.getUserRsById(userId).roles().clientLevel(clientPkId);
		roleScopRs.remove(clientAllRoles);
		
		if(CollectionUtils.isNotEmpty(assignRoles)) {
			List<RoleRepresentation> assignKcClientRoles = clientAllRoles.stream().filter(a -> assignRoles.contains(a.getName())).collect(Collectors.toList());
			roleScopRs.add(assignKcClientRoles);
			return;
		}
		roleScopRs.add(clientAllRoles);
	}
	
	public void appendUserClientRole(String userId, String clientPkId, List<String> assignRoles) {
		List<RoleRepresentation> clientAllRoles = kClient.searchRole(clientPkId,null);
		List<RoleRepresentation> assignKcClientRoles = clientAllRoles.stream().filter(a -> assignRoles.contains(a.getName())).collect(Collectors.toList());
		RoleScopeResource roleScopRs = kClient.getUserRsById(userId).roles().clientLevel(clientPkId);
		roleScopRs.add(assignKcClientRoles);
	}
	
	public void addAttr(String userId,Map<String,List<String>> attrMap) {
		UserRepresentation user = new UserRepresentation();
		user.setId(userId);
		user.setAttributes(attrMap);
		kClient.updateUser(user);
	}
	
	//============================== logout ===============================
	public void logout(String userId) {
		kClient.logout(userId);
	}
	
	//============================== Group ===============================
	public void createGroup(String name) {
		GroupRepresentation kcGroup = new GroupRepresentation();
		kcGroup.setName(name);
		kClient.createGroup(kcGroup);
	}
	
	public void createSubGroup(String name, String parentGroupId) {
		GroupRepresentation kcGroup = new GroupRepresentation();
		kcGroup.setName(name);
		kClient.createSubGroup(parentGroupId, kcGroup);
	}
	
	public void updateGroup(String id, String name) {
		GroupRepresentation kcGroup = new GroupRepresentation();
		kcGroup.setId(id);
		kcGroup.setName(name);
		kClient.updateGroup(kcGroup);
	}
	
	public void deleteGroup(String id) {
		kClient.deleteGroup(id);
	}
	
	public Group getGroupById(String groupId) {
		GroupRepresentation kcGroup = kClient.getGroupById(groupId);
		if(kcGroup == null) {
			return null;
		}
		return new Group(kcGroup);
	}
	
	public List<Group> groupList() {
		return kClient.groups().stream().map(Group::new).collect(Collectors.toList());
	}
	
	public List<String> getRoleUsers(String roleName, String clientPkId) {
		List<UserRepresentation> kcUsers = kClient.getRoleUsers(clientPkId, roleName);
		if(CollectionUtils.isEmpty(kcUsers)) {
			return Collections.emptyList();
		}
		List<String> list = new ArrayList<>();
		for (UserRepresentation user : kcUsers) {
			list.add(user.getEmail());
		}
		return list;
	}
	
	public String getLogoutUrl(String clientId, String redirectUrl) {
		StringBuilder strBuilder = new StringBuilder(keycloakProperties.getLogoutUrl());
		strBuilder.append("?client_id=")
			.append(clientId)
			.append("&post_logout_redirect_uri=")
			.append(URLEncoder.encode(redirectUrl, StandardCharsets.UTF_8));
		return strBuilder.toString();
	}
	
	
}	
