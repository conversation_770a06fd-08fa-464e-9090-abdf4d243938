package com.ikea.digital.permission.core;

import java.io.IOException;
import java.util.Base64;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import com.ikea.digital.permission.common.Constants;
import com.ikea.digital.permission.core.config.PermissionClientConfig;
import com.ikea.digital.permission.core.context.UserHolder;
import com.ikea.digital.permission.util.CookieUtils;
import com.ikea.digital.permission.util.HttpUtils;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class PermissionAuthFilter extends OncePerRequestFilter {
	
	final String cookieName;
        
    PermissionClientConfig pmClientConfig;
    
    PermissionSender permissionSender;

    PermissionAuthFilter(PermissionClientConfig pmClientConfig, PermissionSender permissionSender) {
    	this.pmClientConfig = pmClientConfig;
    	this.permissionSender = permissionSender;
        cookieName = pmClientConfig.getEnv() + "_" + pmClientConfig.getClientId();
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        // 是否放行
        String uri = request.getRequestURI();
        if (HttpUtils.exclusivePath(uri, pmClientConfig.getExcludeAuthenticationPath())) {
            log.debug("it's exclusive path, path:{}", uri);
            filterChain.doFilter(request, response);
            return;
        }

        // 如果有token,转成cookie
        String pmUserToken = request.getParameter(Constants.PM_USER_TOKEN);
        if (StringUtils.isNotBlank(pmUserToken) && validateRedirectUrl(request)) {
            log.info("version {} get the pmUserToken, redirect to new url. url: {}", Constants.VERSION, request.getParameter(Constants.BIZ_ORIGIN_URL));
            this.redirect(request, response, pmUserToken, request.getParameter(Constants.BIZ_ORIGIN_URL));
            return;
        }

        // 无cookie则跳登录
        String userCookie = CookieUtils.readCookie(request, cookieName);
        if (StringUtils.isBlank(userCookie)) {
            log.warn("version {} not found user cookie. go to login, cookieName: {}", Constants.VERSION, cookieName);
            permissionSender.toLogin(request, response);
            return;
        }

        // 获取用户信息
        boolean result = permissionSender.fetchUserByCookie(request, response, userCookie);
        if (result) {
            log.info("success to get user info. user name: {}", UserHolder.getUserInfo().getName());
            filterChain.doFilter(request, response);
        } else {
            permissionSender.toLogin(request, response);
        }
    }

    private void redirect(HttpServletRequest request, HttpServletResponse response, String pmUserToken, String originUrl) throws IOException {
        String userToken = new String(Base64.getDecoder().decode(pmUserToken));
        Cookie cookie = CookieUtils.createCookie(cookieName, pmUserToken, HttpUtils.getDomain(pmClientConfig.getCookieDomain(), request.getServerName()), pmClientConfig.getCookiePath(), Integer.parseInt(userToken.split("\\.")[2]), pmClientConfig.getCookieSecure());
        response.addCookie(cookie);
        response.sendRedirect(StringUtils.isBlank(originUrl) ? pmClientConfig.getCallbackUrl() : originUrl);
    }

    private boolean validateRedirectUrl(HttpServletRequest request) {
        String code = request.getParameter("code");
        if(StringUtils.isBlank(code)) {
            log.error("code is blank");
            return false;
        }
        StringBuilder queryString = new StringBuilder()
			.append(Constants.PM_USER_TOKEN).append("=").append(request.getParameter(Constants.PM_USER_TOKEN)).append("&")
			.append(Constants.BIZ_ORIGIN_URL).append("=").append(request.getParameter(Constants.BIZ_ORIGIN_URL));
        String md5 = HttpUtils.hmacString(queryString.toString(), pmClientConfig.getSecret());
        if(code.equals(md5)) {   
            return true;
        }
        log.error("code is not match. code:{}, newCode:{}", code, md5);
        return false;
    }

}