/**
 * Project Name:backend
 * File Name:FrontUserInfoService.java
 * Package Name:com.ikea.digital.permission.server.service
 * Date:Dec 5, 20244:08:59 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.Closure;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.functors.IfClosure;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.ikea.digital.permission.core.config.PermissionClientConfig;
import com.ikea.digital.permission.server.common.BkdConstants;
import com.ikea.digital.permission.server.common.util.ApplicationHolder;
import com.ikea.digital.permission.server.common.util.JacksonUtils;
import com.ikea.digital.permission.server.feign.model.StoreDO;
import com.ikea.digital.permission.server.repository.RoleRepository;
import com.ikea.digital.permission.server.repository.UserRepository;
import com.ikea.digital.permission.server.repository.UserRoleRepository;
import com.ikea.digital.permission.server.resource.domain.model.Role;
import com.ikea.digital.permission.server.resource.domain.model.User;
import com.ikea.digital.permission.server.resource.domain.model.UserRole;
import com.ikea.digital.permission.server.validator.AuthValidator;
import com.ikea.digital.permission.server.vo.ResourceVo;
import com.ikea.digital.permission.server.vo.UserInfoVo;
import com.ikea.mas.permission.common.PmToken;
import com.ikea.mas.permission.config.prop.PermissionServerProperties;
import com.ikea.mas.permission.config.prop.PermissionServerProperties.BizSystemInfo;

/**
 * ClassName:FrontUserInfoService <br/>
 * Function: prm前端页面的用户信息接口. <br/>
 * Date:     Dec 5, 2024 4:08:59 PM <br/>
 * <AUTHOR>	 
 */
@Service
public class FrontUserInfoService {
	
	@Autowired
	private UserRepository userRepository;
	
	@Autowired
	private UserRoleRepository userRoleRepository;
	
	@Autowired
	private RoleRepository roleRepository;
	
	@Autowired
	private MenuService menuService;
	
	@Autowired
	private StoreService storeService;
	
	@Autowired
	private PermissionClientConfig pmClientConfig;
	
	@Autowired
	private PermissionServerProperties pmServerConfig;
	
	@Autowired
	private UserCacheService userCacheService;
	
	@Autowired
	private AuthValidator authValidator;
	
	public UserInfoVo fetchUserByUserToken(String userToken) {
		String clientId = ApplicationHolder.get().equals(BkdConstants.ADMIN_PORTAL)? pmClientConfig.getClientId() : ApplicationHolder.get();
		if (!authValidator.checkToken(userToken, clientId)) {
			return null;
		}
		
		PmToken pmToken = PmToken.from(userToken);
		userCacheService.refreshSsoTime(pmToken.getSsoUUID(), clientId);

		// 获取用户
		String userId = pmToken.getUserId();
		User user = userRepository.fetchUserById(userId);
		// 如果拥有client下的admin权限，则可以分配client下所有role
		changeUserRolesToAllClientRolesIfHaveClientAdmin(user);
		return this.fillDetailInfo(user);
	}
	
	private UserInfoVo fillDetailInfo(final User kcUser) {
		
		UserInfoVo user = new UserInfoVo(kcUser);
		
		// 用户属性填充
		fillAttr(kcUser, user);
		
		String currClientId = ApplicationHolder.get();
		// 填充resourceMap和clientRoleModelMap
		if(pmClientConfig.getClientId().equals(currClientId)) {
			fillResourcesAndStores(user, currClientId, kcUser);
			return user;
		}
		
		return user;
	}
	
	public UserInfoVo queryUserDetail(String userId) {
		User user = userRepository.fetchUserById(userId);
		return this.fillDetailInfo(user);
	}
	
	private void fillAttr(User kcUser, final UserInfoVo user) {
		Map<String,List<String>> attrMap = kcUser.getAttrMap();
		if(MapUtils.isNotEmpty(attrMap)) {
			List<String> networkId = attrMap.get("networkId");
			if(CollectionUtils.isNotEmpty(networkId)) {
				user.setNetworkId(networkId.get(0));
			}
			List<String> avatar = attrMap.get("picture");
			if(CollectionUtils.isNotEmpty(avatar)) {
				user.setAvatar(avatar.get(0));
			}
			
		}
	}
	
	private void fillResourcesAndStores(final UserInfoVo user, String currClientId, User kcUser) {
		
		// kc中的roles
		Map<String, List<Role>> clientRoleModelMap = kcUser.getClientRoleMap();
		if(MapUtils.isEmpty(clientRoleModelMap)) {
			return;
		}
		
		Map<String,List<String>> clientRoleStoreMap = new HashMap<>();
		
		// 接入permission service的client
		List<String> pmClients = pmServerConfig.getBizSystemList().stream().map(BizSystemInfo::getClientId).collect(Collectors.toList());
		
		// 排除没有接入permission service的client
		List<String> clientIds = new ArrayList<>(clientRoleModelMap.keySet());
		clientIds.forEach(clientId -> {
			if(!pmClients.contains(clientId)) {
				clientRoleModelMap.remove(clientId);
			}
		});
		
		// pm中的role-store
		Map<String, List<UserRole>> userRoleStoreMap = userRoleRepository.getUserRoleStoreMap(user.getUserId());
				
		clientRoleModelMap.forEach((clientId, roleList) -> {
			
			Map<String,List<String>> roleStoreMap;
			if(userRoleStoreMap.containsKey(clientId)) {
				roleStoreMap = userRoleStoreMap.get(clientId).stream().collect(Collectors.toMap(UserRole::getRoleName, po -> { return JacksonUtils.readValue(po.getStoreIds(), new TypeReference<List<String>>() {});}, (e1, e2) -> e1));
			} else {
				roleStoreMap = Collections.emptyMap();
			}
			
			Closure<Role> setStoresClosure = IfClosure.ifClosure((role) -> CollectionUtils.isEmpty(role.getStoreIds()), 
					(role) -> role.setStoreIds(roleStoreMap.get(role.getName())));
			
			// 系统默认的role设置默认store
			Closure<Role> closure = IfClosure.ifClosure(
					(role) -> BkdConstants.SysRole.contains(role.getName()),
					(role) -> role.setStoreIds(Collections.singletonList(BkdConstants.DEFAULT_STORE)),
					setStoresClosure::execute);
			
			clientRoleModelMap.get(clientId).forEach(closure::execute);
			
		});
		
		fillMenu(user, currClientId, kcUser.getRoleMap(), clientRoleStoreMap);
	}
	
	public void changeUserRolesToAllClientRolesIfHaveClientAdmin(User kcUser) {
		// 用户在kc中的role
		Map<String, List<Role>> clientRoleMap = kcUser.getClientRoleMap();
		
		if(MapUtils.isEmpty(clientRoleMap)) {
			return ;
		}
		
		clientRoleMap.forEach((clientId, roles) -> {
			if(CollectionUtils.isEmpty(roles)) {
				return;
			}
			
			boolean hasAdmin = roles.stream().anyMatch(role -> BkdConstants.AppAdmin.equals(role.getName()));
			if(hasAdmin) {
				List<String> storeIds = storeService.queryAllStores().stream().map(StoreDO::getStoreId).collect(Collectors.toList());
				List<Role> allRoles = roleRepository.getRoles(clientId);
				allRoles.forEach(role -> role.setStoreIds(storeIds));
				clientRoleMap.put(clientId, allRoles);
			}
			
		});
	}
	
	/**
	 * 填充{@linkplain user#resourceMap}
	 * @param user
	 * @param clientId
	 * @param roleMap 当前用户在keycloak上的所有role, key -> clientId, value -> roleName list
	 * @param roleStoreMap 当前用户当前clientId在pm中的role和store关系，key -> roleName, value -> stores list
	 */
	private void fillMenu(UserInfoVo user, String clientId, Map<String, List<String>> roleMap, Map<String, List<String>> roleStoreMap) {
		List<String> userClientRoles = roleMap.get(clientId);
		if(CollectionUtils.isEmpty(userClientRoles)) {
			return;
		}
		// key -> menuId, value -> store list
		Map<String,List<String>> menuStoreMap = new HashMap<>();
		List<String> menuIds = new ArrayList<>();
		// 查询所有role 
		List<Role> clientAllroles = roleRepository.getRoles(clientId);
		if(CollectionUtils.isEmpty(clientAllroles)) {
			return;
		}
		Map<String, Role> clientAllRolesMap = clientAllroles.stream().collect(Collectors.toMap(Role::getName, role -> role, (r1, r2) -> r1));
		for(String roleName : userClientRoles) {
			if(!clientAllRolesMap.containsKey(roleName)) {
				continue;
			}
			Role role = clientAllRolesMap.get(roleName);
			List<String> roleMenuIds = role.getAttrMap().get("menuIds");
			if(CollectionUtils.isEmpty(roleMenuIds)) {
				continue;
			}
			menuIds.addAll(roleMenuIds);
			
			if(MapUtils.isEmpty(roleStoreMap)) {
				continue;
			}
			List<String> roleStoreIds = roleStoreMap.get(roleName);
			roleMenuIds.forEach(roleMenuId -> {
				List<String> storeIds = menuStoreMap.computeIfAbsent(roleMenuId, k -> new ArrayList<>());
				if(CollectionUtils.isNotEmpty(roleStoreIds)) {
					storeIds.addAll(roleStoreIds);
				}
			});
		}
		if(CollectionUtils.isEmpty(menuIds)) {
			return;
		}
		List<Long> distinctMenuIds = menuIds.stream().map(Long::valueOf).distinct().collect(Collectors.toList());
		List<ResourceVo> resourceList = menuService.buildResourceTree(distinctMenuIds);
		resourceList.forEach(menu -> fillResourceStoreIds(menu, menuStoreMap));
		
		user.setResourceMap(Collections.singletonMap(clientId, resourceList));
	}
	
	private void fillResourceStoreIds(ResourceVo resourceVo,Map<String, List<String>> menuStoreMap) {
		List<String> menuStoreIds = menuStoreMap.get(String.valueOf(resourceVo.getId()));
		resourceVo.setStoreIds(menuStoreIds == null ? Collections.emptyList() : menuStoreIds.stream().distinct().collect(Collectors.toList()));
		List<ResourceVo> subRs = resourceVo.getSubResource();
		if(CollectionUtils.isNotEmpty(subRs)) {
			subRs.forEach(rs -> {
				fillResourceStoreIds(rs, menuStoreMap);
			});
		}
	}
	
}