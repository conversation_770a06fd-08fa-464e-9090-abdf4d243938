#!/usr/bin/env node

/**
 * 🔄 Permission Service Token Exchange
 *
 * Purpose:
 * 1. Try to exchange JWT token directly with permission service
 * 2. Get a valid pm_user_token that's stored in backend
 * 3. Test the resulting session cookie
 */

import axios from 'axios';
import { getEnvironmentConfig } from '../dist/utils/env.js';
import { decodeJWTUnsafe } from '../dist/utils/jwt-utils.js';
import fs from 'fs';
import path from 'path';

/**
 * Load configuration
 */
function loadConfiguration() {
  const env = getEnvironmentConfig();
  
  let tokenData = null;
  try {
    const tokenFile = path.join(process.cwd(), 'mcp-mpc-odi.token.json');
    const tokenContent = fs.readFileSync(tokenFile, 'utf8');
    tokenData = JSON.parse(tokenContent);
  } catch (error) {
    console.error('⚠️ Could not load token file:', error.message);
  }

  return {
    JWT_TOKEN: tokenData?.access_token || '',
    TOKEN_DATA: tokenData,
    PERMISSION_SERVICE_BASE: 'https://api-dev-mpp-fe.ingka-dt.cn',
    ORDERS_API_BASE: 'https://fe-dev-i.ingka-dt.cn/order-web'
  };
}

const CONFIG = loadConfiguration();

/**
 * Try to get login URL from permission service
 */
async function getPermissionServiceLoginUrl() {
  console.log('🔄 ==================== GET LOGIN URL ====================');
  
  try {
    console.log('🔄 Getting login URL from permission service...');
    
    const loginUrlEndpoint = `${CONFIG.PERMISSION_SERVICE_BASE}/permission-service/user/auth/getLoginUrl`;
    
    const response = await axios.get(loginUrlEndpoint, {
      headers: {
        'clientId': 'orders-portal',
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
      },
      params: {
        redirectUrl: 'https://fe-dev-i.ingka-dt.cn/order-web/user/current',
        referer: 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    console.log(`   Response data: ${JSON.stringify(response.data, null, 2)}`);
    
    if (response.data?.success && response.data?.data) {
      const loginUrl = response.data.data;
      console.log('✅ Got login URL from permission service');
      console.log(`   Login URL: ${loginUrl}`);
      return loginUrl;
    }
    
    return null;
  } catch (error) {
    console.error('❌ Failed to get login URL:', error.message);
    return null;
  }
}

/**
 * Try direct token exchange with permission service
 */
async function tryDirectTokenExchange() {
  console.log('\n🔄 ==================== DIRECT TOKEN EXCHANGE ====================');
  
  try {
    console.log('🔄 Trying direct token exchange with permission service...');
    
    const sessionInfo = decodeJWTUnsafe(CONFIG.JWT_TOKEN);
    console.log(`   User ID: ${sessionInfo.sub}`);
    console.log(`   Email: ${sessionInfo.email || 'N/A'}`);
    
    // Try different permission service endpoints
    const endpoints = [
      '/prm-auth/auth/token',
      '/prm-auth/oauth/token', 
      '/permission-service/auth/token',
      '/permission-service/oauth/token'
    ];
    
    for (const endpoint of endpoints) {
      console.log(`\n🔄 Trying endpoint: ${endpoint}`);
      
      const tokenUrl = `${CONFIG.PERMISSION_SERVICE_BASE}${endpoint}`;
      
      // Try with JWT token as Bearer
      const response = await axios.post(tokenUrl, {
        grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
        assertion: CONFIG.JWT_TOKEN,
        client_id: 'orders-portal'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${CONFIG.JWT_TOKEN}`,
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
        },
        timeout: 10000,
        validateStatus: () => true
      });
      
      console.log(`   Response status: ${response.status}`);
      if (response.data) {
        console.log(`   Response data: ${JSON.stringify(response.data, null, 2)}`);
      }
      
      if (response.status === 200 && response.data) {
        console.log('✅ Token exchange successful!');
        return response.data;
      }
    }
    
    return null;
  } catch (error) {
    console.error('❌ Direct token exchange failed:', error.message);
    return null;
  }
}

/**
 * Try to use JWT token directly with orders API
 */
async function tryJWTDirectly() {
  console.log('\n🔄 ==================== JWT DIRECT TEST ====================');
  
  try {
    console.log('🔄 Testing JWT token directly with orders API...');
    
    const testUrl = `${CONFIG.ORDERS_API_BASE}/orders/search`;
    const requestData = {
      "storeIds": [],
      "baseSize": 10,
      "page": 1,
      "size": 10,
      "timestamp": Date.now()
    };
    
    console.log(`   URL: ${testUrl}`);
    console.log(`   JWT: ${CONFIG.JWT_TOKEN.substring(0, 50)}...`);
    
    const response = await axios.post(testUrl, requestData, {
      headers: {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'Authorization': `Bearer ${CONFIG.JWT_TOKEN}`,
        'dnt': '1',
        'origin': 'https://admin.ingka-dt.cn',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://admin.ingka-dt.cn/',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'x-custom-referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    console.log(`   Response data: ${JSON.stringify(response.data, null, 2)}`);
    
    if (response.status === 200 && response.data && response.data.code !== 401) {
      console.log('✅ JWT token works directly!');
      return true;
    } else if (response.data?.code === 401) {
      console.log('❌ JWT token not accepted - need pm_user_token');
      return false;
    }
    
    return false;
  } catch (error) {
    console.error('❌ JWT direct test failed:', error.message);
    return false;
  }
}

/**
 * Try to extract working pm_user_token from browser capture
 */
async function testBrowserCapturedToken() {
  console.log('\n🔄 ==================== BROWSER CAPTURED TOKEN TEST ====================');
  
  // From your browser capture, the working pm_user_token was:
  const browserPmToken = 'NzhmM2IxMjAtNWU3NS00YzgxLThmMDEtMjAxZGMxYjVjNzEwQDE3NTQwMTU2MTkyNDQuM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA=';
  
  console.log('🔄 Testing browser-captured pm_user_token...');
  console.log(`   Token: ${browserPmToken.substring(0, 50)}...`);
  
  try {
    const testUrl = `${CONFIG.ORDERS_API_BASE}/orders/search`;
    const requestData = {
      "storeIds": [],
      "baseSize": 10,
      "page": 1,
      "size": 10,
      "timestamp": Date.now()
    };
    
    const response = await axios.post(testUrl, requestData, {
      headers: {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'Cookie': `test_orders-portal=${browserPmToken}`,
        'dnt': '1',
        'origin': 'https://admin.ingka-dt.cn',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://admin.ingka-dt.cn/',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'x-custom-referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    console.log(`   Response data: ${JSON.stringify(response.data, null, 2)}`);
    
    if (response.status === 200 && response.data && response.data.code !== 401) {
      console.log('✅ Browser-captured pm_user_token still works!');
      console.log('   This confirms that pm_user_token is a backend session ID');
      return true;
    } else if (response.data?.code === 401) {
      console.log('❌ Browser-captured pm_user_token expired');
      console.log('   This confirms that pm_user_token has limited lifetime');
      return false;
    }
    
    return false;
  } catch (error) {
    console.error('❌ Browser token test failed:', error.message);
    return false;
  }
}

/**
 * Main function
 */
async function testPermissionServiceExchange() {
  console.log('🔄 ==================== PERMISSION SERVICE EXCHANGE ====================');
  console.log('🎯 Purpose: Find the correct way to get valid pm_user_token from backend\n');
  
  console.log('📋 Configuration:');
  console.log(`   Token File: ${CONFIG.TOKEN_DATA ? 'Loaded ✅' : 'Missing ❌'}`);
  console.log(`   JWT Token: ${CONFIG.JWT_TOKEN ? CONFIG.JWT_TOKEN.substring(0, 50) + '...' : 'Not available ❌'}`);
  console.log(`   Permission Service: ${CONFIG.PERMISSION_SERVICE_BASE}`);
  console.log(`   Orders API: ${CONFIG.ORDERS_API_BASE}`);
  console.log('');
  
  if (!CONFIG.JWT_TOKEN) {
    console.log('❌ No JWT token available. Please update mcp-mpc-odi.token.json');
    return;
  }
  
  // Test 1: Try JWT directly
  const jwtWorks = await tryJWTDirectly();
  
  // Test 2: Test browser-captured token
  const browserTokenWorks = await testBrowserCapturedToken();
  
  // Test 3: Get login URL
  const loginUrl = await getPermissionServiceLoginUrl();
  
  // Test 4: Try direct token exchange
  const exchangeResult = await tryDirectTokenExchange();
  
  console.log('\n🏁 ==================== RESULTS SUMMARY ====================');
  console.log('✅ Permission service exchange test completed');
  console.log('');
  console.log('🎯 **Key Findings:**');
  console.log(`   JWT Direct: ${jwtWorks ? 'Works ✅' : 'Failed ❌'}`);
  console.log(`   Browser Token: ${browserTokenWorks ? 'Still valid ✅' : 'Expired ❌'}`);
  console.log(`   Login URL: ${loginUrl ? 'Retrieved ✅' : 'Failed ❌'}`);
  console.log(`   Token Exchange: ${exchangeResult ? 'Success ✅' : 'Failed ❌'}`);
  console.log('');
  
  if (jwtWorks) {
    console.log('🎉 **GREAT NEWS: Your JWT token works directly!**');
    console.log('   You can use JWT Bearer authentication instead of pm_user_token');
  } else if (browserTokenWorks) {
    console.log('🔍 **Browser token still works - pm_user_token is valid backend session**');
    console.log('   Need to find the correct API to exchange JWT for pm_user_token');
  } else if (loginUrl) {
    console.log('💡 **Got login URL - this is the OAuth2 entry point**');
    console.log(`   Login URL: ${loginUrl}`);
    console.log('   This confirms the OAuth2 flow is the correct approach');
  } else {
    console.log('❌ **All methods failed**');
    console.log('   May need to implement full OAuth2 flow or find alternative approach');
  }
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testPermissionServiceExchange().catch(error => {
    console.error('💥 Permission service exchange test failed:', error);
    process.exit(1);
  });
}

export { testPermissionServiceExchange };
