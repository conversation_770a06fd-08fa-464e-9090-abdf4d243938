package com.ikea.digital.permission.server.resource.domain.model;

import java.util.List;
import java.util.Map;

import org.keycloak.representations.idm.UserRepresentation;

import lombok.Data;

@Data
public class User {

	String userId;
	
	String name;
	
	String email;
	
	Boolean enabled;
	
	List<String> groups;
	
	Map<String,List<String>> roleMap;
	
	Map<String,List<String>> attrMap;
	
	Map<String,List<Role>> clientRoleMap;
	
	public User(){}
	
	public User(UserRepresentation user){
		initWithKcUser(user);
	}
	
	public void initWithKcUser(UserRepresentation user){
		this.setUserId(user.getId());
		this.setName(user.getFirstName() + (user.getLastName() == null ? "" : " " + user.getLastName()));
		this.setEmail(user.getEmail());
		this.setEnabled(user.isEnabled());
		this.setGroups(user.getGroups());
		this.setRoleMap(user.getClientRoles());
		this.setAttrMap(user.getAttributes());
	}
}
