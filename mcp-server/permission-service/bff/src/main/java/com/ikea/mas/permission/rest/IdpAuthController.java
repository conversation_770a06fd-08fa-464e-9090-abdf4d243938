package com.ikea.mas.permission.rest;

import java.io.IOException;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ikea.mas.permission.common.Constants;
import com.ikea.mas.permission.common.RespCodeEnum;
import com.ikea.mas.permission.common.Result;
import com.ikea.mas.permission.config.prop.AzureProperties;
import com.ikea.mas.permission.dto.SsoCacheDto;
import com.ikea.mas.permission.dto.StateCacheDto;
import com.ikea.mas.permission.service.IdpAuthService;
import com.ikea.mas.permission.service.UserAuthService;
import com.ikea.mas.permission.utils.CookieUtils;
import com.ikea.mas.permission.validator.SsoValidator;
import com.ikea.mas.permission.validator.StateValidator;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/auth")
@Slf4j
public class IdpAuthController {

	@Autowired
	private IdpAuthService idpAuthService;
	
	@Autowired
	private UserAuthService userAuthService;
	
	@Autowired
	private AzureProperties azureConfig;
	
	@Autowired
	private StateValidator stateValidator;
	
	@Autowired
	private SsoValidator ssoValidator;
	
	/**
	 * req eg: http://192.168.97.38:1999/permission-service/idp/auth/authEndpoint?
	 *              scope=openid+profile+name+email+picture+phone+roles+identities+sub+nickname+network_id
	 *              &state=4b4iKVw4E7CESf32xReRxu-sHNbqT-TQFmZNZqA8yi8.3dPX06PpGno.permission-service
	 *              &response_type=code&client_id=47c18b21-9b4b-4a35-9607-60e156bcab30
	 * 				&redirect_uri=http%3A%2F%2F**************%3A8080%2Frealms%2Fmaster%2Fbroker%2FPermissionCenterServerIDP%2Fendpoint
	 * 				&prompt=none&nonce=lFloM09kduAzFBlgGdq8kg
	 * 
	 * res eg: https://login.microsoftonline.com/fe4ff18a-4d40-422d-9d2a-8917b6073930/oauth2/v2.0/authorize?
	 * 				scope=openid+profile+offline_access+&response_type=code
	 * 				&redirect_uri=http%3A%2F%2Flocalhost%3A1999%2Fpermission-service%2Fidp%2Fauth%2Fmicrosoft%2Fcallback
	 * 				&state=4b4iKVw4E7CESf32xReRxu-sHNbqT-TQFmZNZqA8yi8.3dPX06PpGno.permission-service
	 * 				&nonce=lFloM09kduAzFBlgGdq8kg&prompt=select_account
	 * 				&client_id=47c18b21-9b4b-4a35-9607-60e156bcab30&response_mode=query
	 */
	@GetMapping("/authEndpoint")
	public ResponseEntity<String> authEndpoint(@RequestParam("state") String state, 
			@RequestParam("nonce") String nonce, 
			@RequestParam("scope") String scope, 
			@RequestParam("redirect_uri") String redirectUri,
			HttpServletRequest request,
			HttpServletResponse response) {
		// 此处的state是keycloak产生,非getLoginUrl产生的state
		Result<String> result = idpAuthService.createThirdpartyLoginUrl(nonce, state, redirectUri, azureConfig.getLoginCallbackUrl());
		if(!result.isOk()) {
			return ResponseEntity.status(RespCodeEnum.UNKNOW_ERROR.getCode())
					.body(result.getMsg());
		}
		String loginUrl = result.getData();
		log.info("{} url:{}, state:{}", "redirect2MiscrosoftLoginEndpoint", loginUrl, state);
		return ResponseEntity.status(HttpStatus.FOUND).header(HttpHeaders.LOCATION, loginUrl).build();
	}
	
	/**
	 * req eg: http://localhost:1999/permission-service/idp/auth/microsoft/callback?code=xxxx&state=4b4iKVw4E7CESf32xReRxu-sHNbqT-TQFmZNZqA8yi8.3dPX06PpGno.permission-service&session_state=bad2b905-6fde-4dce-9594-afc7988b9967
	 * res eg: http://**************:8080/realms/master/broker/PermissionCenterServerIDP/endpoint?state=4b4iKVw4E7CESf32xReRxu-sHNbqT-TQFmZNZqA8yi8.3dPX06PpGno.permission-service&code=-KI3Q9nNR7bRofxmeZoXqbHZGew
	 */
	@GetMapping("/microsoft/callback")
	public ResponseEntity<String> microsoftCallback(@RequestParam("code") String code,
            @RequestParam("state") String state,
            @RequestParam("session_state") String sessionState,
            HttpServletRequest request,HttpServletResponse response) throws IOException {
		log.info("----loing02.microsoftCallback.request:{},state:{}",request.getRequestURL(),state);
		String referer = request.getRequestURL().toString();
        referer = referer.contains("?") ? referer.substring(0, referer.indexOf("?")) : referer;
        // 此处的state并非来自getLoginUrl
		Result<StateCacheDto> kcStateResult = stateValidator.checkState(state);
		if(!kcStateResult.isOk()) {
			log.warn("kcState:{} invalid", state);
			return ResponseEntity.status(RespCodeEnum.INVALID_CLIENT.getCode())
					.body(kcStateResult.getMsg());
		}
		
        Result<String> result = idpAuthService.keycloakIdpLoginCompleteEndpoint(code, kcStateResult.getData(), sessionState,referer);
		
		if(!result.isOk()) {
			return ResponseEntity.status(RespCodeEnum.UNKNOW_ERROR.getCode())
					.body(result.getMsg());
		}
		
		String kcLoginCallbackUrl = result.getData();
		log.info("redirect2KeycloakLoginCallbackUrl:{}",kcLoginCallbackUrl);
		return ResponseEntity.status(HttpStatus.FOUND).header(HttpHeaders.LOCATION, kcLoginCallbackUrl).build();
	}
	
	/**
	 * req eg: http://localhost:1999/permission-service/idp/auth/microsoft/callback?code=xxxx&state=4b4iKVw4E7CESf32xReRxu-sHNbqT-TQFmZNZqA8yi8.3dPX06PpGno.permission-service&session_state=bad2b905-6fde-4dce-9594-afc7988b9967
	 * res eg: http://**************:8080/realms/master/broker/PermissionCenterServerIDP/endpoint?state=4b4iKVw4E7CESf32xReRxu-sHNbqT-TQFmZNZqA8yi8.3dPX06PpGno.permission-service&code=-KI3Q9nNR7bRofxmeZoXqbHZGew
	 */
	@GetMapping("/microsoft/directCallback")
	public ResponseEntity<String> microsoftDirectCallback(@RequestParam("code") String code,
            @RequestParam("state") String state,
            @RequestParam("session_state") String sessionState,
            HttpServletRequest request,HttpServletResponse response) throws IOException {
		log.info("----loing02.directCallback.request:{},state:{}",request.getRequestURL(),state);
		String referer = request.getRequestURL().toString();
        referer = referer.contains("?") ? referer.substring(0, referer.indexOf("?")) : referer;
        
        Result<StateCacheDto> stateResult = stateValidator.checkState(state);
        if(!stateResult.isOk()) {
        	log.warn("state:{} invalid", state);
			return ResponseEntity.status(RespCodeEnum.INVALID_CLIENT.getCode())
					.body(stateResult.getMsg());
		}
        
        String ssoUUID = idpAuthService.getSsoUUIDFromStateMap(state);
        // 兼容老代码
        if(StringUtils.isBlank(ssoUUID)) {
        	ssoUUID = CookieUtils.readCookie(request, Constants.SSO_COOKIE_NAME);
        }
        
        Result<SsoCacheDto> ssoResult = ssoValidator.checkSso(ssoUUID);
        if(!ssoResult.isOk()){
        	log.warn("state:{}, ssoUUID:{} invalid", state, ssoUUID);
			return ResponseEntity.status(RespCodeEnum.INVALID_CLIENT.getCode())
					.body("state invalid, please login again!");
    	}
        
        // 跳转回getLoginUrl时候传递的redirect地址
        Result<String> result = userAuthService.thirdpartyLoginComplete(ssoResult.getData(), code, stateResult.getData(), sessionState,referer);
        if(!result.isOk()) {
        	return ResponseEntity.status(RespCodeEnum.UNKNOW_ERROR.getCode())
            		.body(result.getMsg());
        }
        
        String redirectUrl = result.getData();
		log.info("directCallback.redirectUrl:{}, state:{}", redirectUrl, state);
		return ResponseEntity.status(HttpStatus.FOUND).header(HttpHeaders.LOCATION, redirectUrl).build();
	}
	
}
