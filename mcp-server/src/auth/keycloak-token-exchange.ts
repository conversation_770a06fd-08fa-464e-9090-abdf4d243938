import axios from 'axios';
import { getEnvironmentConfig } from '../utils/env.js';
import { decodeJWTUnsafe } from '../utils/jwt-utils.js';

/**
 * Configuration for Keycloak token exchange
 */
export interface KeycloakTokenExchangeConfig {
  enabled: boolean;
  keycloakBaseUrl: string;
  realm: string;
  // Source client (your MCP client)
  sourceClient: {
    clientId: string;
    clientSecret?: string;
  };
  // Target client (permission-service)
  targetClient: {
    clientId: string;
    clientSecret: string;
  };
}

/**
 * Token exchange response from Keycloak
 */
interface TokenExchangeResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token?: string;
  scope?: string;
}

/**
 * Cached token entry
 */
interface CachedToken {
  accessToken: string;
  expiresAt: number;
  sourceTokenHash: string;
}

/**
 * Keycloak Token Exchange Manager
 * Exchanges JWT tokens from one client to another using Keycloak's token exchange
 */
export class KeycloakTokenExchange {
  private config: KeycloakTokenExchangeConfig;
  private tokenCache = new Map<string, CachedToken>();

  constructor(config: KeycloakTokenExchangeConfig) {
    this.config = config;
  }

  /**
   * Exchange JWT token from source client to target client
   */
  async exchangeToken(sourceJWT: string): Promise<string | null> {
    if (!this.config.enabled) {
      console.log('🔄 [KeycloakTokenExchange] Token exchange disabled');
      return null;
    }

    try {
      // Check cache first
      const cached = this.getCachedToken(sourceJWT);
      if (cached) {
        console.log('🎯 [KeycloakTokenExchange] Using cached exchanged token');
        return cached;
      }

      // Perform token exchange
      const exchangedToken = await this.performTokenExchange(sourceJWT);
      if (exchangedToken) {
        this.cacheToken(sourceJWT, exchangedToken);
        return exchangedToken;
      }

      return null;
    } catch (error) {
      console.error('❌ [KeycloakTokenExchange] Token exchange failed:', error);
      return null;
    }
  }

  /**
   * Perform the actual token exchange with Keycloak
   */
  private async performTokenExchange(sourceJWT: string): Promise<string | null> {
    try {
      console.log('🔄 [KeycloakTokenExchange] Performing token exchange');
      console.log(`   Source client: ${this.config.sourceClient.clientId}`);
      console.log(`   Target client: ${this.config.targetClient.clientId}`);

      const tokenEndpoint = `${this.config.keycloakBaseUrl}/auth/realms/${this.config.realm}/protocol/openid-connect/token`;

      // Try Method 1: Using source client credentials (your mcp-mcp-odi client)
      if (this.config.sourceClient.clientSecret) {
        console.log('🔄 [KeycloakTokenExchange] Trying with source client credentials');
        const result = await this.tryTokenExchangeWithSourceClient(sourceJWT, tokenEndpoint);
        if (result) return result;
      }

      // Try Method 2: Using target client credentials (permission-service client)
      if (this.config.targetClient.clientSecret) {
        console.log('🔄 [KeycloakTokenExchange] Trying with target client credentials');
        const result = await this.tryTokenExchangeWithTargetClient(sourceJWT, tokenEndpoint);
        if (result) return result;
      }

      // Try Method 3: Public client token exchange (no client secret)
      console.log('🔄 [KeycloakTokenExchange] Trying public client token exchange');
      const result = await this.tryPublicTokenExchange(sourceJWT, tokenEndpoint);
      if (result) return result;

      console.log('⚠️ [KeycloakTokenExchange] All token exchange methods failed');
      return null;

    } catch (error) {
      console.error('❌ [KeycloakTokenExchange] Token exchange failed:', error);
      return null;
    }
  }

  /**
   * Try token exchange using source client credentials
   */
  private async tryTokenExchangeWithSourceClient(sourceJWT: string, tokenEndpoint: string): Promise<string | null> {
    try {
      const params = new URLSearchParams({
        grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',
        subject_token: sourceJWT,
        subject_token_type: 'urn:ietf:params:oauth:token-type:access_token',
        client_id: this.config.sourceClient.clientId,
        client_secret: this.config.sourceClient.clientSecret!,
        audience: this.config.targetClient.clientId,
        requested_token_type: 'urn:ietf:params:oauth:token-type:access_token'
      });

      const response = await axios.post(tokenEndpoint, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        }
      });

      const tokenResponse: TokenExchangeResponse = response.data;
      if (tokenResponse.access_token) {
        console.log('✅ [KeycloakTokenExchange] Source client token exchange successful');
        return tokenResponse.access_token;
      }
      return null;
    } catch (error) {
      console.log('⚠️ [KeycloakTokenExchange] Source client token exchange failed');
      return null;
    }
  }

  /**
   * Try token exchange using target client credentials
   */
  private async tryTokenExchangeWithTargetClient(sourceJWT: string, tokenEndpoint: string): Promise<string | null> {
    try {
      const params = new URLSearchParams({
        grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',
        subject_token: sourceJWT,
        subject_token_type: 'urn:ietf:params:oauth:token-type:access_token',
        client_id: this.config.targetClient.clientId,
        client_secret: this.config.targetClient.clientSecret,
        audience: this.config.targetClient.clientId,
        requested_token_type: 'urn:ietf:params:oauth:token-type:access_token'
      });

      const response = await axios.post(tokenEndpoint, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        }
      });

      const tokenResponse: TokenExchangeResponse = response.data;
      if (tokenResponse.access_token) {
        console.log('✅ [KeycloakTokenExchange] Target client token exchange successful');
        return tokenResponse.access_token;
      }
      return null;
    } catch (error) {
      console.log('⚠️ [KeycloakTokenExchange] Target client token exchange failed');
      return null;
    }
  }

  /**
   * Try public client token exchange (no client secret required)
   */
  private async tryPublicTokenExchange(sourceJWT: string, tokenEndpoint: string): Promise<string | null> {
    try {
      const params = new URLSearchParams({
        grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',
        subject_token: sourceJWT,
        subject_token_type: 'urn:ietf:params:oauth:token-type:access_token',
        client_id: this.config.sourceClient.clientId,
        audience: this.config.targetClient.clientId,
        requested_token_type: 'urn:ietf:params:oauth:token-type:access_token'
      });

      const response = await axios.post(tokenEndpoint, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        }
      });

      const tokenResponse: TokenExchangeResponse = response.data;
      if (tokenResponse.access_token) {
        console.log('✅ [KeycloakTokenExchange] Public token exchange successful');
        return tokenResponse.access_token;
      }
      return null;
    } catch (error) {
      console.log('⚠️ [KeycloakTokenExchange] Public token exchange failed');
      return null;
    }
  }

  /**
   * Alternative: Use client credentials to get permission-service token
   */
  async getClientCredentialsToken(): Promise<string | null> {
    try {
      console.log('🔄 [KeycloakTokenExchange] Getting client credentials token');

      const tokenEndpoint = `${this.config.keycloakBaseUrl}/auth/realms/${this.config.realm}/protocol/openid-connect/token`;

      const params = new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: this.config.targetClient.clientId,
        client_secret: this.config.targetClient.clientSecret,
        scope: 'openid profile'
      });

      const response = await axios.post(tokenEndpoint, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        }
      });

      const tokenResponse: TokenExchangeResponse = response.data;
      
      if (tokenResponse.access_token) {
        console.log('✅ [KeycloakTokenExchange] Client credentials token obtained');
        return tokenResponse.access_token;
      }

      return null;
    } catch (error) {
      console.error('❌ [KeycloakTokenExchange] Client credentials failed:', error);
      return null;
    }
  }

  /**
   * Use permission service's own authentication flow
   */
  async authenticateWithPermissionService(sourceJWT: string): Promise<string | null> {
    try {
      console.log('🔄 [KeycloakTokenExchange] Authenticating with permission service');

      // Extract user info from source JWT
      const payload = decodeJWTUnsafe(sourceJWT);
      const userId = payload.sub;

      // Step 1: Get login URL from permission service
      const loginUrlResponse = await axios.get(
        `${this.config.keycloakBaseUrl.replace('/auth', '')}/permission-service/user/auth/getLoginUrl`,
        {
          headers: {
            'clientId': 'orders-portal', // Your application client ID
          },
          params: {
            redirectUrl: 'https://fe-dev-i.ingka-dt.cn/order-web/user/current',
            referer: 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
          }
        }
      );

      if (loginUrlResponse.data?.success && loginUrlResponse.data?.data) {
        const loginUrl = loginUrlResponse.data.data;
        console.log('✅ [KeycloakTokenExchange] Got permission service login URL');
        
        // Step 2: Follow the login flow with the source JWT
        // This would require implementing the full OAuth2 flow
        // For now, return the login URL for manual processing
        console.log('🔗 [KeycloakTokenExchange] Login URL:', loginUrl);
        
        return null; // Would need to complete the flow
      }

      return null;
    } catch (error) {
      console.error('❌ [KeycloakTokenExchange] Permission service auth failed:', error);
      return null;
    }
  }

  /**
   * Get cached token if valid
   */
  private getCachedToken(sourceJWT: string): string | null {
    const tokenHash = this.hashToken(sourceJWT);
    const cached = this.tokenCache.get(tokenHash);
    
    if (!cached) {
      return null;
    }

    // Check if expired (with 5 minute buffer)
    const bufferTime = 5 * 60 * 1000; // 5 minutes
    if (cached.expiresAt - bufferTime <= Date.now()) {
      console.log('🕐 [KeycloakTokenExchange] Cached token expired');
      this.tokenCache.delete(tokenHash);
      return null;
    }

    return cached.accessToken;
  }

  /**
   * Cache the exchanged token
   */
  private cacheToken(sourceJWT: string, accessToken: string): void {
    try {
      const payload = decodeJWTUnsafe(accessToken);
      const expiresAt = payload.exp * 1000; // Convert to milliseconds
      const tokenHash = this.hashToken(sourceJWT);

      this.tokenCache.set(tokenHash, {
        accessToken,
        expiresAt,
        sourceTokenHash: tokenHash
      });

      console.log('💾 [KeycloakTokenExchange] Token cached');
    } catch (error) {
      console.error('⚠️ [KeycloakTokenExchange] Failed to cache token:', error);
    }
  }

  /**
   * Create hash of token for caching
   */
  private hashToken(token: string): string {
    // Simple hash based on token content
    return Buffer.from(token).toString('base64').substring(0, 32);
  }

  /**
   * Clear token cache
   */
  clearCache(): void {
    this.tokenCache.clear();
    console.log('🗑️ [KeycloakTokenExchange] Cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { totalCached: number; entries: Array<{ expiresAt: number; sourceHash: string }> } {
    const entries = Array.from(this.tokenCache.values()).map(entry => ({
      expiresAt: entry.expiresAt,
      sourceHash: entry.sourceTokenHash
    }));

    return {
      totalCached: this.tokenCache.size,
      entries
    };
  }
}

/**
 * Create Keycloak token exchange configuration from environment
 */
export function createKeycloakTokenExchangeConfig(): KeycloakTokenExchangeConfig {
  const env = getEnvironmentConfig();
  
  return {
    enabled: env.KEYCLOAK_TOKEN_EXCHANGE_ENABLED || false,
    keycloakBaseUrl: env.KEYCLOAK_BASE_URL || 'https://keycloak.ingka-dt.cn',
    realm: env.KEYCLOAK_REALM || 'master',
    sourceClient: {
      clientId: env.MCP_CLIENT_ID || 'mcp-mcp-odi',
      clientSecret: env.MCP_CLIENT_SECRET
    },
    targetClient: {
      clientId: env.PERMISSION_SERVICE_CLIENT_ID || 'permission-service',
      clientSecret: env.PERMISSION_SERVICE_CLIENT_SECRET || ''
    }
  };
}

/**
 * Global Keycloak token exchange instance
 */
export const globalKeycloakTokenExchange = new KeycloakTokenExchange(createKeycloakTokenExchangeConfig());
