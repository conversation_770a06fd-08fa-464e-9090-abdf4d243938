import dotenv from 'dotenv';
import path from 'path';

// Load .env file
dotenv.config({ path: path.resolve(process.cwd(), '.env') });

/**
 * 环境配置工具
 * 从环境变量中读取配置信息
 */

export interface EnvironmentConfig {
  NODE_ENV: string;
  VITE_API_HOST: string;
  VITE_API_HOST_KONG: string;
  VITE_MASTER_DATA_API_HOST: string;
  VITE_MASTER_DATA_API_KEY: string;
  AUTH_COOKIES: string;
  X_CUSTOM_REFERRER: string;
  DEBUG_SERVICE_ADAPTER: boolean;
  // Transport Configuration
  TRANSPORT: 'stdio' | 'http';
  // Unified Port Configuration
  MCP_SERVER_PORT: number;
  MCP_SERVER_BASE_URL: string;
  // OAuth2 Configuration
  OAUTH2_ENABLED: boolean;
  KEYCLOAK_BASE_URL: string;
  KEYCLOAK_REALM: string;
  OAUTH2_CLIENT_ID: string;
  OAUTH2_CLIENT_SECRET: string;
  OAUTH2_REDIRECT_URI: string;
  OAUTH2_SCOPES: string[];
  OAUTH2_ADMIN_SCOPES: string[];
  OAUTH2_WRITE_SCOPES: string[];
  OAUTH2_DEFAULT_SCOPE_VALIDATION: 'all' | 'any';
  OAUTH2_ISSUER_URL: string;
  // Dynamic Cookie Configuration
  DYNAMIC_COOKIES_ENABLED: boolean;
  INITIAL_JSESSIONID: string;
  KEYCLOAK_AUTH_SESSION_ID: string;
  KEYCLOAK_IDENTITY: string;
  KEYCLOAK_SESSION: string;
  KEYCLOAK_LOCALE: string;
  // Fully Dynamic Cookie Configuration
  FULLY_DYNAMIC_COOKIES_ENABLED: boolean;
  SERVICE_ACCOUNT_ID: string;
  SERVICE_ACCOUNT_SECRET: string;
  AUTH_USERNAME: string;
  AUTH_PASSWORD: string;
  // Keycloak Token Exchange Configuration
  KEYCLOAK_TOKEN_EXCHANGE_ENABLED: boolean;
  MCP_CLIENT_ID: string;
  MCP_CLIENT_SECRET: string;
  PERMISSION_SERVICE_CLIENT_ID: string;
  PERMISSION_SERVICE_CLIENT_SECRET: string;
  // Service Endpoints (all use MCP_SERVER_PORT)
  MCP_ENDPOINT_URL: string;
  OAUTH2_METADATA_URL: string;
  OAUTH2_AUTHORIZATION_URL: string;
  OAUTH2_TOKEN_URL: string;
  OAUTH2_REVOCATION_URL: string;
  HEALTH_ENDPOINT_URL: string;
}

/**
 * 获取环境配置
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  const keycloakBaseUrl = process.env.KEYCLOAK_BASE_URL || 'https://keycloak.ingka-dt.cn';
  const keycloakRealm = process.env.KEYCLOAK_REALM || 'master';
  const mcpServerPort = parseInt(process.env.MCP_SERVER_PORT || '3000', 10);
  const mcpServerBaseUrl = process.env.MCP_SERVER_BASE_URL || `http://localhost:${mcpServerPort}`;

  return {
    NODE_ENV: process.env.NODE_ENV || 'development',
    VITE_API_HOST: process.env.VITE_API_HOST || 'https://api-dev-i.ingka-dt.cn',
    VITE_API_HOST_KONG: process.env.VITE_API_HOST_KONG || 'https://api-dev-i.ingka-dt.cn',
    VITE_MASTER_DATA_API_HOST: process.env.VITE_MASTER_DATA_API_HOST || 'https://master-data-api-dev.ingka-dt.cn',
    VITE_MASTER_DATA_API_KEY: process.env.VITE_MASTER_DATA_API_KEY || '',
    AUTH_COOKIES: process.env.AUTH_COOKIES || '',
    X_CUSTOM_REFERRER: process.env.X_CUSTOM_REFERRER || 'https://fe-dev-i.ingka-dt.cn/order-web',
    DEBUG_SERVICE_ADAPTER: process.env.DEBUG_SERVICE_ADAPTER === 'true',
    // Transport Configuration
    TRANSPORT: (process.env.TRANSPORT === 'http' ? 'http' : 'stdio') as 'stdio' | 'http',
    // Unified Port Configuration
    MCP_SERVER_PORT: mcpServerPort,
    MCP_SERVER_BASE_URL: mcpServerBaseUrl,
    // OAuth2 Configuration
    OAUTH2_ENABLED: process.env.OAUTH2_ENABLED === 'true',
    KEYCLOAK_BASE_URL: keycloakBaseUrl,
    KEYCLOAK_REALM: keycloakRealm,
    OAUTH2_CLIENT_ID: process.env.OAUTH2_CLIENT_ID || 'mcp-server',
    OAUTH2_CLIENT_SECRET: process.env.OAUTH2_CLIENT_SECRET || '',
    OAUTH2_REDIRECT_URI: process.env.OAUTH2_REDIRECT_URI || `${mcpServerBaseUrl}/auth/callback`,
    OAUTH2_SCOPES: (process.env.OAUTH2_SCOPES || 'profile,email,roles').split(',').map(s => s.trim()),
    OAUTH2_ADMIN_SCOPES: (process.env.OAUTH2_ADMIN_SCOPES || 'admin,roles').split(',').map(s => s.trim()),
    OAUTH2_WRITE_SCOPES: (process.env.OAUTH2_WRITE_SCOPES || 'write').split(',').map(s => s.trim()),
    OAUTH2_DEFAULT_SCOPE_VALIDATION: (process.env.OAUTH2_DEFAULT_SCOPE_VALIDATION === 'all' ? 'all' : 'any') as 'all' | 'any',
    OAUTH2_ISSUER_URL: `${keycloakBaseUrl}/realms/${keycloakRealm}`,
    // Dynamic Cookie Configuration
    DYNAMIC_COOKIES_ENABLED: process.env.DYNAMIC_COOKIES_ENABLED === 'true',
    INITIAL_JSESSIONID: process.env.INITIAL_JSESSIONID || '',
    KEYCLOAK_AUTH_SESSION_ID: process.env.KEYCLOAK_AUTH_SESSION_ID || '',
    KEYCLOAK_IDENTITY: process.env.KEYCLOAK_IDENTITY || '',
    KEYCLOAK_SESSION: process.env.KEYCLOAK_SESSION || '',
    KEYCLOAK_LOCALE: process.env.KEYCLOAK_LOCALE || 'en',
    // Fully Dynamic Cookie Configuration
    FULLY_DYNAMIC_COOKIES_ENABLED: process.env.FULLY_DYNAMIC_COOKIES_ENABLED === 'true',
    SERVICE_ACCOUNT_ID: process.env.SERVICE_ACCOUNT_ID || '',
    SERVICE_ACCOUNT_SECRET: process.env.SERVICE_ACCOUNT_SECRET || '',
    AUTH_USERNAME: process.env.AUTH_USERNAME || '',
    AUTH_PASSWORD: process.env.AUTH_PASSWORD || '',
    // Keycloak Token Exchange Configuration
    KEYCLOAK_TOKEN_EXCHANGE_ENABLED: process.env.KEYCLOAK_TOKEN_EXCHANGE_ENABLED === 'true',
    MCP_CLIENT_ID: process.env.MCP_CLIENT_ID || 'mcp-mcp-odi',
    MCP_CLIENT_SECRET: process.env.MCP_CLIENT_SECRET || '',
    PERMISSION_SERVICE_CLIENT_ID: process.env.PERMISSION_SERVICE_CLIENT_ID || 'permission-service',
    PERMISSION_SERVICE_CLIENT_SECRET: process.env.PERMISSION_SERVICE_CLIENT_SECRET || '',
    // Service Endpoints (all use MCP_SERVER_PORT)
    MCP_ENDPOINT_URL: `${mcpServerBaseUrl}/mcp`,
    OAUTH2_METADATA_URL: `${mcpServerBaseUrl}/.well-known/oauth-protected-resource`,
    OAUTH2_AUTHORIZATION_URL: `${mcpServerBaseUrl}/authorize`,
    OAUTH2_TOKEN_URL: `${mcpServerBaseUrl}/token`,
    OAUTH2_REVOCATION_URL: `${mcpServerBaseUrl}/revoke`,
    HEALTH_ENDPOINT_URL: `${mcpServerBaseUrl}/health`,
  };
}

/**
 * 验证必需的环境变量
 */
export function validateEnvironment(): { valid: boolean; missing: string[] } {
  const config = getEnvironmentConfig();
  const missing: string[] = [];

  // 检查基础配置
  if (!config.AUTH_COOKIES && !config.OAUTH2_ENABLED) {
    missing.push('AUTH_COOKIES (required when OAuth2 is disabled)');
  }

  if (!config.VITE_API_HOST_KONG) {
    missing.push('VITE_API_HOST_KONG');
  }

  // 检查OAuth2配置（如果启用）
  if (config.OAUTH2_ENABLED) {
    if (!config.OAUTH2_CLIENT_ID) {
      missing.push('OAUTH2_CLIENT_ID');
    }
    if (!config.OAUTH2_CLIENT_SECRET) {
      missing.push('OAUTH2_CLIENT_SECRET');
    }
    if (!config.KEYCLOAK_BASE_URL) {
      missing.push('KEYCLOAK_BASE_URL');
    }
    if (!config.KEYCLOAK_REALM) {
      missing.push('KEYCLOAK_REALM');
    }
  }

  return {
    valid: missing.length === 0,
    missing,
  };
}

/**
 * 打印环境配置信息（隐藏敏感信息）
 */
export function logEnvironmentInfo() {
  const config = getEnvironmentConfig();

  console.error('🌍 Environment Configuration:');
  console.error(`  NODE_ENV: ${config.NODE_ENV}`);
  console.error(`  TRANSPORT: ${config.TRANSPORT}`);
  console.error(`  VITE_API_HOST: ${config.VITE_API_HOST}`);
  console.error(`  VITE_API_HOST_KONG: ${config.VITE_API_HOST_KONG}`);
  console.error(`  VITE_MASTER_DATA_API_HOST: ${config.VITE_MASTER_DATA_API_HOST}`);
  console.error(`  VITE_MASTER_DATA_API_KEY: ${config.VITE_MASTER_DATA_API_KEY ? '***SET***' : '***NOT SET***'}`);
  console.error(`  AUTH_COOKIES: ${config.AUTH_COOKIES ? '***SET***' : '***NOT SET***'}`);

  console.error('🚀 MCP Server Configuration:');
  console.error(`  PORT: ${config.MCP_SERVER_PORT}`);
  console.error(`  BASE_URL: ${config.MCP_SERVER_BASE_URL}`);
  console.error(`  MCP_ENDPOINT: ${config.MCP_ENDPOINT_URL}`);
  console.error(`  HEALTH_ENDPOINT: ${config.HEALTH_ENDPOINT_URL}`);

  console.error('🔐 OAuth2 Configuration:');
  console.error(`  OAUTH2_ENABLED: ${config.OAUTH2_ENABLED}`);
  if (config.OAUTH2_ENABLED) {
    console.error(`  KEYCLOAK_BASE_URL: ${config.KEYCLOAK_BASE_URL}`);
    console.error(`  KEYCLOAK_REALM: ${config.KEYCLOAK_REALM}`);
    console.error(`  OAUTH2_CLIENT_ID: ${config.OAUTH2_CLIENT_ID}`);
    console.error(`  OAUTH2_CLIENT_SECRET: ${config.OAUTH2_CLIENT_SECRET ? '***SET***' : '***NOT SET***'}`);
    console.error(`  OAUTH2_REDIRECT_URI: ${config.OAUTH2_REDIRECT_URI}`);
    console.error(`  OAUTH2_SCOPES: ${config.OAUTH2_SCOPES.join(', ')}`);
    console.error(`  OAUTH2_ISSUER_URL: ${config.OAUTH2_ISSUER_URL}`);

    console.error('🔗 OAuth2 Service Endpoints:');
    console.error(`  METADATA: ${config.OAUTH2_METADATA_URL}`);
    console.error(`  AUTHORIZATION: ${config.OAUTH2_AUTHORIZATION_URL}`);
    console.error(`  TOKEN: ${config.OAUTH2_TOKEN_URL}`);
    console.error(`  REVOCATION: ${config.OAUTH2_REVOCATION_URL}`);
    console.error(`  MCP_SERVER_BASE_URL: ${config.MCP_SERVER_BASE_URL}`);
    console.error(`  MCP_SERVER_PORT: ${config.MCP_SERVER_PORT}`);
  }
}
