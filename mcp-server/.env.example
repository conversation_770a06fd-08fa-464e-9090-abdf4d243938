# ================================
# Orders Portal MCP Server Configuration Template
# ================================
# Copy this file to .env and update the values

# Environment
NODE_ENV=development

# API Endpoints (Required)
VITE_API_HOST=https://admin.ingka-dt.cn/app-api/orders-portal/uat
VITE_API_HOST_KONG=https://fe-dev-i.ingka-dt.cn/order-web
VITE_MASTER_DATA_API_HOST=https://admin.ingka-dt.cn/master-data
VITE_MASTER_DATA_API_KEY=your_api_key_here

# Authentication (Required when OAuth2 disabled)
AUTH_COOKIES=your_session_cookies_here
X_CUSTOM_REFERRER=https://admin.ingka-dt.cn/app/orders-portal/oms/index

# Transport & Debug
TRANSPORT=stdio
DEBUG_SERVICE_ADAPTER=false

# MCP Server Port
MCP_SERVER_PORT=3000

# ================================
# OAuth2 Configuration (Optional)
# ================================
OAUTH2_ENABLED=false
KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn
KEYCLOAK_REALM=master
OAUTH2_CLIENT_ID=your_client_id
OAUTH2_CLIENT_SECRET=your_client_secret
OAUTH2_SCOPES=openid,profile,email
OAUTH2_ADMIN_SCOPES=admin,roles
OAUTH2_WRITE_SCOPES=write
OAUTH2_DEFAULT_SCOPE_VALIDATION=any

# ================================
# Dynamic Cookie Configuration (Optional)
# ================================
# Enable dynamic cookie acquisition instead of static AUTH_COOKIES
DYNAMIC_COOKIES_ENABLED=false
# Initial JSESSIONID for auth flow
INITIAL_JSESSIONID=your_jsessionid_here
# Keycloak session cookies (obtained from browser)
KEYCLOAK_AUTH_SESSION_ID=your_auth_session_id
KEYCLOAK_IDENTITY=your_keycloak_identity_jwt
KEYCLOAK_SESSION=your_keycloak_session
KEYCLOAK_LOCALE=en

# ================================
# Quick Start
# ================================
# 1. Copy this file: cp .env.example .env
# 2. Update AUTH_COOKIES with your session cookies
# 3. Run: npm run dev
# 4. Test: npm run test:quick
