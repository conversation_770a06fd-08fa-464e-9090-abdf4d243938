package com.ikea.digital.permission.server.common;

public enum RespCodeEnum {

    // success
    SUCCESS(200, "SUCCESS"),

    // 4xx
    BAD_REQUEST(400, "bad request"),
    INVALID_CLIENT(400, "invalid client"),
    NOAUTH_ERROR(401,"access denaied"),
    USER_NOT_EXIST(404, "user not found"),
    TOO_MUCH_DATA(405, "too much data"),
    
    AZURE_ERROR(500, "azure error"),
    
    UNKNOW_ERROR(500, "unknow error");
	
	
    
    private final Integer code;
    private final String msg;

    RespCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }
}
