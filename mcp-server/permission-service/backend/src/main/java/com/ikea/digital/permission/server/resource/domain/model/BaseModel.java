/**
 * Project Name:backend
 * File Name:BaseModel.java
 * Package Name:com.ikea.digital.permission.server.resource.domain.model
 * Date:Jun 12, 20245:03:44 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.resource.domain.model;

import java.util.Date;

import com.ikea.digital.permission.core.context.UserHolder;

import lombok.Data;

/**
 * ClassName:BaseModel <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jun 12, 2024 5:03:44 PM <br/>
 * <AUTHOR>	 
 */
@Data
public abstract class BaseModel {
	
	private String creator;
	
	private Date createGmt;

	private String modifier;
	
	private Date modifyGmt;
	
	public void init() {
		this.createGmt = new Date();
		this.creator = UserHolder.getUserInfo() == null ? "" : UserHolder.getUserInfo().getEmail();
	}

}

