/**
 * Project Name:bff
 * File Name:IdpAuthService.java
 * Package Name:com.ikea.mas.permission.service
 * Date:May 16, 20242:22:30 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.mas.permission.service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.ikea.mas.permission.common.Result;
import com.ikea.mas.permission.config.prop.AzureProperties;
import com.ikea.mas.permission.config.prop.PermissionServerProperties;
import com.ikea.mas.permission.dto.StateCacheDto;
import com.ikea.mas.permission.dto.TokenDto;
import com.ikea.mas.permission.utils.JacksonUtils;
import com.ikea.mas.permission.utils.RedisHelper;

import lombok.extern.slf4j.Slf4j;

/**
 * ClassName:IdpAuthService <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     May 16, 2024 2:22:30 PM <br/>
 * <AUTHOR>	 
 */
@Slf4j
@Service
public class IdpAuthService {
	
	@Autowired
	private MicrosoftAuthService microsoftAuthService;
	
	@Autowired
    private AzureProperties azureConfig;
	
	@Autowired
	private RedisHelper redisHelper;
	
	@Autowired
	private PermissionServerProperties pmConfig;
	
	public String getSsoUUIDFromStateMap(String state) {
		return redisHelper.hget(state, "ssoUUID");
	}
	
	public Result<String> createThirdpartyLoginUrl(String ssoUUID, StateCacheDto stateDto) {
		String nonce = UUID.randomUUID().toString();
		redisHelper.hset(stateDto.getState(), "ssoUUID", ssoUUID, stateDto.getExpireTime());
		try {
			String loginUrl = microsoftAuthService.generateMicrosoftLoginUrl(nonce, stateDto.getState(), azureConfig.getSimpleLoginCallbackUrl());
			return Result.ok(loginUrl);
		} catch (Exception e) {
			log.error("state:" + stateDto.getState(), e);
			return Result.fail("build microsoft login url error!");
		}
	}
	
	public Result<String> createThirdpartyLoginUrl(String nonce, String kcState, String redirectUrl, String callBackUrl) {
		// 只有来自/authEndpoint的请求，下面的缓存才有用
		Map<String,String> map = new HashMap<>();
        map.put("nonce", nonce);
        map.put("redirectUrl", redirectUrl);
        redisHelper.hmset(kcState, map, pmConfig.getUrlTimeout());
        try {
        	String loginUrl = microsoftAuthService.generateMicrosoftLoginUrl(nonce, kcState, callBackUrl);
        	return Result.ok(loginUrl);
		} catch (Exception e) {
			log.error("kcState:" + kcState, e);
			return Result.fail("build microsoft login url error!");
		}
	}
	
	public Result<String> keycloakIdpLoginCompleteEndpoint(String code, StateCacheDto kcStateDto, String sessionState,String referer) {
		String kcState = kcStateDto.getState();
		
		TokenDto tokenResult;
		try {
			tokenResult = microsoftAuthService.getMicrosoftToken(code, kcState, sessionState,referer);
		} catch (Exception e) {
			log.error("kcState:" + kcState, e);
			return Result.fail("get microsoft token error!");
		}
		
		String tokeJsonStr = JacksonUtils.object2JsonString(tokenResult);
		DecodedJWT authJwt = JWT.decode(tokenResult.getIdToken());
		String kcode = RandomStringUtils.randomAlphabetic(10) + authJwt.getHeaderClaim("kid").asString() +  RandomStringUtils.randomAlphabetic(10);
		redisHelper.set(kcode, tokeJsonStr, pmConfig.getUrlTimeout());
		redisHelper.delete(kcState);
		String kcLoginCallbackUrl = KeycloakService.generateIdpBorkerEndpointUrl(kcStateDto.getCache().get("redirectUrl") , kcode, kcState);
		return Result.ok(kcLoginCallbackUrl);
	}
	
}