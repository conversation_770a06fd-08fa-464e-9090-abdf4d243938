package com.ikea.digital.permission.server.controller;

import jakarta.validation.Valid;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ikea.digital.permission.core.config.PmPreAuthorize;
import com.ikea.digital.permission.server.common.BaseResponse;
import com.ikea.digital.permission.server.dto.MenuCreateOrUpdateDto;
import com.ikea.digital.permission.server.dto.MenuDeleteDto;
import com.ikea.digital.permission.server.dto.UpdateValidGroup;
import com.ikea.digital.permission.server.service.CreateAndUpdateMenuService;
import com.ikea.digital.permission.server.service.DeleteMenuService;
import com.ikea.digital.permission.server.service.MenuService;
import com.ikea.digital.permission.server.vo.ResourceVo;
import com.ikea.digital.permission.server.vo.SimpleRoleVo;

@RestController
@RequestMapping("/rs/menu")
public class MenuController {
	
	@Autowired
	private DeleteMenuService deleteService;
	
	@Autowired
	private CreateAndUpdateMenuService updateService;
	
	@Autowired
	private MenuService menuService;
	
	@PostMapping("/create")
	@PmPreAuthorize("hasAnyRoleWithClient({'_menuManager','application_admin'}, #vo.clientId )")
	public BaseResponse<?> createMenu(@RequestBody @Valid MenuCreateOrUpdateDto vo){
		updateService.createMenu(vo);
		return BaseResponse.success();
	}
	
	@PostMapping("/update")
	@PmPreAuthorize("hasAnyRoleWithClient({'_menuManager','application_admin'}, #dto.clientId)")
	public BaseResponse<String> updateMenu(@RequestBody @Validated(UpdateValidGroup.class) MenuCreateOrUpdateDto dto){
		updateService.updateMenuAndRoleMenuIdsAttr(dto);
		return BaseResponse.success();
	}
	
	@GetMapping("/delete/{clientId}/{id}")
	@PmPreAuthorize("hasAnyRoleWithClient({'_menuManager','application_admin'}, #clientId)")
	public BaseResponse<?> deleteMenu(@PathVariable("clientId") String clientId, @PathVariable("id") Long id){
		deleteService.deleteMenu(new MenuDeleteDto(id, clientId));
		return BaseResponse.success();
	}
	
	@GetMapping("/list/{clientId}")
	@PmPreAuthorize("hasAnyRoleWithClient({'_menuManager','application_admin'}, #clientId)")
	public BaseResponse<List<ResourceVo>> menuList(@PathVariable("clientId") String clientId){
		List<ResourceVo> result = menuService.listMenu(clientId);
		return BaseResponse.success(result);
	}
	
	@GetMapping("/roles/{clientId}/{menuId}")
	@PmPreAuthorize("hasAnyRoleWithClient({'_menuManager','application_admin'}, #clientId)")
	public BaseResponse<List<SimpleRoleVo>> roleList(@PathVariable("clientId") String clientId, @PathVariable("menuId") Long menuId) {
		List<SimpleRoleVo> result = menuService.listRoles(clientId, menuId);
		return BaseResponse.success(result);
	}
	
}
