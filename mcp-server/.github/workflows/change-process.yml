name: PR Events Workflow

on:
  pull_request:
    types: [opened, closed]
  pull_request_review:
    types: [submitted]
  

permissions:
  contents: read

jobs:
  handle-pr-events:
    runs-on: 
      - self-hosted
      - ubuntu-lts
      - ali
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get PR Title
        if: ${{ github.event.action == 'opened' }}
        id: GetPRTitle
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GIT_TOKEN_CR }}
          script: |
            const perPage = 500;
            const page = 1;
            const { data: pr } = await github.rest.pulls.get({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.issue.number,
              per_page: perPage,
              page: page,
            });
            console.log('PR Title:', pr.title);
            core.setOutput('title', pr.title); // Explicitly set the output
      
      - name: Extract PR Description Fields
        if: ${{ github.event.action == 'opened' }}
        id: ExtractPRFields
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GIT_TOKEN_CR }}
          script: |
            const { data: pullRequest } = await github.rest.pulls.get({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.issue.number,
            });
  
            if (pullRequest.body) {
              console.log('PR Description:', pullRequest.body);
  
              // Parse the PR body into key-value pairs
              const lines = pullRequest.body.split('\n');
              const fields = {};
              for (const line of lines) {
                const [key, ...valueParts] = line.split(':').map(s => s.trim());
                const value = valueParts.join(':'); // Join the rest of the parts to handle colons in the value
                if (key && value) {
                  fields[key] = value;
                }
              }
  
              console.log('Extracted Fields:', fields);
  
              // Set outputs for specific fields
              core.setOutput('component', fields['component'] || 'Not specified');
              core.setOutput('changeType', fields['changeType'] || 'Not specified');
              core.setOutput('changeCompletionDate', fields['changeCompletionDate'] || 'Not specified');
            } else {
              console.log('No description found for this PR.');
              core.setOutput('component', 'No description found');
              core.setOutput('changeType', 'No description found');
              core.setOutput('changeCompletionDate', 'No description found');
            }
      
      - name: Get PR Files and Commits
        id: GetPRFilesAndCommits
        if: ${{ github.event.action == 'opened' || github.event.action == 'closed' || github.event.action == 'submitted' }}
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GIT_TOKEN_CR }}
          script: |
            // Get the list of files changed in the PR
            const { data: files } = await github.rest.pulls.listFiles({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.issue.number,
            });
            console.log('Files changed in this PR:');
            const fileList = files.map(file => file.filename);
            fileList.forEach(file => {
              console.log(`- ${file}`);
            });

            // Get the list of commits in the PR
            const { data: commits } = await github.rest.pulls.listCommits({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.issue.number,
            });
            console.log('Commits in this PR:');
            const commitList = commits.map(commit => ({
              message: commit.commit.message,
              sha: commit.sha,
            }));
            commitList.forEach(commit => {
              console.log(`- ${commit.message} (SHA: ${commit.sha})`);
            });

            // Combine files and commits into a description
            const description = [
              '*Files Changed:*',
              ...fileList.map(file => `- ${file}`),
              '',
              '*Commits:*',
              ...commitList.map(commit => `- ${commit.message} (SHA: ${commit.sha})`),
            ].join('\n');

            console.log('Generated Description:', description);
            core.setOutput('description', description); // Explicitly set the output


      - name: Create Jira Issue
        if: ${{ github.event.action == 'opened' }}
        id: CreateJiraIssue
        uses: china-digital-hub/shared-workflows/actions/jira-issue-action@master
        with:
          jiraBaseUrl: "https://jira.digital.ingka.com"
          authToken: ${{ secrets.JIRA_AUTH_TOKEN }}
          action: created
          assignee: ${{ github.event.pull_request.user.login }}
          project: "MAS"
          issueType: "Change Request"
          changeType: ${{ steps.ExtractPRFields.outputs.changeType }} # Use the changeType from Extract PR Fields step
          changeCompletionDate: ${{ steps.ExtractPRFields.outputs.changeCompletionDate }} # Use the changeCompletionDate from Extract PR Fields step
          component: ${{ steps.ExtractPRFields.outputs.component }} # Use the component from Extract PR Fields step
          summary: ${{ steps.GetPRTitle.outputs.title }} # Use the title from Get PR Title step
          description: ${{ steps.GetPRFilesAndCommits.outputs.description }} # Use the description from Get PR Files and Commits step
          externalReference: "https://git.build.ingka.ikea.com/china-digital-hub/${{ github.event.repository.name }}/pull/${{ github.event.pull_request.number }}"
      
      - name: Add Jira IssueKey to PR
        id: AddJiraIssueKey
        if: ${{ github.event.action == 'opened' }}
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GIT_TOKEN_CR }}
          script: |
            const issueKey = `${{ steps.CreateJiraIssue.outputs.issueKey }}`;
            console.log('Adding comment to PR with content:', issueKey);
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: issueKey,
            });

      - name: Get First PR Comment
        id: GetFirstPRComment
        if: ${{ github.event.action == 'closed' || github.event.action == 'submitted' }}
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GIT_TOKEN_CR }}
          script: |
            const { data: comments } = await github.rest.issues.listComments({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
            });
            if (comments.length > 0) {
              console.log('First comment:', comments[0].body);
            } else {
              console.log('No comments found on this PR.');
            }
            core.setOutput('firstComment', comments.length > 0 ? comments[0].body : 'No comments found');

      - name: Handle Approve Event
        id: HandleApproveEvent
        if: ${{ github.event.action == 'submitted' && github.event.review.state == 'approved' }}
        uses: china-digital-hub/shared-workflows/actions/jira-issue-action@master
        with:
          jiraBaseUrl: "https://jira.digital.ingka.com"
          authToken: ${{ secrets.JIRA_AUTH_TOKEN }}
          action: approved
          approvers: ${{ github.event.review.user.login }}
          issueKey: ${{ steps.GetFirstPRComment.outputs.firstComment }}
          description: ${{ steps.GetPRFilesAndCommits.outputs.description }}

      - name: Handle Merge Event
        if: ${{ github.event.action == 'closed' && github.event.pull_request.merged == true }}
        id: HandleMergeEvent
        uses: china-digital-hub/shared-workflows/actions/jira-issue-action@master
        with:
          jiraBaseUrl: "https://jira.digital.ingka.com"
          authToken: ${{ secrets.JIRA_AUTH_TOKEN }}
          action: merged
          issueKey: ${{ steps.GetFirstPRComment.outputs.firstComment }}
          description: ${{ steps.GetPRFilesAndCommits.outputs.description }}

      - name: Handle Close Event
        if: ${{ github.event.action == 'closed' && github.event.pull_request.merged == false }}
        uses: china-digital-hub/shared-workflows/actions/jira-issue-action@master
        with:
          jiraBaseUrl: "https://jira.digital.ingka.com"
          authToken: ${{ secrets.JIRA_AUTH_TOKEN }}
          action: closed
          issueKey: ${{ steps.GetFirstPRComment.outputs.firstComment }}
          description: ${{ steps.GetPRFilesAndCommits.outputs.description }}
