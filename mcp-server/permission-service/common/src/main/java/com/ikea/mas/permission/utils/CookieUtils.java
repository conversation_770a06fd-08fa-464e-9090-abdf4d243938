package com.ikea.mas.permission.utils;

import java.util.Arrays;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;

import com.ikea.mas.permission.exception.AuthException;

/**
 * created by st.z on 2022年6月7日
 */
public final class CookieUtils {

    public static String readCookie(HttpServletRequest request, String key) {
        if (StringUtils.isBlank(key)) {
            throw new AuthException("key must not be null!");
        }

        Cookie[] cookies = request.getCookies();
        if (cookies == null) {
            return null;
        }

        return Arrays.stream(cookies).filter(t -> key.equals(t.getName())).findFirst().map(Cookie::getValue).orElse(null);
    }
    
    public static Cookie createCookie(String name, String value, String domain, String path, int maxAge, boolean secure) {
        Cookie cookie = new <PERSON>ie(name, value);
        cookie.setDomain(domain);
        cookie.setPath(path);
        cookie.setMaxAge(maxAge);
        cookie.setHttpOnly(true);
        cookie.setSecure(secure);
        return cookie;
    }
}
