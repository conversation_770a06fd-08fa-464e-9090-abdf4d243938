/**
 * Project Name:permission-server
 * File Name:SpringAppContextUtilsInitializer.java
 * Package Name:com.ikea.digital.permission.server.common.util
 * Date:Feb 22, 20242:52:06 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.common.util;

import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;

/**
 * ClassName:SpringAppContextUtilsInitializer <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Feb 22, 2024 2:52:06 PM <br/>
 * <AUTHOR>	 
 */
public class SpringAppContextUtilsInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

	@Override
	public void initialize(ConfigurableApplicationContext applicationContext) {
		SpringAppContextUtils.setAppContext(applicationContext);
	}

}