package com.ikea.digital.permission.dto;

import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 3/4/23
 */
@Data
public class UserBody {
    String userId;
    String name;
    String email;
    String networkId;
    String avatar;

	List<String> storeIds;
    List<String> groups;

    Map<String,List<String>> roleMap;
    Map<String,List<String>> roleStoreMap;
    Map<String,List<ResourceDto>> resourceMap;
    
	Map<String,Map<String,List<String>>> clientRoleStoreMap;
	
	List<String> orgIds;
	
	@Override
	public String toString() {
		return "UserBody [userId=" + userId + ", name=" + name + ", email=" + email + ", networkId=" + networkId
				+ ", avatar=" + avatar  + ", groups=" + groups + ", roleMap=" + roleMap
				+ ", roleStoreMap=" + roleStoreMap + ", resourceMap=" + resourceMap + "]";
	}
}
