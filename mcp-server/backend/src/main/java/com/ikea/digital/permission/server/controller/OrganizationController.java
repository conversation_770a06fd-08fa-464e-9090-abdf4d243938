package com.ikea.digital.permission.server.controller;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ikea.digital.permission.core.config.PmPreAuthorize;
import com.ikea.digital.permission.server.common.BaseResponse;
import com.ikea.digital.permission.server.dto.OrgCreateOrUpdateDto;
import com.ikea.digital.permission.server.dto.OrganizationUserDto;
import com.ikea.digital.permission.server.dto.UpdateValidGroup;
import com.ikea.digital.permission.server.service.OrgService;
import com.ikea.digital.permission.server.vo.OrgStructVo;
import com.ikea.digital.permission.server.vo.OrgUserInfoVo;

@RestController
@RequestMapping("/rs/org")
public class OrganizationController {
	
	@Autowired
	private OrgService orgService;
	
	@GetMapping("/list/{clientId}")
	@PmPreAuthorize("hasAnyRoleWithClient({'_orgManager', 'application_admin'}, #clientId)")
	public BaseResponse<OrgStructVo> orgStructTree(@PathVariable("clientId") String clientId){
		return BaseResponse.success(orgService.orgTree(clientId));
	}
	
	@PostMapping("/create")
	@PmPreAuthorize("hasAnyRoleWithClient({'_orgManager', 'application_admin'}, #vo.clientId)")
	public BaseResponse<?> createOrg(@RequestBody @Valid OrgCreateOrUpdateDto vo){
		orgService.createOrg(vo);
		return BaseResponse.success();
	}
	
	@PostMapping("/update")
	@PmPreAuthorize("hasAnyRoleWithClient({'_orgManager', 'application_admin'}, #vo.clientId)")
	public BaseResponse<?> updateOrg(@RequestBody @Validated(UpdateValidGroup.class) OrgCreateOrUpdateDto vo){
		orgService.updateOrg(vo);
		return BaseResponse.success();
	}
	
	@GetMapping("/delete/{clientId}/{id}")
	@PmPreAuthorize("hasAnyRoleWithClient({'_orgManager', 'application_admin'}, #clientId)")
	public BaseResponse<?> deleteOrg(@PathVariable("clientId") @NotEmpty String clientId, @PathVariable("id") @NotNull Long id){
		orgService.deleteOrg(clientId, id);
		return BaseResponse.success();
	}
	 
	@GetMapping("/users/{clientId}/{orgId}")
	@PmPreAuthorize("hasAnyRoleWithClient({'_orgManager', 'application_admin'}, #clientId)")
	public BaseResponse<List<OrgUserInfoVo>> orgUsers(@PathVariable("clientId") @NotEmpty String clientId, @PathVariable("orgId") @NotNull Long orgId){
		List<OrgUserInfoVo> result = orgService.orgUsers(clientId, orgId);
		return BaseResponse.success(result);
	}
	
	@PostMapping("/joinUser")
	@PmPreAuthorize("hasAnyRoleWithClient({'_orgManager', 'application_admin'}, #vo.clientId)")
	public BaseResponse<?> joinUser(@RequestBody @Valid OrganizationUserDto vo){
		orgService.joinOrgUser(vo);
		return BaseResponse.success();
	}
	
	@PostMapping("/removeUser")
	@PmPreAuthorize("hasAnyRoleWithClient({'_orgManager', 'application_admin'}, #vo.clientId)")
	public BaseResponse<?> removeUser(@RequestBody @Valid OrganizationUserDto vo){
		orgService.removeOrgUser(vo);
		return BaseResponse.success();
	}
	
}
