package com.ikea.digital.permission.auth.rest;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.http.Parameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ikea.digital.permission.auth.service.MicrosoftAuthService;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/idp/auth")
@Slf4j
public class IdpAuthController {

	@Autowired
	private MicrosoftAuthService microsoftAuthService;
	
	/**
	 * refer auth2.0中tokenEndpoint约定{@link https://connect2id.com/products/server/docs/api/token}</br> 
	 * 目前只支持grant_type=authorization_code</br>
	 * req eg: http://192.168.97.38:1999/permission-service/idp/auth/tokenEndpoint,
	 * 			params:{code:-KI3Q9nNR7bRofxmeZoXqbHZGew,grant_type:authorization_code,redirect_uri:http://192.168.44.128:8080/realms/master/broker/PermissionCenterServerIDP/endpoint,
	 * 			client_secret:09c8bfdb-dd11-488c-aa6a-c13e7d9f6193,
	 * 			client_id:47c18b21-9b4b-4a35-9607-60e156bcab30}
	 */
	@PostMapping("/tokenEndpoint")
	public ResponseEntity<String> tokenEndpoint(HttpServletRequest request) {

		byte[] buffer = new byte[8192];
		int len = 0;
		
		try(InputStream in =  request.getInputStream()){
			len = IOUtils.read(request.getInputStream(), buffer);
		} catch(Exception e) {
			log.error("Reads bytes from an input stream error.", e);
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
		}
		
		if(len <= 0) {
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
		}
		
		Parameters parameters = new Parameters();
		parameters.processParameters(buffer, 0, len);
		
		// 此处code为缓存的kcode
		String code = parameters.getParameter("code");
		
		if(StringUtils.isBlank(code)) {
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
		}
		
		//TODO:可以对client和secret进行校验
		
    	String token = microsoftAuthService.createTokenWithCode(code);
    	if(StringUtils.isBlank(token)) {
    		return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(token);
    	}
    	return ResponseEntity.status(HttpStatus.OK).body(token);
	}
	
	@GetMapping("/userInfo")
	public ResponseEntity<Map<String, String>> userInfo(@RequestHeader(value = "Authorization",required = true) String accessToken,HttpServletResponse response) throws IOException {
		log.info("----keycloak.query.userInfoEndpoint,accessToken:{}",accessToken);
		Map<String, String> userInfo = microsoftAuthService.fetchUserInfo(accessToken);
		if(MapUtils.isEmpty(userInfo)) {
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
		}
		
		return ResponseEntity.status(HttpStatus.OK).body(userInfo);
	}
	
}
