package com.ikea.digital.permission.server.exception;

import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ikea.digital.permission.exception.PermissionAccessException;
import com.ikea.digital.permission.server.common.BaseResponse;
import com.ikea.digital.permission.server.common.RespCodeEnum;
import com.ikea.mas.permission.exception.AuthException;

import lombok.extern.slf4j.Slf4j;

/**
 * 全局异常处理类
 * <AUTHOR>
 * @date 12/4/21
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {
	
    @ExceptionHandler(BindException.class)
    @ResponseBody
    public BaseResponse<Object> bindExceptionHandler(BindException e) {
        BaseResponse<Object> result = new BaseResponse<>();
        result.setCode(RespCodeEnum.UNKNOW_ERROR.getCode());
        for (FieldError error : e.getBindingResult().getFieldErrors()) {
            result.setMessage(error.getDefaultMessage());
            break;
        }

        log.error("bindException: {}", result.getMessage(), e);
        return result;
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public BaseResponse<Object> methodArgumentNotValidException(MethodArgumentNotValidException e) {
        BaseResponse<Object> result = new BaseResponse<>();
        result.setCode(RespCodeEnum.UNKNOW_ERROR.getCode());
        if (e.getBindingResult().hasFieldErrors()) {
            result.setMessage(e.getBindingResult().getFieldError().getDefaultMessage());
        }

        log.error("methodArgumentNotValidException: {}", result.getMessage(), e);
        return result;
    }

    @ExceptionHandler({AuthException.class})
    @ResponseBody
    public BaseResponse<Object> businessException(AuthException e) {
        BaseResponse<Object> result = new BaseResponse<>();
        result.setCode(e.getCode());
        result.setMessage(e.getMessage());

        log.error("BusinessException code {}, message {}", result.getCode(), result.getMessage(), e);
        return result;
    }
    
    @ExceptionHandler({PermissionAccessException.class})
    @ResponseBody
    public BaseResponse<Object> businessException(PermissionAccessException e) {
    	BaseResponse<Object> result = new BaseResponse<>();
    	result.setCode(RespCodeEnum.UNKNOW_ERROR.getCode());
    	result.setMessage(e.getMessage());
    	
    	log.error("BusinessException code {}, message {}", result.getCode(), result.getMessage(), e);
    	return result;
    }
    
    @ExceptionHandler(ResourceException.class)
    @ResponseBody
    public BaseResponse<Object> businessException(ResourceException e) {
        BaseResponse<Object> result = new BaseResponse<>();
        result.setCode(RespCodeEnum.UNKNOW_ERROR.getCode());
        result.setMessage(e.getMessage());

        log.error("BusinessException code {}, message {}", result.getCode(), result.getMessage(), e);
        return result;
    }

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public BaseResponse<Object> exception(Exception e) {
        BaseResponse<Object> result = new BaseResponse<>();
        result.setCode(RespCodeEnum.UNKNOW_ERROR.getCode());
        result.setMessage(RespCodeEnum.UNKNOW_ERROR.getMsg());
        log.error("Exception code " + result.getCode() + ", message " + result.getMessage(), e);
        return result;
    }
}
