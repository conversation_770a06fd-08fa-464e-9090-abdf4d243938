package com.ikea.digital.permission.server.resource.domain.model;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.keycloak.representations.idm.RoleRepresentation;

import com.ikea.digital.permission.server.vo.SimpleRoleVo;

import lombok.Data;

@Data
public class Role {
	
	String id;

	String name;
	
	String description;
	
	Map<String,List<String>> attrMap;
	
	List<String> storeIds;
	
	public Role() {}
	
	public Role(RoleRepresentation kcRole) {
		this.id = kcRole.getId();
		this.name = kcRole.getName();
		this.description = kcRole.getDescription();
		this.attrMap = kcRole.getAttributes();
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Role other = (Role) obj;
		return  Objects.equals(id, other.id) && Objects.equals(name, other.name);
	}

	@Override
	public int hashCode() {
		return Objects.hash(id, name);
	}
	
	public static SimpleRoleVo toSimpleRoleVo(Role role) {
		if(Objects.isNull(role)) {
			return null;
		}
		
		SimpleRoleVo vo = new SimpleRoleVo();
		vo.setDescription(role.getDescription());
		vo.setName(role.getName());
		return vo;
	}
}
