/**
 * Project Name:permission-client
 * File Name:AbstractPermissionSender.java
 * Package Name:com.ikea.digital.permission.core
 * Date:Nov 2, 20234:28:23 PM
 * Copyright (c) 2023, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.core;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import com.ikea.digital.permission.common.BizBaseResponse;
import com.ikea.digital.permission.common.BizRespCodeEnum;
import com.ikea.digital.permission.common.Constants;
import com.ikea.digital.permission.core.config.PermissionClientConfig;
import com.ikea.digital.permission.core.context.UserContext;
import com.ikea.digital.permission.dto.UserBody;
import com.ikea.digital.permission.exception.PermissionAccessException;

/**
 * ClassName:AbstractPermissionSender <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Nov 2, 2023 4:28:23 PM <br/>
 * <AUTHOR>	 
 */
public abstract class AbstractPermissionSender {
	
	Logger log = LoggerFactory.getLogger(getClass());
	
	protected final PermissionClientConfig pmClientConfig;

	protected final RestTemplate restTemplate;
	
	protected final MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
    
    public AbstractPermissionSender(PermissionClientConfig pmClientConfig, RestTemplate restTemplate) {
    	 this.pmClientConfig = pmClientConfig;
         this.restTemplate = restTemplate;
         headers.add(Constants.CLIENT_ID, pmClientConfig.getClientId());
         headers.add(Constants.CLIENT_SECRET, pmClientConfig.getSecret());
         if(StringUtils.isNotBlank(pmClientConfig.getApikey())) {
        	 headers.add(Constants.APIKEY, pmClientConfig.getApikey());
         }
	}
    
    protected String buildFullUrl(String url, Map<String, String> queryParams) {
        String params = queryParams.entrySet().stream().map(entry -> entry.getKey() + "=" + entry.getValue()).collect(Collectors.joining("&"));
        return url + "?" + params;
    }
	
	@SuppressWarnings("rawtypes")
	protected ResponseEntity<BizBaseResponse> toLogin(String requestURL, String bizOriginUrl, String refererURL) {
        // param
        Map<String, String> params = new HashMap<>();
        params.put(Constants.CLIENT_ID, pmClientConfig.getClientId());
        String bizBeCallbackUrl = pmClientConfig.getBizBeCallbackUrl();
        if (StringUtils.isBlank(bizBeCallbackUrl)) {
            bizBeCallbackUrl = requestURL;
        }
        params.put(Constants.REDIRECT_URL, bizBeCallbackUrl);
        
        String bizOriginUrlParam = Optional.ofNullable(StringUtils.firstNonBlank(bizOriginUrl, pmClientConfig.getCallbackUrl(), refererURL)).orElse(StringUtils.EMPTY);
        
        if (StringUtils.isNotBlank(bizOriginUrlParam)) {
	        try {
	        	bizOriginUrlParam = URLDecoder.decode(bizOriginUrlParam, "utf-8");
	        } catch (UnsupportedEncodingException e) {
	            log.error("decode bizOriginUrl failed: {}", bizOriginUrlParam);
	            throw new PermissionAccessException(BizRespCodeEnum.UNKNOW_ERROR);
	        }
        }
        
        params.put(Constants.REFERER, bizOriginUrlParam);
        params.put(Constants.INTERNAL_NET, String.valueOf(pmClientConfig.getInternalNet()));

        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(headers);
        log.info("request login start url with headers: {}", headers);
        log.info("request login start url with param: {}", params);
        ResponseEntity<BizBaseResponse> responseEntity = restTemplate.exchange(this.buildFullUrl(pmClientConfig.getLoginUrl(), params), HttpMethod.GET, requestEntity, BizBaseResponse.class);
        log.info("respond login start url with response: {}", responseEntity.getBody());

        if (responseEntity.getStatusCode() != HttpStatus.OK || responseEntity.getBody() == null || !responseEntity.getBody().rspOk()) {
            throw new PermissionAccessException(BizRespCodeEnum.GET_LOGIN_URL_ERROR);
        }

        return responseEntity;
    }
	
	@SuppressWarnings("rawtypes")
	protected ResponseEntity<BizBaseResponse> toLogin(String requestURL, String bizOriginUrl, 
			String refererURL, String customerReferer) {
		return toLogin(requestURL, bizOriginUrl, StringUtils.firstNonBlank(customerReferer, refererURL));
	}

	
	protected UserContext getUserInfo(String userCookie) {
        // param
        Map<String, String> params = new HashMap<>();
        params.put(Constants.USER_TOKEN, userCookie);

        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(headers);
        log.info("request fetch user with param: {}", params);
        ParameterizedTypeReference<BizBaseResponse<UserBody>> reference = new ParameterizedTypeReference<BizBaseResponse<UserBody>>() {
        };
        ResponseEntity<BizBaseResponse<UserBody>> responseEntity = restTemplate.exchange(this.buildFullUrl(pmClientConfig.getUserInfoUrl(), params), HttpMethod.GET, requestEntity, reference);
        log.info("respond fetch user with response: {}", responseEntity.getBody());

        if (responseEntity.getStatusCode() != HttpStatus.OK || responseEntity.getBody() == null) {
            throw new PermissionAccessException(BizRespCodeEnum.GET_USER_INFO_ERROR);
        }

        BizBaseResponse<UserBody> bizBaseResponse = responseEntity.getBody();
        if (bizBaseResponse.rspOk()) {
            return getUserContext(bizBaseResponse);
        }

        if (bizBaseResponse.rspBadRequest()) {
            throw new PermissionAccessException(BizRespCodeEnum.INVALID_CLIENT);
        }

        if (bizBaseResponse.rspServiceError()) {
            log.error("access error, userCookie: {}, body: {}", userCookie, bizBaseResponse);
            throw new PermissionAccessException(BizRespCodeEnum.UNKNOW_ERROR);
        }

        if (bizBaseResponse.rspDenined()) {
            log.warn("access denied, userCookie: {}, body: {}", userCookie, bizBaseResponse);
            return null;
        }
        return null;
    }
	
	private static UserContext getUserContext(BizBaseResponse<UserBody> bizBaseResponse) {
        UserBody userInfo = bizBaseResponse.getData();
        UserContext userContext = new UserContext();
        userContext.setUserId(userInfo.getUserId());
        userContext.setName(userInfo.getName());
        userContext.setEmail(userInfo.getEmail());
        userContext.setNetworkId(userInfo.getNetworkId());
        userContext.setAvatar(userInfo.getAvatar());
        userContext.setGroups(userInfo.getGroups());
        userContext.setRoleMap(userInfo.getRoleMap());
        userContext.setRoleStoreMap(userInfo.getRoleStoreMap());
        userContext.setResourceMap(userInfo.getResourceMap());
        userContext.setStoreIds(userInfo.getStoreIds());
        userContext.setOrgIdList(userInfo.getOrgIds());
        return userContext;
    }

}

