package com.ikea.digital.permission.server.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ikea.digital.permission.core.config.PmPreAuthorize;
import com.ikea.digital.permission.server.common.BaseResponse;
import com.ikea.digital.permission.server.service.ClientService;
import com.ikea.digital.permission.server.service.RoleService;
import com.ikea.digital.permission.server.vo.ClientVo;

@RestController
@RequestMapping("/rs/client")
public class ClientController {
	
	@Autowired
	private ClientService clientService;
	
	@Autowired
	private RoleService roleService;
	
	@GetMapping("/list")
	@PmPreAuthorize("hasAnyRole('clientManager')")
	public BaseResponse<List<ClientVo>> clientList(){
		List<ClientVo> result = clientService.listClient();
		return BaseResponse.success(result);
	}
	
	@GetMapping("/roles/{clientId}")
	@PmPreAuthorize("hasAnyRole('clientManager')")
	public BaseResponse<?> clientList(@PathVariable("clientId") String clientId){
		roleService.getRoles(clientId);
		return BaseResponse.success();
	}
	
}
