FROM artifactory.cloud.ingka-system.cn/cn-digital-hub-docker-virtual/baseimages/gradle:8.5-jdk17 as builder

ARG APP_HOME=/home/<USER>

WORKDIR $APP_HOME

ADD . $APP_HOME

RUN --mount=type=secret,id=gradle.properties,dst=./gradle.properties \
  ./gradlew -p backend clean bootJar --no-watch-fs -g .

FROM artifactory.cloud.ingka-system.cn/cn-digital-hub-docker-virtual/baseimages/openjdk:17-temurin

COPY --from=builder /home/<USER>/backend/build/libs/*.jar /app/app.jar

EXPOSE 8080