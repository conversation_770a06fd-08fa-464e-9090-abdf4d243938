package com.ikea.digital.permission.server.service;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ikea.digital.permission.server.common.OptTypeEnum;
import com.ikea.digital.permission.server.common.util.JacksonUtils;
import com.ikea.digital.permission.server.common.util.KeycloakManager;
import com.ikea.digital.permission.server.dto.GroupCreateOrUpdateDto;
import com.ikea.digital.permission.server.exception.ResourceException;
import com.ikea.digital.permission.server.repository.LogRepository;
import com.ikea.digital.permission.server.resource.domain.model.Group;
import com.ikea.digital.permission.server.resource.domain.model.persistent.LogPO;
import com.ikea.digital.permission.server.vo.GroupVo;

@Service
public class GroupService {
	
	@Autowired
	KeycloakManager keycloakManager;
	
	@Autowired
	private LogRepository logRepository;
	
	public void createGroup(GroupCreateOrUpdateDto dto) {
		if(StringUtils.isNotBlank(dto.getParentGroupId())) {
			Group group = keycloakManager.getGroupById(dto.getParentGroupId());
			if(group == null) {
				throw new ResourceException("不存在组资源");
			}
			keycloakManager.createSubGroup(dto.getName(), dto.getParentGroupId());
			return;
		}
		keycloakManager.createGroup(dto.getName());
		
		logRepository.insert(new LogPO(OptTypeEnum.CREATE_GROUP.getCode(), dto.getName(), JacksonUtils.object2JsonString(dto),
				null, null));
	}
	
	public void updateGroup(GroupCreateOrUpdateDto dto) {
		Group group = keycloakManager.getGroupById(dto.getId());
		if(group == null) {
			throw new ResourceException("不存在组资源");
		}
		keycloakManager.updateGroup(dto.getId(), dto.getName());
		
		Group oldGroup = new Group();
		oldGroup.setId(group.getId());
		oldGroup.setName(group.getName());
		logRepository.insert(new LogPO(OptTypeEnum.EDIT_GROUP.getCode(), dto.getId(), JacksonUtils.object2JsonString(dto),
				JacksonUtils.object2JsonString(oldGroup), null));
	}
	
	public void deleteGroup(String groupId) {
		keycloakManager.deleteGroup(groupId);
		logRepository.insert(new LogPO(OptTypeEnum.DELETE_GROUP.getCode(), groupId, groupId,
				null, null));
	}
	
	public List<GroupVo> groupList() {
		List<Group> list = keycloakManager.groupList();
		return Group.toGroupVoList(list);
	}
	
}