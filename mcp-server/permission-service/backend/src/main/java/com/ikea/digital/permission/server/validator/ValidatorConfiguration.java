package com.ikea.digital.permission.server.validator;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

@Configuration
public class ValidatorConfiguration {

    @Bean
    public LocalValidatorFactoryBean validator(ValidatableValidator validatableValidator) {
        return new ValidatableEnhancedValidator(validatableValidator);
    }
}
