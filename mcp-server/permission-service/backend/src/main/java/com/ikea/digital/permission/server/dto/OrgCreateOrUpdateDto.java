package com.ikea.digital.permission.server.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgCreateOrUpdateDto extends BaseDto {
	
	
	Long parentId;
	
	@NotNull(groups = UpdateValidGroup.class)
	Long id;
	
	@NotEmpty
	String name;
	
	@NotEmpty
	String description;
	
}