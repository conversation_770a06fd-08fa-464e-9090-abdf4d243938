/**
 * Project Name:permission-server
 * File Name:LogRepository.java
 * Package Name:com.ikea.digital.permission.server.resource.domain.repository
 * Date:Jan 30, 20244:40:43 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.repository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.ikea.digital.permission.server.resource.domain.mapper.LogPOMapper;
import com.ikea.digital.permission.server.resource.domain.model.persistent.LogPO;

/**
 * ClassName:LogRepository <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jan 30, 2024 4:40:43 PM <br/>
 * <AUTHOR>	 
 */
@Repository
public class LogRepository {
	
	@Autowired
	private LogPOMapper logMapper;
	
	public long insert(LogPO log) {
		Assert.notNull(log, "log cannot be null");
		return logMapper.insert(log);
	}
	
	

}

