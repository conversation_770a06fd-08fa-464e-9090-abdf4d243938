import jwt from 'jsonwebtoken';
import axios from 'axios';
import { getEnvironmentConfig } from '../utils/env.js';
import { globalPmTokenManager } from './pm-token-manager.js';
import { globalKeycloakTokenExchange } from './keycloak-token-exchange.js';

/**
 * JWT payload structure from Keycloak
 */
interface KeycloakJWTPayload {
  sub: string;           // User ID
  sid: string;           // Session ID
  iss: string;           // Issuer
  aud: string | string[]; // Audience
  exp: number;           // Expiration
  iat: number;           // Issued at
  azp?: string;          // Authorized party (client ID)
  client_id?: string;    // Client ID
  scope?: string;        // Scopes
  email?: string;        // User email
  preferred_username?: string;
  realm_access?: {
    roles: string[];
  };
  resource_access?: {
    [clientId: string]: {
      roles: string[];
    };
  };
  session_state?: string; // Keycloak session state
  [key: string]: any;    // Additional claims
}

/**
 * Configuration for JWT-based cookie acquisition
 */
export interface JWTCookieConfig {
  enabled: boolean;
  baseUrls: {
    authService: string;
    orderWeb: string;
  };
  clientId: string;
  redirectUrl: string;
}

/**
 * Cached cookie entry
 */
interface CookieEntry {
  cookie: string;
  expiresAt: number;
  createdAt: number;
  jwtToken: string;
}

/**
 * JWT-based Cookie Manager
 * Uses JWT token from frontend to obtain session cookies more efficiently
 */
export class JWTCookieManager {
  private cache = new Map<string, CookieEntry>();
  private config: JWTCookieConfig;
  private acquisitionPromises = new Map<string, Promise<string>>();

  constructor(config: JWTCookieConfig) {
    this.config = config;
  }

  /**
   * Get session cookie using JWT token from frontend
   */
  async getSessionCookieFromJWT(jwtToken: string): Promise<string | null> {
    if (!this.config.enabled) {
      console.log('🍪 [JWTCookieManager] JWT-based cookies disabled');
      return null;
    }

    try {
      // Decode JWT to extract session information (without verification for now)
      const payload = this.decodeJWT(jwtToken);
      const cacheKey = `${payload.sub}_${payload.sid}`;

      // Check cache first
      const cached = this.getCachedCookie(cacheKey, jwtToken);
      if (cached) {
        console.log('🎯 [JWTCookieManager] Using cached cookie');
        return cached;
      }

      // Prevent concurrent acquisitions for the same user/session
      if (this.acquisitionPromises.has(cacheKey)) {
        console.log('🔄 [JWTCookieManager] Waiting for ongoing acquisition');
        return await this.acquisitionPromises.get(cacheKey)!;
      }

      // Start new acquisition
      const acquisitionPromise = this.acquireSessionCookieFromJWT(jwtToken, payload);
      this.acquisitionPromises.set(cacheKey, acquisitionPromise);

      try {
        const cookie = await acquisitionPromise;
        return cookie;
      } finally {
        this.acquisitionPromises.delete(cacheKey);
      }

    } catch (error) {
      console.error('❌ [JWTCookieManager] JWT cookie acquisition failed:', error);
      return null;
    }
  }

  /**
   * Decode JWT token without verification (for extracting session info)
   */
  private decodeJWT(token: string): KeycloakJWTPayload {
    try {
      const decoded = jwt.decode(token) as KeycloakJWTPayload;
      if (!decoded) {
        throw new Error('Failed to decode JWT token');
      }
      return decoded;
    } catch (error) {
      throw new Error(`Invalid JWT token: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get cached cookie if valid
   */
  private getCachedCookie(cacheKey: string, currentJWT: string): string | null {
    const entry = this.cache.get(cacheKey);
    if (!entry) {
      return null;
    }

    // Check if JWT token has changed (user re-authenticated)
    if (entry.jwtToken !== currentJWT) {
      console.log('🔄 [JWTCookieManager] JWT token changed, invalidating cache');
      this.cache.delete(cacheKey);
      return null;
    }

    // Check if expired (with 5 minute buffer)
    const bufferTime = 5 * 60 * 1000; // 5 minutes
    if (entry.expiresAt - bufferTime <= Date.now()) {
      console.log('🕐 [JWTCookieManager] Cached cookie expired');
      this.cache.delete(cacheKey);
      return null;
    }

    return entry.cookie;
  }

  /**
   * Acquire session cookie using JWT token
   */
  private async acquireSessionCookieFromJWT(jwtToken: string, payload: KeycloakJWTPayload): Promise<string> {
    console.log('🔄 [JWTCookieManager] Starting JWT-based cookie acquisition');
    console.log(`   User: ${payload.sub}`);
    console.log(`   Session: ${payload.sid}`);
    console.log(`   Client: ${payload.azp || payload.client_id}`);

    try {
      // Method 1: Try Keycloak token exchange (NEW!)
      const exchangedToken = await this.tryKeycloakTokenExchange(jwtToken, payload);
      if (exchangedToken) {
        // Use exchanged token to create pm_user_token
        const pmUserToken = await this.tryDirectPmTokenCreation(exchangedToken, payload);
        if (pmUserToken) {
          this.cacheCookie(payload, jwtToken, pmUserToken);
          return pmUserToken;
        }
      }

      // Method 2: Try direct pm_user_token creation from original JWT
      const directCookie = await this.tryDirectPmTokenCreation(jwtToken, payload);
      if (directCookie) {
        this.cacheCookie(payload, jwtToken, directCookie);
        return directCookie;
      }

      // Method 3: Try direct token exchange if supported
      const exchangeCookie = await this.tryDirectTokenExchange(jwtToken, payload);
      if (exchangeCookie) {
        this.cacheCookie(payload, jwtToken, exchangeCookie);
        return exchangeCookie;
      }

      // Method 4: Try using JWT session reconstruction
      const pmUserToken = await this.tryJWTToPmUserToken(jwtToken, payload);
      if (pmUserToken) {
        const sessionCookie = await this.exchangePmUserTokenForCookie(pmUserToken);
        this.cacheCookie(payload, jwtToken, sessionCookie);
        return sessionCookie;
      }

      throw new Error('All JWT-based cookie acquisition methods failed');

    } catch (error) {
      console.error('❌ [JWTCookieManager] JWT cookie acquisition failed:', error);
      throw error;
    }
  }

  /**
   * Try Keycloak token exchange (NEW METHOD!)
   * Exchange JWT from mcp-mcp-odi client to permission-service client
   */
  private async tryKeycloakTokenExchange(jwtToken: string, payload: KeycloakJWTPayload): Promise<string | null> {
    try {
      console.log('🔄 [JWTCookieManager] Trying Keycloak token exchange');
      console.log(`   Source client: mcp-mcp-odi`);
      console.log(`   Target client: permission-service`);

      const exchangedToken = await globalKeycloakTokenExchange.exchangeToken(jwtToken);
      if (exchangedToken) {
        console.log('✅ [JWTCookieManager] Keycloak token exchange successful');
        return exchangedToken;
      }

      // Fallback: Try client credentials for permission-service
      console.log('🔄 [JWTCookieManager] Trying client credentials fallback');
      const clientToken = await globalKeycloakTokenExchange.getClientCredentialsToken();
      if (clientToken) {
        console.log('✅ [JWTCookieManager] Client credentials token obtained');
        return clientToken;
      }

      return null;
    } catch (error) {
      console.log('⚠️ [JWTCookieManager] Keycloak token exchange failed:', error);
      return null;
    }
  }

  /**
   * Try direct pm_user_token creation from JWT (NEW METHOD!)
   * This creates a pm_user_token directly from JWT without needing HTTP sessions
   */
  private async tryDirectPmTokenCreation(jwtToken: string, payload: KeycloakJWTPayload): Promise<string | null> {
    try {
      console.log('🔧 [JWTCookieManager] Trying direct pm_user_token creation from JWT');

      // Check if we can convert this JWT
      if (!globalPmTokenManager.canConvertJWT(jwtToken)) {
        console.log('⚠️ [JWTCookieManager] JWT cannot be converted to pm_user_token');
        return null;
      }

      // Create session cookie directly from JWT
      const sessionCookie = await globalPmTokenManager.convertJWTToSessionCookie(jwtToken);

      console.log('✅ [JWTCookieManager] Direct pm_user_token creation successful');
      return sessionCookie;

    } catch (error) {
      console.log('⚠️ [JWTCookieManager] Direct pm_user_token creation failed:', error);
      return null;
    }
  }

  /**
   * Try direct token exchange (if the backend supports it)
   */
  private async tryDirectTokenExchange(jwtToken: string, payload: KeycloakJWTPayload): Promise<string | null> {
    try {
      console.log('🔄 [JWTCookieManager] Trying direct token exchange');
      
      // Try calling the user/current endpoint directly with JWT Bearer token
      const response = await axios.get(`${this.config.baseUrls.orderWeb}/order-web/user/current`, {
        headers: {
          'Authorization': `Bearer ${jwtToken}`,
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          'Accept': 'application/json'
        },
        maxRedirects: 0,
        validateStatus: (status) => status === 302 || status === 200
      });

      // Check for session cookie in response
      const setCookieHeaders = response.headers['set-cookie'];
      if (setCookieHeaders) {
        const sessionCookie = this.extractSessionCookie(setCookieHeaders);
        if (sessionCookie) {
          console.log('✅ [JWTCookieManager] Direct token exchange successful');
          return sessionCookie;
        }
      }

      return null;
    } catch (error) {
      console.log('⚠️ [JWTCookieManager] Direct token exchange failed, trying alternative method');
      return null;
    }
  }

  /**
   * Try using JWT session info to reconstruct the authentication flow
   */
  private async tryJWTToPmUserToken(jwtToken: string, payload: KeycloakJWTPayload): Promise<string | null> {
    try {
      console.log('🔄 [JWTCookieManager] Trying JWT session reconstruction');

      // Step 1: Create a new HTTP session by accessing the auth service
      const state = this.generateState();
      const authUrl = `${this.config.baseUrls.authService}/prm-auth/auth/toLogin`;
      const authParams = new URLSearchParams({
        clientId: this.config.clientId,
        redirectUrl: this.config.redirectUrl,
        state: state
      });

      // This creates a new JSESSIONID (we can't use the JWT's session info directly)
      const initialResponse = await axios.get(`${authUrl}?${authParams}`, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        },
        maxRedirects: 0,
        validateStatus: (status) => status === 302 || status === 200
      });

      // Extract the new JSESSIONID from this request
      const jsessionId = this.extractJSessionId(initialResponse.headers['set-cookie'] || []);
      if (!jsessionId) {
        throw new Error('Could not obtain JSESSIONID from auth service');
      }

      console.log('✅ [JWTCookieManager] Created new HTTP session:', jsessionId.substring(0, 8) + '...');

      // Step 2: Get the Keycloak auth URL from the redirect
      const keycloakAuthUrl = initialResponse.headers.location;
      if (!keycloakAuthUrl) {
        throw new Error('No Keycloak auth URL in redirect');
      }

      // Step 3: Try to use the JWT's session info to authenticate with Keycloak
      // This is where we need to bridge JWT session to HTTP session
      const keycloakResponse = await this.tryKeycloakSessionBridge(keycloakAuthUrl, payload);
      if (!keycloakResponse) {
        return null;
      }

      // Step 4: Complete the flow to get pm_user_token
      const pmUserToken = await this.completeAuthFlow(keycloakResponse, jsessionId);
      if (pmUserToken) {
        console.log('✅ [JWTCookieManager] JWT session reconstruction successful');
        return pmUserToken;
      }

      return null;
    } catch (error) {
      console.log('⚠️ [JWTCookieManager] JWT session reconstruction failed:', error);
      return null;
    }
  }

  /**
   * Try to bridge JWT session info with Keycloak HTTP session
   */
  private async tryKeycloakSessionBridge(keycloakAuthUrl: string, payload: KeycloakJWTPayload): Promise<any> {
    try {
      // The challenge: JWT has session ID (sid) but we need HTTP cookies
      // Possible approaches:

      // Approach 1: Try to access Keycloak with session info from JWT
      const sessionCookies = this.constructKeycloakCookiesFromJWT(payload);

      const response = await axios.get(keycloakAuthUrl, {
        headers: {
          'Cookie': sessionCookies,
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        },
        maxRedirects: 0,
        validateStatus: (status) => status === 302 || status === 200
      });

      return response;
    } catch (error) {
      console.log('⚠️ [JWTCookieManager] Keycloak session bridge failed:', error);
      return null;
    }
  }

  /**
   * Attempt to construct Keycloak cookies from JWT payload
   * This is the tricky part - we have session ID but need HTTP cookies
   */
  private constructKeycloakCookiesFromJWT(payload: KeycloakJWTPayload): string {
    // This is where the limitation becomes clear:
    // JWT has session ID (sid) but we need the actual HTTP session cookies
    // that were created when the user logged in through the browser

    const sessionId = payload.sid;
    const userId = payload.sub;

    // We can try to construct cookie names based on Keycloak patterns:
    const cookies = [
      `AUTH_SESSION_ID=${sessionId}`,
      `AUTH_SESSION_ID_LEGACY=${sessionId}`,
      `KEYCLOAK_SESSION="master/${userId}/${sessionId}"`,
      `KEYCLOAK_SESSION_LEGACY="master/${userId}/${sessionId}"`
    ];

    console.log('🔧 [JWTCookieManager] Attempting to construct cookies from JWT session info');
    return cookies.join('; ');
  }

  /**
   * Complete the authentication flow
   */
  private async completeAuthFlow(keycloakResponse: any, jsessionId: string): Promise<string | null> {
    try {
      // Extract authorization code or pm_user_token from the response
      const location = keycloakResponse.headers.location;
      if (location && location.includes('pm_user_token=')) {
        const url = new URL(location);
        return url.searchParams.get('pm_user_token');
      }

      return null;
    } catch (error) {
      console.log('⚠️ [JWTCookieManager] Auth flow completion failed:', error);
      return null;
    }
  }

  /**
   * Extract JSESSIONID from Set-Cookie headers
   */
  private extractJSessionId(setCookieHeaders: string[]): string | null {
    for (const header of setCookieHeaders) {
      const match = header.match(/JSESSIONID=([^;]+)/);
      if (match) {
        return match[1];
      }
    }
    return null;
  }

  /**
   * Exchange pm_user_token for session cookie
   */
  private async exchangePmUserTokenForCookie(pmUserToken: string): Promise<string> {
    console.log('🍪 [JWTCookieManager] Exchanging pm_user_token for session cookie');
    
    const exchangeUrl = `${this.config.baseUrls.orderWeb}/order-web/user/current`;
    const params = new URLSearchParams({
      pm_user_token: pmUserToken,
      biz_origin_url: 'https://admin.ingka-dt.cn/app/orders-portal/oms/index?sso=true'
    });

    const response = await axios.get(`${exchangeUrl}?${params}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      },
      maxRedirects: 0,
      validateStatus: (status) => status === 302
    });

    // Extract session cookie from Set-Cookie header
    const setCookieHeaders = response.headers['set-cookie'];
    if (!setCookieHeaders) {
      throw new Error('No Set-Cookie headers in response');
    }

    const sessionCookie = this.extractSessionCookie(setCookieHeaders);
    if (!sessionCookie) {
      throw new Error('No test_orders-portal cookie found in response');
    }

    console.log('✅ [JWTCookieManager] pm_user_token exchange successful');
    return sessionCookie;
  }

  /**
   * Extract session cookie from Set-Cookie headers
   */
  private extractSessionCookie(setCookieHeaders: string[]): string | null {
    for (const header of setCookieHeaders) {
      if (header.includes('test_orders-portal=')) {
        const match = header.match(/test_orders-portal=([^;]+)/);
        if (match) {
          return `test_orders-portal=${match[1]}`;
        }
      }
    }
    return null;
  }

  /**
   * Cache the session cookie
   */
  private cacheCookie(payload: KeycloakJWTPayload, jwtToken: string, cookie: string): void {
    const now = Date.now();
    const ttl = 3600 * 1000; // 1 hour (from Max-Age=3600)
    const cacheKey = `${payload.sub}_${payload.sid}`;
    
    this.cache.set(cacheKey, {
      cookie,
      expiresAt: now + ttl,
      createdAt: now,
      jwtToken
    });

    console.log('💾 [JWTCookieManager] Cookie cached with 1 hour TTL');
  }

  /**
   * Generate random state for OAuth2 flow
   */
  private generateState(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Clear cached cookies
   */
  clearCache(): void {
    this.cache.clear();
    console.log('🗑️ [JWTCookieManager] Cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { totalCached: number; entries: Array<{ sub: string; sid: string; expiresAt: number; createdAt: number }> } {
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => {
      const [sub, sid] = key.split('_');
      return {
        sub,
        sid,
        expiresAt: entry.expiresAt,
        createdAt: entry.createdAt
      };
    });

    return {
      totalCached: this.cache.size,
      entries
    };
  }
}

/**
 * Create JWT cookie configuration from environment
 */
export function createJWTCookieConfig(): JWTCookieConfig {
  return {
    enabled: true, // Always enabled when JWT tokens are available
    clientId: 'orders-portal',
    redirectUrl: 'https://fe-dev-i.ingka-dt.cn/order-web/user/current',
    baseUrls: {
      authService: 'https://api-dev-mpp-fe.ingka-dt.cn',
      orderWeb: 'https://fe-dev-i.ingka-dt.cn'
    }
  };
}

/**
 * Global JWT cookie manager instance
 */
export const globalJWTCookieManager = new JWTCookieManager(createJWTCookieConfig());
