/**
 * Project Name:backend
 * File Name:DeleteMenuService.java
 * Package Name:com.ikea.digital.permission.server.resource.service
 * Date:Jun 12, 20243:52:30 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.service;

import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.ikea.digital.permission.server.dto.MenuDeleteDto;
import com.ikea.digital.permission.server.dto.ResourceNode;
import com.ikea.digital.permission.server.resource.domain.model.Menu;
import com.ikea.digital.permission.server.resource.domain.model.Role;

/**
 * ClassName:DeleteMenuService <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jun 12, 2024 3:52:30 PM <br/>
 * <AUTHOR>	 
 */
@Service("deleteMenuService")
public class DeleteMenuService extends AbstractMenuService {
	
	public void deleteMenu(MenuDeleteDto dto) {
		Menu menu = menuRepository.selectByPrimaryKey(dto.getId());
		if(Objects.isNull(menu)) {
			log.warn("menu:{} not exist.", dto.getId());
			return;
		}
		// 查询已经关联menuId的role
		List<Role> roles = roleRepository.getRolesWithMenuId(dto.getClientId(), dto.getId());
		if(CollectionUtils.isNotEmpty(roles)) {
			roles.forEach(role -> removeMenuFromRole(role.getName(), dto.getClientId(), dto.getId().toString()));
		}
		menuRepository.deleteMenu(dto);
	}

	@Override
	protected void removeNode(ResourceNode node, String clientId, String menuId) {
		List<Long> broMenuIds = menuRepository.getBrotherMenuIds(clientId, Long.valueOf(menuId));
		if(CollectionUtils.isNotEmpty(broMenuIds)) {
			node.getParent().removeChildrenRecursive(node);
		} else {
			// 没有其他兄弟menu, 所以当前menu删除后，当前menu的parent将变成leaf node，不需要删除
			node.getParent().removeChildren(node);
		}
	}

}