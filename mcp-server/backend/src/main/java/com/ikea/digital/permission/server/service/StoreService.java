package com.ikea.digital.permission.server.service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ikea.digital.permission.server.feign.CupidStoreClient;
import com.ikea.digital.permission.server.feign.model.CommonAPIResponse;
import com.ikea.digital.permission.server.feign.model.CupidStore;
import com.ikea.digital.permission.server.feign.model.StoreDO;

@Service
public class StoreService {

	private static final int API_SUCCESS_CODE = 200;

	@Autowired
	CupidStoreClient cupidStoreClient;
	
	public List<StoreDO> queryAllStores() {
		CommonAPIResponse<List<CupidStore>> storeClientResponse = cupidStoreClient.queryAllStores();
		if (API_SUCCESS_CODE == storeClientResponse.getCode() && CollectionUtils.isNotEmpty(storeClientResponse.getData())) {
			return storeClientResponse.getData().stream().filter(store -> !store.getHide().equals(1))
					.map(store -> new StoreDO(store.getStoreBuCode(),store.getCnName())).collect(Collectors.toList());
		} else {
			return new ArrayList<>();
		}
	}

}
