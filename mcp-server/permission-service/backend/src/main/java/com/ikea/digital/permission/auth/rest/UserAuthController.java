package com.ikea.digital.permission.auth.rest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ikea.digital.permission.auth.service.UserAuthService;
import com.ikea.digital.permission.server.common.BaseResponse;
import com.ikea.digital.permission.server.common.RespCodeEnum;
import com.ikea.digital.permission.server.validator.ClientValidator;
import com.ikea.mas.permission.common.Constants;
import com.ikea.mas.permission.config.prop.PermissionServerProperties;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/user/auth")
@Slf4j
public class UserAuthController {

    @Autowired
    private UserAuthService userAuthService;
    
    @Autowired
    private PermissionServerProperties pmServerConfig;
    
    @Autowired
    private ClientValidator clientValidator;
    
    /**
     * 获取登录跳转地址，跳转到"/permission-service/user/auth/toLogin", 返回地址的requestParam: clientId,redirect_url,state
     * eg: http://localhost:1999/permission-service/user/auth/toLogin?client_id=app1&redirect_url=http%3A%2F%2Flocalhost%3A1001%2Fdemo1%2Fread
     * @param redirectUrl 通常为client的后端地址,loginComplete完成后会跳转到这个地址
     * @param referer referer会在loginComplete完成后拼接在跳转地址(redirectUrl)的biz_origin_url参数上,
     * 				client拦截请求把token写入cookie并redirect到这个biz_origin_url
     */
    @GetMapping("/getLoginUrl")
    public BaseResponse<String> getLoginUrl(@RequestHeader("clientId") String clientId,
                                                 @RequestParam("redirectUrl") String redirectUrl,
                                                 @RequestParam("referer") String referer,
                                                 @RequestParam(name =  "internalNet", required = false, defaultValue = "true") boolean internalNet) {
        if (!validate(clientId, redirectUrl)) {
        	log.error("client:{},redirectUrl:{}不在白名单内！",clientId,redirectUrl);
            return BaseResponse.error(RespCodeEnum.INVALID_CLIENT);
        }
        
        if(!validateReferer(clientId, referer)) {
        	log.error("client:{},referer:{}不在白名单内！", clientId, referer);
            return BaseResponse.error(RespCodeEnum.INVALID_CLIENT);
        }
        
        String loginUrl = userAuthService.createPmLoginUrl(clientId, redirectUrl,referer, internalNet);
        return BaseResponse.success(loginUrl);
    }
    
    @RequestMapping("/logout")
	public BaseResponse<?> logout(HttpServletRequest request, HttpServletResponse response, 
			@RequestParam(value = "post_logout_redirect_uri", required = false) String postLogoutRedirectUri
			,@RequestParam(value = "userToken", required = false) String userToken) {
			String clientId = request.getHeader(Constants.CLIENT_ID);
        	String clientSecret = request.getHeader(Constants.CLIENT_SECRET);
			boolean validateAccess = clientValidator.validateAccess(clientId, clientSecret);
			if(!validateAccess) {
				log.warn("clientId:{} not matched with clientSecret", clientId);
				return BaseResponse.error(RespCodeEnum.INVALID_CLIENT);
			}
			userAuthService.logout(userToken);
			String logoutUrl = userAuthService.getLogOutUrl(postLogoutRedirectUri);
			return BaseResponse.success(logoutUrl);
	}
    
    private boolean validate(String clientId, String redirectUrl) {
        return pmServerConfig.getBizSystemList().stream().anyMatch(t -> t.validate(clientId, redirectUrl));
    }
    
    private boolean validateReferer(String clientId, String referer) {
    	return pmServerConfig.validateReferer(referer);
    }
}