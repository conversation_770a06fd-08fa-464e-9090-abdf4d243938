package com.ikea.mas.permission.utils;

import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * 
 * created by st.z on 2022年6月7日
 */
@Component
public class RedisHelper {
	
	@Autowired
    private RedisTemplate<String, String> redisTemplate;
	
    public void set(String key, String value, long time) {
        redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
    }
	
    public Boolean setNX(String key, String value,long timeout) {
        return redisTemplate.opsForValue().setIfAbsent(key, value,timeout, TimeUnit.SECONDS);
    }
    
    public String get(String key) {
    	if(StringUtils.isBlank(key)) {
    		return null;
    	}
        return redisTemplate.opsForValue().get(key);
    }
    
    public void hset(String key, String field, String value, long time) {
        HashOperations<String, Object, Object> opsHash = redisTemplate.opsForHash();
        opsHash.put(key, field, value);
        redisTemplate.expire(key, time, TimeUnit.SECONDS);
    }
    
    public Long getExpire(String key) {
    	return redisTemplate.getExpire(key);
    }
	
    public void hmset(String key, Map<String,? extends Object> kvMap, long time) {
    	HashOperations<String, Object, Object> opsHash = redisTemplate.opsForHash();
        opsHash.putAll(key,kvMap);
        redisTemplate.expire(key, time, TimeUnit.SECONDS);
    }
    
    public String hget(String key, String field) {
        HashOperations<String, String, String> opsForHash = redisTemplate.opsForHash();
        return opsForHash.get(key, field);
    }
    
    public Map<String, ? extends Object> hgetall(String key) {
        HashOperations<String, String, Object> opsForHash = redisTemplate.opsForHash();
        return opsForHash.entries(key);
    }
    
	public Long hdel(String key, String field) {
		HashOperations<String, String, String> opsForHash = redisTemplate.opsForHash();
		return opsForHash.delete(key, field);
	}
	
	public Boolean exists(String key) {
        return redisTemplate.hasKey(key);
    }
	
	public boolean delete(String key) {
		return redisTemplate.delete(key);
	}
}
