package com.ikea.digital.permission.core.config;

import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;

import lombok.Data;

@Data
@ConfigurationProperties (prefix = "permission.client")
public class PermissionClientConfig {
	private boolean enable;

	private String env;
	private String clientId;
	private String secret;
    
	private String apikey;
	private String loginUrl;
	private String logoutUrl;
    private String callbackUrl;
    private String userInfoUrl;
    
    private String bizBeCallbackUrl;
    
    private String cookiePath = "/";
    private String cookieDomain;
    
    private Boolean internalNet = true;

    private List<String> excludeAuthenticationPath;
    private List<String> includeAuthenticationPath;
    
    private Boolean cookieSecure = false;

}