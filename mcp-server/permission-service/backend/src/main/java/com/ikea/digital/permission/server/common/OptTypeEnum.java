package com.ikea.digital.permission.server.common;

public enum OptTypeEnum {

	ADD_USER(1,"add_user", "创建用户"),
	EDIT_USER(2,"edit_user","编辑用户"),
	DELETE_USER(3,"delete_user","删除用户"),
	ASSIGN_ROLE(4,"assign_role","分配角色"), 
	ASSIGN_GROUP(5,"assign_group", "分配组"), 
	
	ADD_RS(10, "add_rs", "创建资源"),
	EDIT_RS(11, "edit_rs", "编辑资源"), 
	DELETE_RS(12, "delete_rs", "删除资源"),
	
	
	CREATE_ROLE(20, "create_role", "创建角色"),
	EDIT_ROLE(21, "edit_role", "编辑角色"), 
	DELETE_ROLE(22, "delete_role", "编辑角色"),
	
	CREATE_GROUP(30, "create_group", "创建组"),
	EDIT_GROUP(31, "edit_group", "编辑组"),
	DELETE_GROUP(32, "delete_group", "删除组"),
	
	CREATE_ORG(40, "create_org", "创建Org"),
	EDIT_ORG(41, "edit_org", "编辑Org"),
	DELETE_ORG(42, "delete_Org", "删除Org"),
	JOIN_ORG_USER(43, "join_org_user", "添加Org用户"),
	REMOVE_ORG_USER(44, "remove_org_user", "移除Org用户")
	;

	Integer code;
	
	String name;

	String descript;

	OptTypeEnum(Integer code, String name, String descript) {
		this.code = code;
		this.name = name;
		this.descript = descript;
	}

	public Integer getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

	public String getDescript() {
		return descript;
	}

}
