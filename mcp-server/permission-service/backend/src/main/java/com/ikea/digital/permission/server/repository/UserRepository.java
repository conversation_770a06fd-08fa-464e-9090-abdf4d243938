/**
 * Project Name:backend
 * File Name:UserRepository.java
 * Package Name:com.ikea.digital.permission.server.repository
 * Date:Dec 4, 20242:12:47 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.repository;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.ikea.digital.permission.server.common.util.KeycloakManager;
import com.ikea.digital.permission.server.resource.domain.model.User;

/**
 * ClassName:UserRepository <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Dec 4, 2024 2:12:47 PM <br/>
 * <AUTHOR>	 
 */
@Repository
public class UserRepository {
	
	@Autowired
	private KeycloakManager keycloakManager;
	

	public User fetchUserById(String userId){
		return keycloakManager.getUserById(userId);
	}
	
	public Optional<User> searchSingleUserByUsername(String userName) {
		return keycloakManager.searchSingleUserByUsername(userName);
	}
}

