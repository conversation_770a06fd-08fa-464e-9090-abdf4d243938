import axios, { AxiosResponse } from 'axios';
import { getEnvironmentConfig } from '../utils/env.js';

/**
 * Configuration for dynamic cookie acquisition
 */
export interface DynamicCookieConfig {
  enabled: boolean;
  initialJSessionId: string;
  keycloakCookies: {
    authSessionId: string;
    keycloakIdentity: string;
    keycloakSession: string;
    keycloakLocale?: string;
  };
  clientId: string;
  redirectUrl: string;
  baseUrls: {
    authService: string;
    keycloak: string;
    orderWeb: string;
  };
}

/**
 * Cached cookie entry
 */
interface CookieEntry {
  cookie: string;
  expiresAt: number;
  createdAt: number;
}

/**
 * Dynamic Cookie Manager
 * Implements the complete OAuth2/OIDC flow to obtain session cookies dynamically
 */
export class DynamicCookieManager {
  private cache = new Map<string, CookieEntry>();
  private config: DynamicCookieConfig;
  private isAcquiring = false;
  private acquisitionPromise?: Promise<string>;

  constructor(config: DynamicCookieConfig) {
    this.config = config;
  }

  /**
   * Get a valid session cookie, acquiring one if necessary
   */
  async getSessionCookie(): Promise<string | null> {
    if (!this.config.enabled) {
      console.log('🍪 [DynamicCookieManager] Dynamic cookies disabled');
      return null;
    }

    // Check cache first
    const cached = this.getCachedCookie();
    if (cached) {
      console.log('🎯 [DynamicCookieManager] Using cached cookie');
      return cached;
    }

    // Prevent concurrent acquisitions
    if (this.isAcquiring && this.acquisitionPromise) {
      console.log('🔄 [DynamicCookieManager] Waiting for ongoing acquisition');
      return await this.acquisitionPromise;
    }

    // Start new acquisition
    this.isAcquiring = true;
    this.acquisitionPromise = this.acquireNewCookie();

    try {
      const cookie = await this.acquisitionPromise;
      return cookie;
    } finally {
      this.isAcquiring = false;
      this.acquisitionPromise = undefined;
    }
  }

  /**
   * Get cached cookie if valid
   */
  private getCachedCookie(): string | null {
    const entry = this.cache.get('session_cookie');
    if (!entry) {
      return null;
    }

    // Check if expired (with 5 minute buffer)
    const bufferTime = 5 * 60 * 1000; // 5 minutes
    if (entry.expiresAt - bufferTime <= Date.now()) {
      console.log('🕐 [DynamicCookieManager] Cached cookie expired');
      this.cache.delete('session_cookie');
      return null;
    }

    return entry.cookie;
  }

  /**
   * Acquire a new session cookie through the complete OAuth2 flow
   */
  private async acquireNewCookie(): Promise<string> {
    console.log('🔄 [DynamicCookieManager] Starting cookie acquisition flow');
    
    try {
      // Step 1: Initial auth request
      const pmUserToken = await this.step1_InitialAuth();
      
      // Step 2: Exchange pm_user_token for session cookie
      const sessionCookie = await this.step2_ExchangeForCookie(pmUserToken);
      
      // Cache the cookie
      this.cacheCookie(sessionCookie);
      
      console.log('✅ [DynamicCookieManager] Cookie acquisition successful');
      return sessionCookie;
      
    } catch (error) {
      console.error('❌ [DynamicCookieManager] Cookie acquisition failed:', error);
      throw new Error(`Failed to acquire session cookie: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Step 1: Complete OAuth2 flow to get pm_user_token
   */
  private async step1_InitialAuth(): Promise<string> {
    const state = this.generateState();
    
    // Step 1a: Initial auth request
    console.log('🔐 [DynamicCookieManager] Step 1a: Initial auth request');
    const authUrl = `${this.config.baseUrls.authService}/prm-auth/auth/toLogin`;
    const authParams = new URLSearchParams({
      clientId: this.config.clientId,
      redirectUrl: this.config.redirectUrl,
      state: state
    });

    const authResponse = await axios.get(`${authUrl}?${authParams}`, {
      headers: {
        'Cookie': `JSESSIONID=${this.config.initialJSessionId}`,
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      },
      maxRedirects: 0,
      validateStatus: (status) => status === 302
    });

    const keycloakUrl = authResponse.headers.location;
    if (!keycloakUrl) {
      throw new Error('No redirect URL from initial auth request');
    }

    // Step 1b: Keycloak OAuth2 request
    console.log('🔐 [DynamicCookieManager] Step 1b: Keycloak OAuth2 request');
    const keycloakCookies = this.buildKeycloakCookies();
    
    const keycloakResponse = await axios.get(keycloakUrl, {
      headers: {
        'Cookie': keycloakCookies,
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      },
      maxRedirects: 0,
      validateStatus: (status) => status === 302
    });

    const loginCompleteUrl = keycloakResponse.headers.location;
    if (!loginCompleteUrl) {
      throw new Error('No redirect URL from Keycloak');
    }

    // Step 1c: Complete login
    console.log('🔐 [DynamicCookieManager] Step 1c: Complete login');
    const loginResponse = await axios.get(loginCompleteUrl, {
      headers: {
        'Cookie': `JSESSIONID=${this.config.initialJSessionId}`,
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      },
      maxRedirects: 0,
      validateStatus: (status) => status === 302
    });

    const finalRedirectUrl = loginResponse.headers.location;
    if (!finalRedirectUrl) {
      throw new Error('No final redirect URL from login complete');
    }

    // Extract pm_user_token from URL
    const url = new URL(finalRedirectUrl);
    const pmUserToken = url.searchParams.get('pm_user_token');
    if (!pmUserToken) {
      throw new Error('No pm_user_token in final redirect URL');
    }

    console.log('✅ [DynamicCookieManager] Step 1 complete: pm_user_token acquired');
    return pmUserToken;
  }

  /**
   * Step 2: Exchange pm_user_token for session cookie
   */
  private async step2_ExchangeForCookie(pmUserToken: string): Promise<string> {
    console.log('🍪 [DynamicCookieManager] Step 2: Exchange for session cookie');
    
    const exchangeUrl = `${this.config.baseUrls.orderWeb}/order-web/user/current`;
    const params = new URLSearchParams({
      pm_user_token: pmUserToken,
      biz_origin_url: 'https://admin.ingka-dt.cn/app/orders-portal/oms/index?sso=true'
    });

    const response = await axios.get(`${exchangeUrl}?${params}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      },
      maxRedirects: 0,
      validateStatus: (status) => status === 302
    });

    // Extract session cookie from Set-Cookie header
    const setCookieHeaders = response.headers['set-cookie'];
    if (!setCookieHeaders) {
      throw new Error('No Set-Cookie headers in response');
    }

    const sessionCookie = this.extractSessionCookie(setCookieHeaders);
    if (!sessionCookie) {
      throw new Error('No test_orders-portal cookie found in response');
    }

    console.log('✅ [DynamicCookieManager] Step 2 complete: session cookie acquired');
    return sessionCookie;
  }

  /**
   * Build Keycloak cookies string
   */
  private buildKeycloakCookies(): string {
    const cookies = [
      `AUTH_SESSION_ID=${this.config.keycloakCookies.authSessionId}`,
      `AUTH_SESSION_ID_LEGACY=${this.config.keycloakCookies.authSessionId}`,
      `KEYCLOAK_IDENTITY=${this.config.keycloakCookies.keycloakIdentity}`,
      `KEYCLOAK_IDENTITY_LEGACY=${this.config.keycloakCookies.keycloakIdentity}`,
      `KEYCLOAK_SESSION=${this.config.keycloakCookies.keycloakSession}`,
      `KEYCLOAK_SESSION_LEGACY=${this.config.keycloakCookies.keycloakSession}`
    ];

    if (this.config.keycloakCookies.keycloakLocale) {
      cookies.push(`KEYCLOAK_LOCALE=${this.config.keycloakCookies.keycloakLocale}`);
    }

    return cookies.join('; ');
  }

  /**
   * Extract session cookie from Set-Cookie headers
   */
  private extractSessionCookie(setCookieHeaders: string[]): string | null {
    for (const header of setCookieHeaders) {
      if (header.includes('test_orders-portal=')) {
        const match = header.match(/test_orders-portal=([^;]+)/);
        if (match) {
          return `test_orders-portal=${match[1]}`;
        }
      }
    }
    return null;
  }

  /**
   * Cache the session cookie
   */
  private cacheCookie(cookie: string): void {
    const now = Date.now();
    const ttl = 3600 * 1000; // 1 hour (from Max-Age=3600)
    
    this.cache.set('session_cookie', {
      cookie,
      expiresAt: now + ttl,
      createdAt: now
    });

    console.log('💾 [DynamicCookieManager] Cookie cached with 1 hour TTL');
  }

  /**
   * Generate random state for OAuth2 flow
   */
  private generateState(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Clear cached cookies
   */
  clearCache(): void {
    this.cache.clear();
    console.log('🗑️ [DynamicCookieManager] Cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { hasCached: boolean; expiresAt?: number; createdAt?: number } {
    const entry = this.cache.get('session_cookie');
    return {
      hasCached: !!entry,
      expiresAt: entry?.expiresAt,
      createdAt: entry?.createdAt
    };
  }
}

/**
 * Create dynamic cookie configuration from environment
 */
export function createDynamicCookieConfig(): DynamicCookieConfig {
  const env = getEnvironmentConfig();
  
  return {
    enabled: env.DYNAMIC_COOKIES_ENABLED || false,
    initialJSessionId: env.INITIAL_JSESSIONID || '',
    keycloakCookies: {
      authSessionId: env.KEYCLOAK_AUTH_SESSION_ID || '',
      keycloakIdentity: env.KEYCLOAK_IDENTITY || '',
      keycloakSession: env.KEYCLOAK_SESSION || '',
      keycloakLocale: env.KEYCLOAK_LOCALE || 'en'
    },
    clientId: 'orders-portal',
    redirectUrl: 'https://fe-dev-i.ingka-dt.cn/order-web/user/current',
    baseUrls: {
      authService: 'https://api-dev-mpp-fe.ingka-dt.cn',
      keycloak: 'https://keycloak.ingka-dt.cn',
      orderWeb: 'https://fe-dev-i.ingka-dt.cn'
    }
  };
}

/**
 * Global dynamic cookie manager instance
 */
export const globalDynamicCookieManager = new DynamicCookieManager(createDynamicCookieConfig());
