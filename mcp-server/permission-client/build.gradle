plugins {
    id 'maven-publish'
}

group = 'com.ikea.digital'
version = '2.1.0-SNAPSHOT'

bootJar.enabled = false

java {
    //withJavadocJar()
    withSourcesJar()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-aop'

    implementation group: 'org.apache.commons', name: 'commons-lang3'
    implementation group: 'org.apache.commons', name: 'commons-collections4'
    implementation 'org.apache.httpcomponents.client5:httpclient5'
    
    compileOnly 'org.springframework.boot:spring-boot-starter-web'
    compileOnly 'org.springframework.boot:spring-boot-starter-webflux'
    
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
}

publishing {
    publications {
        library(MavenPublication) {
            group = project.group
            artifactId 'permission-client'
            version project.version
            from components.java
            versionMapping {
                usage('java-api') {
                    fromResolutionOf('runtimeClasspath')
                }
                usage('java-runtime') {
                    fromResolutionResult()
                }
            }
        }
    }

    repositories {
        maven {
            credentials {
                username = "${RT_CDH_USERNAME}"
                password = "${RT_CDH_TOKEN}"
            }
            def releasesRepoUrl = 'https://artifactory.cloud.ingka-system.cn:443/artifactory/mas-maven-release-local/'
            def snapshotsRepoUrl = 'https://artifactory.cloud.ingka-system.cn:443/artifactory/mas-maven-snapshot-local/'
            url = project.version.endsWith('SNAPSHOT') ? snapshotsRepoUrl : releasesRepoUrl
        }
    }
}
