package com.ikea.digital.permission.server.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ikea.digital.permission.core.context.UserHolder;
import com.ikea.digital.permission.server.common.OptTypeEnum;
import com.ikea.digital.permission.server.common.util.JacksonUtils;
import com.ikea.digital.permission.server.common.util.KeycloakManager;
import com.ikea.digital.permission.server.dto.OrgCreateOrUpdateDto;
import com.ikea.digital.permission.server.dto.OrganizationUserDto;
import com.ikea.digital.permission.server.exception.ResourceException;
import com.ikea.digital.permission.server.repository.LogRepository;
import com.ikea.digital.permission.server.repository.OrgRepository;
import com.ikea.digital.permission.server.repository.OrgUserRepository;
import com.ikea.digital.permission.server.resource.domain.model.User;
import com.ikea.digital.permission.server.resource.domain.model.persistent.LogPO;
import com.ikea.digital.permission.server.resource.domain.model.persistent.OrgPO;
import com.ikea.digital.permission.server.resource.domain.model.persistent.OrgUserPO;
import com.ikea.digital.permission.server.vo.OrgStructVo;
import com.ikea.digital.permission.server.vo.OrgUserInfoVo;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class OrgService {

	@Autowired
	private OrgRepository orgRepository;
	
	@Autowired
	private OrgUserRepository orgUserRepository;
	
	@Autowired
	private KeycloakManager keycloakManager;
	
	@Autowired
	private LogRepository logRepository;
	
	@Autowired
	private UserCacheService userCacheService;

	public OrgStructVo orgTree(String clientId) {
		
		List<OrgPO> orgList = orgRepository.getByClientId(clientId);
		
		if(CollectionUtils.isEmpty(orgList)) {
			return null;
		}
		
        List<OrgStructVo> orgStructDtoList = OrgPO.toOrgStructDto(orgList);
        for(OrgStructVo orgStructDto : orgStructDtoList) {
			List<OrgStructVo> subOrgStructList = orgStructDtoList.stream().filter(a -> a.getDeep() == orgStructDto.getDeep() + 1 && a.getPath().startsWith(orgStructDto.getPath())).collect(Collectors.toList());
			orgStructDto.setSubOrgStructDto(subOrgStructList);
		}
        
        return orgStructDtoList.stream().filter(a -> a.getDeep() == 1).findFirst().orElseGet(null);
	}

	
	public void createOrg(OrgCreateOrUpdateDto dto) {
		OrgPO parentPo = this.checkParam(dto);
		OrgPO record = new OrgPO();
		record.setName(dto.getName());
		record.setDescription(dto.getDescription());
		record.setClientId(dto.getClientId());
		record.setDeep(parentPo == null ? 1 : parentPo.getDeep() + 1);
		record.setPath(parentPo == null ? "/" + dto.getName() : parentPo.getPath() + "/" + dto.getName());
		orgRepository.insert(record);
		
		logRepository.insert(new LogPO(OptTypeEnum.CREATE_ORG.getCode(), dto.getName(), JacksonUtils.object2JsonString(dto),
				null, null));
		return;
	}

	private OrgPO checkParam(OrgCreateOrUpdateDto dto) {
		OrgPO po = orgRepository.getOneByClientIdAndName(dto.getClientId(), dto.getName());
		if (Objects.nonNull(po)) {
			throw new ResourceException("名称已存在！");
		}

		Long parentId = dto.getParentId();
		if (Objects.isNull(parentId)) {
			OrgPO firstStruct = orgRepository.getRootOrgByClientId(dto.getClientId());
			if (Objects.nonNull(firstStruct)) {
				throw new ResourceException("parentId不能为空!");
			}
			return null;
		}
		
		OrgPO parentPo = orgRepository.getByPrimaryKey(parentId);
		if (Objects.isNull(parentPo)) {
			throw new ResourceException("parentId无效!");
		}

		return parentPo;
	}

	@Transactional
	public void updateOrg(OrgCreateOrUpdateDto dto) {
		// 0、校验
		OrgPO oldOrgPo = orgRepository.getByPrimaryKey(dto.getId());
		if (oldOrgPo == null) {
			throw new ResourceException("id无效!");
		}
		OrgPO po = orgRepository.getOneByClientIdAndName(dto.getClientId(), dto.getName());
		if (po != null && !po.getId().equals(dto.getId()) ) {
			throw new ResourceException("名称已存在！");
		}

		// 1、更新path、及基本信息
		String oldPath = oldOrgPo.getPath();
		String newPath = oldPath.replaceAll(oldOrgPo.getName() + "$", dto.getName());
		orgRepository.updatePath(dto.getClientId(), oldPath, newPath);

		OrgPO newOrgPo = new OrgPO();
		newOrgPo.setId(oldOrgPo.getId());
		newOrgPo.setName(dto.getName());
		newOrgPo.setDescription(dto.getDescription());
		newOrgPo.setModifier(UserHolder.getUserInfo().getEmail());
		newOrgPo.setModifyGmt(new Date());
		orgRepository.updateByPrimaryKeySelective(newOrgPo);
		
		logRepository.insert(new LogPO(OptTypeEnum.EDIT_ORG.getCode(), dto.getName(), JacksonUtils.object2JsonString(dto),
				JacksonUtils.object2JsonString(oldOrgPo), JacksonUtils.object2JsonString(newOrgPo)));
		
	}

	public void deleteOrg(String clientId, Long orgId) {
		// 0、判断当前组及子组，是否存在用户
		OrgPO orgPo = orgRepository.getByPrimaryKey(orgId);
		
		List<OrgPO> subOrg = orgRepository.getByClientIdAndPathLike(clientId, orgPo.getPath());
		
		List<Long> subOrgIds = subOrg.stream().map(OrgPO::getId).collect(Collectors.toList());
		
		List<OrgUserPO> orgUserList = orgUserRepository.getByClientIdAndOrgIdIn(clientId, subOrgIds);
		
		if(CollectionUtils.isNotEmpty(orgUserList)) {
			Set<Long> orgIds = orgUserList.stream().map(OrgUserPO::getOrgId).collect(Collectors.toSet());
			List<String> orgNames = subOrg.stream().filter(a -> orgIds.contains(a.getId())).map(OrgPO::getName).collect(Collectors.toList());
			throw new ResourceException("组" + orgNames + "内存在用户,不允许删除");
		}
		
		// 1、删除当前组及子组数据
		orgRepository.deleteWithIdIn(subOrgIds);
		
		logRepository.insert(new LogPO(OptTypeEnum.DELETE_ORG.getCode(), String.valueOf(orgId), 
				JacksonUtils.kv2JsonString("id", orgId, "clientId", clientId),
				null, null));
	}

	public List<OrgUserInfoVo> orgUsers(String clientId, Long orgId) {
		List<OrgUserPO> orgUsers = orgUserRepository.getByClientIdAndOrgId(clientId, orgId);
		
		List<String> emails = orgUsers.stream().map(OrgUserPO::getUserEmail).collect(Collectors.toList());
		List<OrgUserInfoVo> userData = new ArrayList<>();
		for(String email : emails) {
			Optional<User> userOpt = keycloakManager.searchSingleUserByUsername(email);
			if(userOpt.isEmpty()) {
				continue;
			}
			OrgUserInfoVo userDto = new OrgUserInfoVo();
			userDto.setName(userOpt.get().getName());
			userDto.setEmail(email);
			userData.add(userDto);
		}
		return userData;
	}

	public void joinOrgUser(OrganizationUserDto dto) {
		OrgPO orgPo = orgRepository.getByPrimaryKey(dto.getOrgId());
		if(orgPo == null) {
			throw new ResourceException("orgId无效");
		}
		if(!orgPo.getClientId().equals(dto.getClientId())) {
			throw new ResourceException("clientId与orgId不匹配");
		}
		// 0、过滤已在组内的用户
		Set<String> emailList = dto.getUserEmail();
		List<OrgUserPO> orgUsers = orgUserRepository.getByClientIdAndOrgId(dto.getClientId(), dto.getOrgId());
		if(CollectionUtils.isNotEmpty(orgUsers)) {
			List<String> joinedUser = orgUsers.stream().map(OrgUserPO::getUserEmail).collect(Collectors.toList());
			List<String> repeatUser = dto.getUserEmail().stream().filter(a -> joinedUser.contains(a)).collect(Collectors.toList());
			log.warn("joidUser:{},repeatUseer:{}",dto, repeatUser);
			emailList.removeAll(repeatUser);
		}
		
		// 1、添加用户
		List<OrgUserPO> newRecord = new ArrayList<>(); 
		emailList.forEach(email -> {
			OrgUserPO orgUserPo = new OrgUserPO();
			orgUserPo.setClientId(dto.getClientId());
			orgUserPo.setOrgId(dto.getOrgId());
			orgUserPo.setUserEmail(email);
			newRecord.add(orgUserPo);
			// 刷新用户缓存
			Optional<User> userOpt = keycloakManager.searchSingleUserByUsername(email);
			if(userOpt.isPresent()) {
				userCacheService.clearNewUserCache(dto.getClientId(), userOpt.get().getUserId());
			}
		});
		if(CollectionUtils.isNotEmpty(newRecord)) {
			orgUserRepository.insertList(newRecord);
			logRepository.insert(new LogPO(OptTypeEnum.JOIN_ORG_USER.getCode(), String.valueOf(dto.getOrgId()), JacksonUtils.object2JsonString(dto),
					null, null));
		}
	}

	public void removeOrgUser(OrganizationUserDto dto) {
		// 删除用户缓存
		dto.getUserEmail().forEach(email -> {
			Optional<User> userOpt = keycloakManager.searchSingleUserByUsername(email);
			if(userOpt.isPresent()) {
				userCacheService.clearNewUserCache(dto.getClientId(), userOpt.get().getUserId());
			}
		});
		
		orgUserRepository.deleteOrgUser(dto.getClientId(), dto.getOrgId(), dto.getUserEmail());
		logRepository.insert(new LogPO(OptTypeEnum.REMOVE_ORG_USER.getCode(), String.valueOf(dto.getOrgId()), JacksonUtils.object2JsonString(dto),
				null, null));
	}
	
	public List<OrgUserInfoVo> childUser(OrganizationUserDto dto) {
		// 0、查询用户所在组
		List<OrgUserPO> userOrgs = orgUserRepository.getByClientIdAndUserEmailIn(dto.getClientId(), dto.getUserEmail());
		if(CollectionUtils.isEmpty(userOrgs)) {
			return Collections.emptyList();
		}
		// 1、查所在组的子组
		List<Long> userOrgIds = userOrgs.stream().map(OrgUserPO::getOrgId).collect(Collectors.toList());
		List<OrgPO> orgList = orgRepository.getByClientIdAndIds(dto.getClientId(), userOrgIds);
		List<String> pathList = orgList.stream().map(org -> org.getPath() + "/").collect(Collectors.toList());
		List<OrgPO> subOrgList = orgRepository.getByClientIdAndPathLikeOr(dto.getClientId(), pathList);
		
		// 2、查子组下的用户
		if(CollectionUtils.isEmpty(subOrgList)) {
			return Collections.emptyList();
		}
		List<Long> subOrgIds = subOrgList.stream().map(OrgPO::getId).collect(Collectors.toList());
		List<OrgUserPO> childUserOrgs = orgUserRepository.getByClientIdAndOrgIdIn(dto.getClientId(), subOrgIds);
		List<OrgUserInfoVo> userData = new ArrayList<>();
		List<String> emailData = childUserOrgs.stream().map(OrgUserPO::getUserEmail).distinct().collect(Collectors.toList());
		for(String email : emailData) {
			OrgUserInfoVo userDto = new OrgUserInfoVo();
			userDto.setEmail(email);
			userData.add(userDto);
		}
		return userData;
	}
	
	public List<Long> getOrgIdList(String email, String clientId){
		// 用户所在组
		List<OrgUserPO> userOrgs = orgUserRepository.getByClientIdAndUserEmailIn(clientId, Collections.singletonList(email));
		if(CollectionUtils.isEmpty(userOrgs)) {
			return Collections.emptyList();
		}
		
		// 查所在组的子组
		List<Long> orgIds = userOrgs.stream().map(OrgUserPO::getOrgId).collect(Collectors.toList());
		List<OrgPO> orgList = orgRepository.getByClientIdAndIds(clientId, orgIds);
		List<String> pathList = orgList.stream().map(org -> org.getPath().concat("/")).collect(Collectors.toList());
		List<OrgPO> subOrgList = orgRepository.getByClientIdAndPathLikeOr(clientId, pathList);
		
		if(CollectionUtils.isNotEmpty(subOrgList)) {
			List<Long> subOrgIds = subOrgList.stream().map(OrgPO::getId).collect(Collectors.toList());
			orgIds.addAll(subOrgIds);
		}
		
		return orgIds;
	}
	
	public List<String> getUsers(String clientId, List<Long> orgIds) {
		List<OrgUserPO> orgUsers = orgUserRepository.getByClientIdAndOrgIdIn(clientId, orgIds);
		if(CollectionUtils.isEmpty(orgUsers)) {
			return Collections.emptyList();
		}
		return orgUsers.stream().map(OrgUserPO::getUserEmail).collect(Collectors.toList());
	}

}
