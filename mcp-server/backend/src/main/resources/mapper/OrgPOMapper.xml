<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ikea.digital.permission.server.resource.domain.mapper.OrgPOMapper">
  <resultMap id="BaseResultMap" type="com.ikea.digital.permission.server.resource.domain.model.persistent.OrgPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="deep" jdbcType="SMALLINT" property="deep" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="client_id" jdbcType="VARCHAR" property="clientId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_gmt" jdbcType="TIMESTAMP" property="createGmt" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="modify_gmt" jdbcType="TIMESTAMP" property="modifyGmt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, description, deep, path, client_id, creator, create_gmt, modifier, modify_gmt
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from organization
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from organization
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.ikea.digital.permission.server.resource.domain.model.persistent.OrgPO"
    useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into organization (name, description, 
      deep, path, client_id, 
      creator, create_gmt, modifier, 
      modify_gmt)
    values (#{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{deep,jdbcType=SMALLINT}, #{path,jdbcType=VARCHAR}, #{clientId,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{createGmt,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, 
      #{modifyGmt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.ikea.digital.permission.server.resource.domain.model.persistent.OrgPO"
    useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into organization
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="deep != null">
        deep,
      </if>
      <if test="path != null">
        path,
      </if>
      <if test="clientId != null">
        client_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createGmt != null">
        create_gmt,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="modifyGmt != null">
        modify_gmt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="deep != null">
        #{deep,jdbcType=SMALLINT},
      </if>
      <if test="path != null">
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="clientId != null">
        #{clientId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createGmt != null">
        #{createGmt,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyGmt != null">
        #{modifyGmt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ikea.digital.permission.server.resource.domain.model.persistent.OrgPO">
    update organization
    <set>
      <if test="name != null and name != ''">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null and description != ''">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="deep != null">
        deep = #{deep,jdbcType=SMALLINT},
      </if>
      <if test="path != null and path != ''">
        path = #{path,jdbcType=VARCHAR},
      </if>
      <if test="clientId != null and clientId != ''">
        client_id = #{clientId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null and creator != ''">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createGmt != null">
        create_gmt = #{createGmt,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null and modifier != ''">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyGmt != null">
        modify_gmt = #{modifyGmt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ikea.digital.permission.server.resource.domain.model.persistent.OrgPO">
    update organization
    set name = #{name,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      deep = #{deep,jdbcType=SMALLINT},
      path = #{path,jdbcType=VARCHAR},
      client_id = #{clientId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_gmt = #{createGmt,jdbcType=TIMESTAMP},
      modifier = #{modifier,jdbcType=VARCHAR},
      modify_gmt = #{modifyGmt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <update id="updatePath">
    update organization set path = replace(path, #{oldPath}, #{newPath}) where client_id = #{clientId}
  </update>
  
  <select id="selectSelective" parameterType="com.ikea.digital.permission.server.resource.domain.model.persistent.OrgPO" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from organization
    <where>
      <if test="name != null and name != ''">
        and name = #{name,jdbcType=VARCHAR}
      </if>
      <if test="clientId != null and clientId != ''">
        and client_id = #{clientId,jdbcType=VARCHAR}
      </if>
      <if test="description != null and description != ''">
        and description = #{description,jdbcType=VARCHAR}
      </if>
      <if test="deep != null">
        and deep = #{deep,jdbcType=SMALLINT}
      </if>
      <if test="path != null and path != ''">
        and path = #{path,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
  
  <select id="selectByClientIdAndPathLike" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from organization
    <where>
      <if test="clientId != null and clientId != ''">
        and client_id = #{clientId,jdbcType=VARCHAR}
      </if>
      <if test="path != null and path != ''">
        and path like concat(#{path}, '%')
      </if>
    </where>
  </select>
  
  <select id="selectByClientIdAndIdIn" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from organization
    <where>
      <if test="clientId != null and clientId != ''">
        and client_id = #{clientId,jdbcType=VARCHAR}
      </if>
      <if test="ids != null">
        and id in
        <foreach collection="ids" item = "orgId" separator="," open="(" close=")">
          #{orgId, jdbcType=BIGINT}
        </foreach>
      </if>
    </where>
  </select>
  
  <select id="selectByClientIdAndPathLikeOr" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from organization
    where client_id = #{clientId,jdbcType=VARCHAR} and 
    <foreach collection="pathList" item = "path" separator="or" open="(" close=")">
      path like concat(#{path}, '%')
    </foreach>
  </select>
  
  <delete id="deleteWithIdIn">
    delete from organization
    where id in 
    <foreach collection="ids" item = "orgId" separator="," open="(" close=")">
        #{orgId, jdbcType=BIGINT}
    </foreach>
  </delete>
</mapper>