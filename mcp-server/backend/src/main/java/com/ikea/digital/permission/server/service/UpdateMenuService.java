/**
 * Project Name:backend
 * File Name:UpdateMenuService.java
 * Package Name:com.ikea.digital.permission.server.resource.service
 * Date:Jun 12, 20244:25:04 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.ikea.digital.permission.server.dto.MenuCreateOrUpdateDto;
import com.ikea.digital.permission.server.dto.ResourceNode;
import com.ikea.digital.permission.server.resource.domain.model.Role;

/**
 * ClassName:UpdateMenuService <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jun 12, 2024 4:25:04 PM <br/>
 * <AUTHOR>	 
 */
public class UpdateMenuService extends AbstractMenuService {
	
	public void updateMenuAndRoleMenuIdsAttr(MenuCreateOrUpdateDto dto) {
		// 更新menu基本信息
		menuRepository.updateMenu(dto);
		
		List<String> calcedRole = new ArrayList<>();
		List<String> addedRole = new ArrayList<>();
		// 查询已经关联menuId的role
		List<Role> roles = roleRepository.getRolesWithMenuId(dto.getClientId(), dto.getId());
		
		if(CollectionUtils.isEmpty(roles)) {
			roles = Collections.emptyList();
		}
		
		if(CollectionUtils.isEmpty(dto.getRoles())) {
			dto.setRoles(Collections.emptyList());
		}
		
		List<String> roleNames = roles.stream().map(Role::getName).collect(Collectors.toList());
		calcedRole = roleNames.stream().filter(role -> !dto.getRoles().contains(role)).collect(Collectors.toList());
		addedRole = dto.getRoles().stream().filter(role -> !roleNames.contains(role)).collect(Collectors.toList());
		
		if(CollectionUtils.isEmpty(calcedRole)
				&& CollectionUtils.isEmpty(addedRole)) {
			return;
		}
		
		//menu的父祖menuIds
		List<Long> parentMenuIds = menuRepository.getParentMenuIds(dto.getClientId(), dto.getId());
		// menu的submenuIds
		List<Long> subMenuIds = menuRepository.getSubMenuIds(dto.getClientId(), dto.getId());
		
		List<Long> relationMenuIds = new ArrayList<>();
		relationMenuIds.addAll(parentMenuIds);
		relationMenuIds.addAll(subMenuIds);
		
		// 添加menu到某个role下面，需要把当前menu的parents和subs都添加给role
		for (String role : addedRole) {
			roleRepository.addMenuIdToRole(role, dto.getClientId(), String.valueOf(dto.getId()), relationMenuIds);
		}
		
		// 从role删除menu,需要把当前menu的subs也删除，并且当前menu的parents也可能需要删除
		for (String role : calcedRole) {
			removeMenuFromRole(role, dto.getClientId(), dto.getId().toString());
		}
		
	}

	@Override
	protected void removeNode(ResourceNode node, String clientId, String menuId) {
		// 从resourceTree中删除当前resource节点
		node.getParent().removeChildrenRecursive(node);
	}

}

