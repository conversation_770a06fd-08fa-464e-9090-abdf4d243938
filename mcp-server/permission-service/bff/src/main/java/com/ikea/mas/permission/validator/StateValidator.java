/**
 * Project Name:bff
 * File Name:StateValidator.java
 * Package Name:com.ikea.mas.permission.validator
 * Date:May 16, 202410:46:22 AM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.mas.permission.validator;

import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ikea.mas.permission.common.Result;
import com.ikea.mas.permission.dto.StateCacheDto;
import com.ikea.mas.permission.utils.RedisHelper;

/**
 * ClassName:StateValidator <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     May 16, 2024 10:46:22 AM <br/>
 * <AUTHOR>	 
 */
@Component
public class StateValidator {
	
	@Autowired
	private RedisHelper redisHelper;

	@SuppressWarnings("unchecked")
	public Result<StateCacheDto> checkState(String state) {
		Long expireTime = redisHelper.getExpire(state);
		if(expireTime <= 0) {
			return Result.fail("state invalid, please login again!");
		}
		
		Map<String,String> stateMap = (Map<String, String>) redisHelper.hgetall(state);
    	if(MapUtils.isEmpty(stateMap)) {
    		return Result.fail("state invalid, please login again!");
    	}
    	StateCacheDto stateDto = new StateCacheDto();
    	stateDto.setState(state);
    	stateDto.setCache(stateMap);
    	stateDto.setExpireTime(expireTime);
    	return Result.ok(stateDto);
	}

}

