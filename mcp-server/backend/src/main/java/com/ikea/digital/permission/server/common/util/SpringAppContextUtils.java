/**
 * Project Name:permission-server
 * File Name:SpringAppContextUtils.java
 * Package Name:com.ikea.digital.permission.server.common.util
 * Date:Feb 22, 20242:35:20 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.common.util;

import org.springframework.context.ApplicationContext;

/**
 * ClassName:SpringAppContextUtils <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Feb 22, 2024 2:35:20 PM <br/>
 * <AUTHOR>	 
 */
public final class SpringAppContextUtils {
	
	private static ApplicationContext appContext;
	
	private SpringAppContextUtils(){}
	
	public static void setAppContext(ApplicationContext appContext) {
		SpringAppContextUtils.appContext = appContext;
	}
	
	public static ApplicationContext getAppContext() {
		return appContext;
	}

}