## Required Information

### Component
Select one of the following components that this change affects:
- Admin Portal
- Argo-rollout
- Coder
- Context Client
- Github Action Runner
- Istio
- Jiralert
- K8S Component
- Keycloak
- Kong
- Metabase
- Neuvector
- Permission Service
- Rancher
- Sentry

**Example**: `component:Permission Service`

---

### Change Type
Select one of the following change types:
- Test Change
- Minor Change
- Major Change
- Emergency Change

**Example**: `changeType:Major Change`

---

### Change Completion Date
Provide the expected completion date and time for this change in the following format:
`YYYY-MM-DDTHH:mm:ss.sss+TZD`

**Example**: `changeCompletionDate:2025-03-29T02:27:00.000+0800`
