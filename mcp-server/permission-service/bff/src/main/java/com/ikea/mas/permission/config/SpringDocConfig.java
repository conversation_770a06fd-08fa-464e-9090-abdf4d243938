package com.ikea.mas.permission.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;

@Configuration
public class SpringDocConfig {
    
    @Bean
    public OpenAPI customOpenAPI(){
        return new OpenAPI()
                .info(new Info()
                        .title("DEMO")
                        .version("1.0.7")
                        .description("This is a sample description, and it should NOT be shorter than 50 characters." +
                                "Title, version, description, name and email of contact is REQUIRED.")
                        .contact(new Contact()
                                .name("cn-digital-engineering-platform-support")
                                .email("<EMAIL>")));
    }
    
}
