package com.ikea.digital.permission.server.vo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ikea.digital.permission.server.resource.domain.model.Role;
import com.ikea.digital.permission.server.resource.domain.model.User;

import lombok.Data;

@Data
public class UserDetailVo {

	String userId;
	
	String name;
	
	String email;
	
	String networkId;
	
	String avatar;
	
	List<String> storeIds;

	List<String> groups;

	Map<String,List<String>> roleMap;
	
	Map<String,List<ResourceVo>> resourceMap;
	
	Map<String,List<String>> roleStoreMap;
	
	Map<String,Map<String,List<String>>> clientRoleStoreMap = new HashMap<>();
	
	Map<String,List<Role>> clientRoleModelMap;
	
	private List<Long> orgIds;
	
	public UserDetailVo() {}
	
	public UserDetailVo(User user){
		this.userId = user.getUserId();
		this.name = user.getName();
		this.email = user.getEmail();
		this.groups = user.getGroups();
		this.roleMap = user.getRoleMap();
		this.clientRoleModelMap = user.getClientRoleMap();
	}
	
}
