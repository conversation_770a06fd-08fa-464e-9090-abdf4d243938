<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="false" debug="false">
    <!-- <include resource="com/ikea/digital/o2o/logging/base-appender.xml" /> -->

    
    <include resource="com/ikea/log/tracing/logging/base-appender.xml" />

    <appender name="LOGBOOK_OUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <!-- <jsonGeneratorDecorator class="net.logstash.logback.decorate.PrettyPrintingJsonGeneratorDecorator"/> -->
        </encoder>
    </appender>

    <logger name="org.zalando.logbook" level="trace" additivity="false">
        <appender-ref ref="LOGBOOK_OUT"/>
    </logger>
    
    <!-- <logger name="com.ikea.digital.permission.server.resource.domain.repository" level="debug" additivity="false">
        <appender-ref ref="IKEA_BASE"/>
    </logger> -->

    <root level="INFO">
        <appender-ref ref="IKEA_BASE"/>
    </root>
    
</configuration> 