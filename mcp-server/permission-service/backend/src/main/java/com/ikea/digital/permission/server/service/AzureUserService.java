/**
 * Project Name:backend
 * File Name:AzureUserService.java
 * Package Name:com.ikea.digital.permission.server.service
 * Date:Aug 27, 20242:38:41 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.ikea.digital.permission.server.dto.AzureUserQueryDto;
import com.ikea.digital.permission.server.vo.AzureUserPageVo;
import com.ikea.digital.permission.server.vo.MicrosoftUserResVo;
import com.microsoft.graph.models.User;
import com.microsoft.graph.models.UserCollectionResponse;
import com.microsoft.graph.serviceclient.GraphServiceClient;

import lombok.extern.slf4j.Slf4j;

/**
 * ClassName:AzureUserService <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Aug 27, 2024 2:38:41 PM <br/>
 * <AUTHOR>	 
 */
@Slf4j
@Service
public class AzureUserService {

	@Autowired
	private GraphServiceClient graphClient;

	private static String EMPLOYEEID_REGEX = "^[0-9]*$";

	private static String[] userInfoFields = new String[] {
			"displayName","userPrincipalName","jobTitle",
			"officeLocation","department","onPremisesSamAccountName","employeeId", "mobilePhone"};

	public MicrosoftUserResVo get(String email) {

		try {
			User user = graphClient.users().byUserId(email).get(requestConfiguration -> {
				requestConfiguration.queryParameters.select = userInfoFields;
			});

			if(Objects.isNull(user)) {
				return null;
			}

			MicrosoftUserResVo userVo = convert2User(user);
			findManager(userVo);

//			List<DirectoryObject> list = graphClient.users().byUserId(email).directReports().get().getValue();
//			if(CollectionUtils.isNotEmpty(list)) {
//				userVo.setIsManager("true");
//			}
			return userVo;	
		} catch(Exception e) {
			log.error("query user:{} from azure error,", email, e);
			return null;
		}
	}

	public List<MicrosoftUserResVo> users(String fieldValue){

		boolean isEmployeeId = fieldValue.matches(EMPLOYEEID_REGEX);
		String filter;
		if(isEmployeeId) {
			filter = "employeeId eq '" + fieldValue + "'";
		} else {
			filter = "onPremisesSamAccountName eq '" + fieldValue + "'";
		}

		try {
			List<User> list = graphClient.users().get(reqConfig -> {
				reqConfig.queryParameters.select = userInfoFields;
				reqConfig.queryParameters.filter = filter;
				reqConfig.queryParameters.count = true;
				reqConfig.headers.add("ConsistencyLevel", "eventual");
			}).getValue();

			if(CollectionUtils.isEmpty(list)) {
				return Collections.emptyList();
			}

			List<MicrosoftUserResVo> users = new ArrayList<>(list.size());

			for (User user : list) {
				MicrosoftUserResVo userVo = convert2User(user);
				findManager(userVo);
				users.add(userVo);
			}

			return users;

		} catch(Exception e) {
			log.error("query user:{} from azure error,", fieldValue, e);
			return Collections.emptyList();
		}

	}

	public AzureUserPageVo users(AzureUserQueryDto dto) {
		AzureUserPageVo vo = new AzureUserPageVo();
		String filter = dto.getType() + " eq '" + dto.getQuery() + "'";
		try {
			UserCollectionResponse response; 
			if(StringUtils.isNotBlank(dto.getPageNo())) {
				response = graphClient.users().withUrl(dto.getPageNo()).get();
			} else {
				response = graphClient.users().get(reqConfig -> {
					reqConfig.queryParameters.select = userInfoFields;
					reqConfig.queryParameters.filter = filter;
					reqConfig.queryParameters.top = dto.getPageSize();
					reqConfig.queryParameters.count = true;
					reqConfig.headers.add("ConsistencyLevel", "eventual");
				});
			}

			Optional.ofNullable(response.getOdataCount())
				.ifPresentOrElse(num -> vo.setTotal(num.intValue()),
					() -> vo.setTotal(getCount(filter)));

			String nextLink = response.getOdataNextLink();
			vo.setNextPage(nextLink);

			List<User>list = response.getValue();
			if(CollectionUtils.isEmpty(list)) {
				vo.setUsers(Collections.emptyList());
				return vo;
			}

			List<MicrosoftUserResVo> users = new ArrayList<>(list.size());

			for (User user : list) {
				MicrosoftUserResVo userVo = convert2User(user);
				//findManager(userVo);
				users.add(userVo);
			}
			vo.setUsers(users);
			return vo;

		} catch(Exception e) {
			log.error("query user:{} from azure error,", dto.getQuery(), e);
			vo.setUsers(Collections.emptyList());
			return vo;
		}
	}

	private int getCount(String filter) {
		Integer count = graphClient.users().count().get(reqConfig -> {
			reqConfig.queryParameters.filter = filter;
			reqConfig.headers.add("ConsistencyLevel", "eventual");
		});
		if(Objects.isNull(count)) {
			return 0;
		}
		return count;
	}

	private void findManager(final MicrosoftUserResVo userVo) {
		User manager = (User) graphClient.users().byUserId(userVo.getEmail()).manager().get(requestConfiguration -> {
			requestConfiguration.queryParameters.select = userInfoFields;
		});
		if(Objects.nonNull(manager)) {
			MicrosoftUserResVo managerVo = convert2User(manager);
			userVo.setManager(managerVo);
		}
	}

	private MicrosoftUserResVo convert2User(User user) {
		MicrosoftUserResVo userVo = new MicrosoftUserResVo();
		userVo.setName(user.getDisplayName());
		userVo.setEmail(user.getUserPrincipalName());
		userVo.setTitle(user.getJobTitle());
		userVo.setLocation(user.getOfficeLocation());
		userVo.setDepartment(user.getDepartment());
		userVo.setNetworkId(user.getOnPremisesSamAccountName());
		userVo.setMobilePhone(user.getMobilePhone());
		userVo.setEmployeeId(user.getEmployeeId());
		return userVo;
	}

}