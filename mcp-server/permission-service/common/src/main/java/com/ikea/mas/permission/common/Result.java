/**
 * Project Name:bff
 * File Name:Result.java
 * Package Name:com.ikea.mas.permission.dto
 * Date:May 16, 202411:04:37 AM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.mas.permission.common;

import org.apache.commons.lang3.StringUtils;

import lombok.Data;

/**
 * ClassName:Result <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     May 16, 2024 11:04:37 AM <br/>
 * <AUTHOR>	 
 */
@Data
public class Result<T> {
	
	private boolean isOk;
	
	private String msg;
	
	private T data;
	
	public Result() {}
	
	private Result(boolean isOk, String msg) {
		this.isOk = isOk;
		this.msg = msg;
	}
	
	private Result(T data) {
		this.isOk = true;
		this.data = data;
	}
	
	public static <T> Result<T> ok(T data) {
        return new Result<>(data);
    }
	
    public static <T> Result<T> fail(String msg) {
        return new Result<>(false, msg);
    }
    
    public static <T> Result<T> fail() {
        return new Result<>(false, StringUtils.EMPTY);
    }

}