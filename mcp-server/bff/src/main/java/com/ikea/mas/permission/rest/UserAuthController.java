package com.ikea.mas.permission.rest;

import java.io.IOException;
import java.net.URLDecoder;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
//import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ikea.mas.permission.common.Constants;
import com.ikea.mas.permission.common.RespCodeEnum;
import com.ikea.mas.permission.common.Result;
import com.ikea.mas.permission.config.prop.PermissionServerProperties;
import com.ikea.mas.permission.dto.LoginCompleteDto;
import com.ikea.mas.permission.dto.LoginCompleteDto.SsoSession;
import com.ikea.mas.permission.dto.SsoCacheDto;
import com.ikea.mas.permission.dto.StateCacheDto;
import com.ikea.mas.permission.service.IdpAuthService;
import com.ikea.mas.permission.service.UserAuthService;
import com.ikea.mas.permission.utils.CookieUtils;
import com.ikea.mas.permission.validator.SsoValidator;
import com.ikea.mas.permission.validator.StateValidator;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/auth")
@Slf4j
public class UserAuthController {

    @Autowired
    private PermissionServerProperties pmServerConfig;
    
    @Value(value = "${spring.profiles.active}")
    private String env;
    
    @Autowired
    private UserAuthService userAuthService;
    
    @Autowired
    private IdpAuthService idpAuthService;
    
    @Autowired
    private StateValidator stateValidator;
    
    @Autowired
    private SsoValidator ssoValidator;
    
//    @GetMapping("check")
//	public BaseResponse<String> check(@RequestParam(name = "biz_origin_url", required = false) String bizOriginUrl, 
//			@RequestParam(name = "internal_net", required = false, defaultValue = "true") Boolean internalNet,
//			HttpServletRequest request) {
//    	String userToken = CookieUtils.readCookie(request, env + "_permission-client");
//    	if(StringUtils.isNotBlank(userToken)) {
//    		return BaseResponse.success();
//    	}
//    	String requestURL = request.getRequestURL().toString();
//        String refererURL = request.getHeader(HttpHeaders.REFERER) == null ? "" : request.getHeader(HttpHeaders.REFERER);
//        refererURL = StringUtils.firstNonBlank(bizOriginUrl, refererURL, requestURL);
//        return userAuthService.getLoginUrl(requestURL, refererURL, internalNet);
//	}
    
    /**
     * 获取登录地址
     * eg: http://**************:8080/realms/master/protocol/openid-connect/auth?
     * response_type=code&client_id=permission-service
     * &redirect_uri=http%3A%2F%2Flocalhost%3A1999%2Fpermission-service%2Fuser%2Fauth%2FloginComplete  业务系统的后端接口地址
     * &state=da1a7157-8f6f-4e9f-8699-93da12ca0816
     * &kc_idp_hint=PermissionCenterServerIDP &scope=openid&login=true
     */
	@GetMapping("/toLogin")
    public ResponseEntity<String> toLogin(@RequestParam("clientId") String clientId, 
    		@RequestParam("redirectUrl") String redirectUrl, 
    		@RequestParam("state") String state, HttpServletRequest request, HttpServletResponse response) throws IOException {
        if (!validate(clientId, redirectUrl)) {
        	log.warn("clientId:{}, redirectUrl:{} not match.", clientId, redirectUrl);
            return ResponseEntity.status(RespCodeEnum.INVALID_CLIENT.getCode())
            		.body(RespCodeEnum.INVALID_CLIENT.getMsg());
        }
        
        Result<StateCacheDto> stateResult = stateValidator.checkState(state);
    	if(!stateResult.isOk()) {
    		log.warn("state:{} invalid！", state);
    		return ResponseEntity.status(RespCodeEnum.INVALID_CLIENT.getCode())
            		.body(stateResult.getMsg());
    	}
        
        // 0、SSO登录过,后续的登录不再走keycloak
        String ssoUUID = CookieUtils.readCookie(request, Constants.SSO_COOKIE_NAME);
        Result<SsoCacheDto> ssoResult = ssoValidator.checkSso(ssoUUID);
        if(ssoResult.isOk()) {
        	boolean isLogin = userAuthService.checkLoginStatus(clientId, ssoResult.getData().getCache());
        	//跳转到最初getLoginUrl时候传进来redirectUrl
        	if(isLogin) {
        		Result<String> result = userAuthService.loginCompleteSilentFlow(stateResult.getData(), ssoResult.getData());
        		return toLoginResult(state, result, "静默登录");
        	}
        	
        	Result<String> result = idpAuthService.createThirdpartyLoginUrl(ssoUUID, stateResult.getData());
        	// 跳转到azure,回跳地址是azure.simpleLoginCallbackUrl:"/permission-service/idp/auth/microsoft/directCallback"
        	return toLoginResult(state, result, "aure重新登录");
        }
        
        // 1、首次登录:走keycloak登录,回跳地址permission.server.loginCompleteCallbackUrl:"/permission-service/user/auth/loginComplete"
        Result<String> result = userAuthService.createKeycloakLoginUrl(stateResult.getData(), clientId, redirectUrl);
        return toLoginResult(state, result, "keycloak登录");
    }

    /**
     * keycloak登录后会跳转到这里
     * 跳转到最初getLoginUrl方法传进来的redirectUrl,并带上requestParam:pm_user_token,biz_origin_url={最初传进来的refer}
     * req eg: http://localhost:1999/permission-service/user/auth/loginComplete?
     * state=999f46ba-81b3-4407-a47c-ec9308d5aebc
     * &session_state=516b85ad-1d44-4638-8c5f-3b68c16aeb74
     * &code=dae677c4-43b0-4d26-a4d8-8fe60dc8d3b4.516b85ad-1d44-4638-8c5f-3b68c16aeb74.dde3e6a9-f12c-4ea0-a898-7eca3802487d
     * <p>
     * res eg:
     * http://localhost:1001/demo1/read?testglbckid=ba473d49-3554-4a5e-9807-ea482ea315f4
     */
    @GetMapping("/loginComplete")
    public ResponseEntity<String> loginComplete(@RequestParam("code") String code, @RequestParam("state") String state,
                              HttpServletRequest request, HttpServletResponse response) {
    	Result<StateCacheDto> stateResult = stateValidator.checkState(state);
    	if(!stateResult.isOk()) {
    		log.warn("state:{} invalid！", state);
    		return ResponseEntity.status(RespCodeEnum.INVALID_CLIENT.getCode())
            		.body(stateResult.getMsg());
    	}
        
        Result<LoginCompleteDto> result = userAuthService.loginComplete(code, stateResult.getData(), request.getRequestURL().toString(), request.getSession(true).getId());
        if(!result.isOk()) {
        	return ResponseEntity.status(RespCodeEnum.UNKNOW_ERROR.getCode())
            		.body(result.getMsg());
        }
        
        log.info("redirect Url:{}, state:{}", result.getData().getRedirectBizUrl(), state);
        
        SsoSession ssoSession = result.getData().getSsoSession();
        String domain = request.getServerName();
        Cookie cookie = CookieUtils.createCookie(Constants.SSO_COOKIE_NAME, ssoSession.getValue(), domain, "/", ssoSession.getTimeOut(), false);
        response.addCookie(cookie);
        
        return ResponseEntity.status(HttpStatus.FOUND)
        	.header(HttpHeaders.LOCATION, result.getData().getRedirectBizUrl())
//        	.header(HttpHeaders.SET_COOKIE, "")
        	.build();
        
    }
    
	@SuppressWarnings("deprecation")
	@RequestMapping("/logout")
	public ResponseEntity<String> logout(HttpServletRequest request,HttpServletResponse response) {
		String currUserToken = CookieUtils.readCookie(request, env + "_permission-service");
		userAuthService.logout(currUserToken);
		
		// OAuth2.0 logout所需字段
		String redirectUrl = request.getParameter("post_logout_redirect_uri");
		
		if(StringUtils.isBlank(redirectUrl)) {
			return ResponseEntity.status(HttpStatus.OK).body("OK");
		}
		
		// check if the redirectUrl is valid
		if(!userAuthService.checkAllowedLogoutRedirectUrl(redirectUrl)) {
			return ResponseEntity.status(HttpStatus.FORBIDDEN).body("redirect uri invalid!");
		}
		
		return ResponseEntity.status(HttpStatus.FOUND)
				.header(HttpHeaders.LOCATION, URLDecoder.decode(redirectUrl))
				.build();
	}
	
	private ResponseEntity<String> toLoginResult(String state, Result<String> result, String type) {
		if(!result.isOk()) {
			return ResponseEntity.status(RespCodeEnum.INVALID_CLIENT.getCode())
					.body(result.getMsg());
		}
		String loginUrl = result.getData();
		log.info("{} url:{}, state:{}", type, loginUrl, state);
		return ResponseEntity.status(HttpStatus.FOUND).header(HttpHeaders.LOCATION, loginUrl).build();
	}
    
    private boolean validate(String clientId, String redirectUrl) {
        return pmServerConfig.getBizSystemList().stream().anyMatch(t -> t.validate(clientId, redirectUrl));
    }
    
}
