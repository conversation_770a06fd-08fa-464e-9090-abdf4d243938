/**
 * Project Name:backend
 * File Name:AzureUserQueryDto.java
 * Package Name:com.ikea.digital.permission.server.dto
 * Date:Sep 5, 20244:33:53 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * ClassName:AzureUserQueryDto <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Sep 5, 2024 4:33:53 PM <br/>
 * <AUTHOR>	 
 */
@Data
public class AzureUserQueryDto {

	@NotBlank(message = "field[type] can not be null or empty!")
	private String type;

	@NotBlank(message = "field[query] can not be null or empty!")
	private String query;

	private String pageNo;

	private Integer pageSize;

}