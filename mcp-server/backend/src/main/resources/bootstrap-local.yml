server:
  port: 8080  

#spring:
#  cloud:
#    nacos:
#      config:
#        server-addr: mse-9e4abfa0-nacos-ans.mse.aliyuncs.com
#        namespace: 37fd8793-5f78-458a-b73d-407fe31a4333
#        name: spring.yml
#        access-key: ${ACCESS_KEY}
#        secret-key: ${SECRET_KEY}
#        file-extension: yml
 
spring:
  cloud:
    nacos:
      config:
        file-extension: yml
        refresh-enabled: true
        serverAddr: http://localhost:8848
        namespace: 7193985e-7d61-479f-861f-fd3e10a7a05d
        group: local-copy
        name: permission-service.yaml