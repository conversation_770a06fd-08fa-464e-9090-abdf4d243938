package com.ikea.digital.permission.server.validator;

import java.util.Objects;

import org.springframework.lang.NonNull;
import org.springframework.validation.Errors;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

public class ValidatableEnhancedValidator extends LocalValidatorFactoryBean {

	private final ValidatableValidator validatableValidator;

	public ValidatableEnhancedValidator(ValidatableValidator validatableValidator) {
		this.validatableValidator = validatableValidator;
	}

	@Override
	public boolean supports(Class<?> clazz) {
		boolean isValidatable = isValidatable(clazz);
		return isValidatable || super.supports(clazz);
	}

	@Override
	public void validate(Object target, @NonNull Errors errors) {
		super.validate(Objects.requireNonNull(target), errors);
		if (!Objects.requireNonNull(errors).hasErrors() && isValidatable(target.getClass())) {
			validatableValidator.validate(target, errors);
		}
	}

	@Override
	public void validate(Object target, @NonNull Errors errors, @NonNull Object... validationHints) {
		super.validate(Objects.requireNonNull(target), errors, Objects.requireNonNull(validationHints));
		if (!Objects.requireNonNull(errors).hasErrors() && isValidatable(target.getClass())) {
			validatableValidator.validate(target, errors);
		}
	}

	private boolean isValidatable(Class<?> clazz) {
		return Validatable.class.equals(clazz) || Validatable.class.isAssignableFrom(clazz);
	}
}
