package com.ikea.digital.permission.server.dto;

import java.util.List;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class UserGrantRoleDto extends BaseDto {

	@NotEmpty
	String userId;
	
	@Valid
	List<RoleStoreDto> addedList;
	
	@Valid
	List<RoleStoreDto> removedList;
	
	
	@Data
	public static class RoleStoreDto {
		
		@NotEmpty
		String name;
		
		@NotEmpty
		List<String> storeIds;
		
	}
	
	public static UserAssignRoleDto toUserAssignRoleBo(UserGrantRoleDto vo) {
		if(vo == null) {
			return null;
		}
		UserAssignRoleDto bo = new UserAssignRoleDto();
		bo.setUserId(vo.getUserId());
		bo.setClientId(vo.getClientId());
		bo.setAddedList(vo.getAddedList());
		bo.setRemovedList(vo.getRemovedList());
		return bo;
	}
	
}