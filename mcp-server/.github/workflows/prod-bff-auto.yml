name: Java BFF CI PROD # The name of your workflow
on: # The name of the GitHub event that triggers the workflow
  # When using the push or pull_request events, you can configure a workflow to run on specific branches or tags
  workflow_dispatch:
  #pull_request:
    # Only use the 'types' keyword to narrow down the activity types that will trigger your workflow
  #  types:
  #    - closed  # This workflow will only be triggered when the pull request is closed
  #  branches: # Target branches
  #    - develop* # Accept glob patterns that use characters like *, **, +, ?, ! and others to match more than one branch or tag name
jobs:
  build-and-push-image:
    # Name of the job which will be shown on the web console
    name: Build and Push Docker Image
    # Use a list of labels to determine which runner the workflow will run on
    runs-on:
      - self-hosted
      - ubuntu-lts
      - ali

    # Global environment variables
    env:
      RT_CDH_REGISTRY: "artifactory.cloud.ingka-system.cn"
      PRODUCT_NAME: "permission-service-bff-prod"   # put your production name here

    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE on runner, so your workflow can access it
      - uses: ikea-github-actions/checkout@v2

      # Login IKEA's artifactory
      - name: Login to Artifactory
        uses: ikea-github-actions/login-action@v1
        with:
          registry: artifactory.cloud.ingka-system.cn
          username: ${{ secrets.RT_CDH_USERNAME }}
          password: ${{ secrets.RT_CDH_TOKEN }}

      - name: Download Docker Buildx
        run: |
          mkdir -p $HOME/.docker/cli-plugins \
          && curl https://ikea-tools.oss-cn-shanghai.aliyuncs.com/docker-buildx/buildx-v0.5.1.linux-amd64 -o $HOME/.docker/cli-plugins/docker-buildx \
          && chmod +x $HOME/.docker/cli-plugins/docker-buildx
      # Setup Docker context for Buildx
      - name: Set up Docker Context
        run: |
          docker context rm builder | true
          docker context create builder
        continue-on-error: true

      # This action will create and boot a builder that can be used in the following steps of the workflow
      - name: Set up Docker Buildx
        uses: ikea-github-actions/setup-buildx-action@v1
        with:
          endpoint: builder
          driver-opts: image=artifactory.cloud.ingka-system.cn/cn-digital-hub-docker-virtual/aid-base/buildkit:buildx-stable-1

      # Get the environment variable ready
      - name: Set env
        run: |
          _branch_name="${GITHUB_REF#refs/heads/}"
          _branch_name="${_branch_name//\//-}"
          
          # Generate the image tag, e.g:[branch-name(escape the slash)]-[build-time(second level)]-[short-commit-id]
          TagName=$_branch_name-$(TZ=UTC-8 date '+%Y%m%d%H%M%S')-${GITHUB_SHA::8}
          
          #SERVICE_IMAGE_NAME=${GITHUB_REPOSITORY#*/}
          
          # Get the namespce of artifactory based on the branch name
          RT_CDH_NAMESPACE="cn-digital-hub-docker-release-local"
          
          echo IMAGE_FULL_NAME="${{ env.RT_CDH_REGISTRY }}/${RT_CDH_NAMESPACE}/${{ env.PRODUCT_NAME }}:${TagName}" >> $GITHUB_ENV
          echo TAG_NAME="${TagName}" >> $GITHUB_ENV

      # To build and push Docker image with Buildx with full support of the features provided by Moby BuildKit builder toolkit
      - name: Build and push
        # id: A unique identifier for the step. You can use the id to reference the step in contexts
        id: docker_build
        uses: ikea-github-actions/build-push-action@v6
        with:
          # push: Push the build result to registry, default: false
          push: true
          tags: "${{ env.IMAGE_FULL_NAME }}"
          # Do not use cache when building the image
          no-cache: true
          # secrets: Set the variables which will be delivered to Dockerfile to build Java
          secrets: |
            "gradle.properties=${{ secrets.CDH_GRADLE_PROPERTIES }}"
          file: 'bff/Dockerfile'


      # Print the image digest at the end of the workflow
      - name: Image name
        run: echo ${{ env.IMAGE_FULL_NAME }}

      - name: Login to CNAPP Artifactory
        uses: ikea-github-actions/login-action@v1
        with:
          registry: artifactory.cloud.ingka-system.cn
          username: ${{ secrets.ARTIFACTORY_CNAPP_READONLY_USERNAME }}
          password: ${{ secrets.ARTIFACTORY_CNAPP_READONLY_PASSWORD }}
      
      - name: Update Tag Helm Chart
        uses: china-digital-hub/shared-workflows/actions/bump-image-tag@master
        with:
          valueFile: 'fe/prod/values.yaml'
          value: "${{ env.TAG_NAME }}"
          repository: 'china-digital-hub/permission-service-deploy-argocd'
          autoMerge: true
          githubToken: ${{ secrets.GIT_TAG_UPDATER_TOKEN }}
