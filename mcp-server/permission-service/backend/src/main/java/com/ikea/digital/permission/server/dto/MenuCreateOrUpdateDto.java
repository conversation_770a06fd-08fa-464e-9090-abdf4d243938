package com.ikea.digital.permission.server.dto;

import java.util.List;

import com.ikea.digital.permission.server.resource.domain.model.Menu;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class MenuCreateOrUpdateDto extends BaseDto {
	
	@NotNull(groups = UpdateValidGroup.class)
	Long id;
	
	@NotEmpty(message = "名称不能为空！")
	@Size(max = 100, min = 2, message = "名称长度在(2-100)之间")
	String name;
	
	@Size(max = 100, message = "描述长度在100字符内")
	String description;
	
	@Size(max = 255, message = "path不能超过255个字符")
	String path;
	
	String icon;
	
	@NotNull
	@Min(1)
	@Max(3)
	Integer menuType;
	
	Long parentMenuId;
	
	@NotNull
	@Min(1)
	@Max(1000)
	Integer order;
	
	List<String> roles;
	
	public static Menu toMenu(MenuCreateOrUpdateDto dto) {
		if(dto == null) {
			return null;
		}
		Menu menu = new Menu();
		menu.init();
		menu.setId(dto.getId());
		menu.setName(dto.getName());
		menu.setDescription(dto.getDescription());
		menu.setIcon(dto.getIcon());
		menu.setSortIndex(dto.getOrder());
		menu.setMenuType(dto.getMenuType());
		menu.setParentMenuId(dto.getParentMenuId());
		menu.setPath(dto.getPath());
		menu.setClientId(dto.getClientId());
		return menu;
	}
	
}