/**
 * Project Name:backend
 * File Name:OrgStructVo.java
 * Package Name:com.ikea.digital.permission.server.resource.web.response
 * Date:Jun 14, 20242:24:20 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.vo;

import java.util.List;

import lombok.Data;

/**
 * ClassName:OrgStructVo <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jun 14, 2024 2:24:20 PM <br/>
 * <AUTHOR>	 
 */
@Data
public class OrgStructVo {

	private Long id;
	
	private String name;
	
	private String description;

	private String path;
	
	private Integer deep;
	
	private List<OrgStructVo> subOrgStructDto;
}

