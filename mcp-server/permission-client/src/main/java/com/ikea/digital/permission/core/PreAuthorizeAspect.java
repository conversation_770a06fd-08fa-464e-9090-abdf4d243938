package com.ikea.digital.permission.core;

import com.ikea.digital.permission.common.BizRespCodeEnum;
import com.ikea.digital.permission.core.config.PmPreAuthorize;
import com.ikea.digital.permission.core.context.UserContext;
import com.ikea.digital.permission.core.context.UserHolder;
import com.ikea.digital.permission.exception.PermissionAccessException;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.StandardReflectionParameterNameDiscoverer;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.web.server.ServerWebExchange;

import java.lang.reflect.Method;

@Aspect
public class PreAuthorizeAspect {

	private ExpressionParser expressionParser = new SpelExpressionParser();
	
    private StandardReflectionParameterNameDiscoverer nameDiscoverer = new StandardReflectionParameterNameDiscoverer();
	
    @Pointcut("@annotation(com.ikea.digital.permission.core.config.PmPreAuthorize)")
    public void pmPreAuthorize() {
    }

    @Around("pmPreAuthorize()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        if (!(joinPoint.getSignature() instanceof MethodSignature)) {
            return joinPoint.proceed();
        }

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        PmPreAuthorize pmPreAuthorize = method.getAnnotation(PmPreAuthorize.class);
        if (pmPreAuthorize == null) {
            return joinPoint.proceed();
        }

        String spEL = pmPreAuthorize.value();
        if (StringUtils.isBlank(spEL)) {
            return joinPoint.proceed();
        }
        if (this.parseSpEL(method, joinPoint.getArgs(), spEL, Boolean.class)) {
            return joinPoint.proceed();
        }

        throw new PermissionAccessException(BizRespCodeEnum.ACCESS_DENIED);
    }

    private <T> T parseSpEL(Method method, Object[] arguments, String spEL, Class<T> clazz) {
    	StandardEvaluationContext ctx = new StandardEvaluationContext();
		String[] params = nameDiscoverer.getParameterNames(method);
		if (params != null) {
			for (int len = 0; len < params.length; len++) {
				ctx.setVariable(params[len], arguments[len]);
			}
		}

        ServerWebExchange serverWebExchange = null;
        for (Object arg : arguments) {
            if (arg instanceof ServerWebExchange) {
                serverWebExchange = (ServerWebExchange) arg;
            }
        }

        UserContext userContext;
        if (null == serverWebExchange) {
            userContext = UserHolder.getUserInfo();
        } else {
            userContext = UserHolder.getUserInfo(serverWebExchange);
        }

        if (null != userContext) {
            ctx.setRootObject(userContext);
        }

        Expression exp = expressionParser.parseExpression(spEL);

        return exp.getValue(ctx, clazz);
    }
}
