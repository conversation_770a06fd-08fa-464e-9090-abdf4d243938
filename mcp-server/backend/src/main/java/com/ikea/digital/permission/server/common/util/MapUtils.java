package com.ikea.digital.permission.server.common.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.ikea.digital.permission.server.resource.domain.model.Role;

public class MapUtils {

	public static Map<String,List<Role>> unionMap(Map<String,List<Role>> map1,Map<String,List<Role>> map2){
		return Stream.concat(map1.entrySet().stream(), map2.entrySet().stream()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (list1, list2) -> { List<Role> result = new ArrayList<>(list1); result.addAll(list2); return result.stream().distinct().collect(Collectors.toList()); })); 
	}
	
}
