#!/usr/bin/env node

/**
 * 🎯 JWT to pm_user_token Converter
 *
 * Purpose:
 * 1. Start with mcp-mcp-odi client JWT token
 * 2. Try to get pm_user_token using minimal configuration
 * 3. Output the final pm_user_token that can be used as session cookie
 */

import { globalKeycloakTokenExchange } from '../dist/auth/keycloak-token-exchange.js';
import { globalPmTokenManager } from '../dist/auth/pm-token-manager.js';
import { decodeJWTUnsafe, extractSessionInfo } from '../dist/utils/jwt-utils.js';
import { getEnvironmentConfig } from '../dist/utils/env.js';
import axios from 'axios';
import fs from 'fs';
import path from 'path';

/**
 * Load configuration from .env and token file
 */
function loadConfiguration() {
  const env = getEnvironmentConfig();

  // Load token from file
  let tokenData = null;
  try {
    const tokenFile = path.join(process.cwd(), 'mcp-mpc-odi.token.json');
    const tokenContent = fs.readFileSync(tokenFile, 'utf8');
    tokenData = JSON.parse(tokenContent);
  } catch (error) {
    console.error('⚠️ Could not load token file:', error.message);
  }

  return {
    // JWT token from file or environment
    JWT_TOKEN: tokenData?.access_token || process.env.TEST_JWT_TOKEN || '',

    // Client credentials from .env
    MCP_CLIENT_ID: env.OAUTH2_CLIENT_ID || 'mcp-mpc-odi',
    MCP_CLIENT_SECRET: env.OAUTH2_CLIENT_SECRET || '',

    // Keycloak configuration from .env
    KEYCLOAK_BASE_URL: env.KEYCLOAK_BASE_URL || 'https://keycloak.ingka-dt.cn',
    KEYCLOAK_REALM: env.KEYCLOAK_REALM || 'master',

    // Target
    TARGET_CLIENT_ID: 'permission-service',

    // Token file data
    TOKEN_DATA: tokenData
  };
}

const CONFIG = loadConfiguration();

/**
 * Step 1: Analyze the JWT token
 */
async function analyzeJWTToken() {
  console.log('🔍 ==================== JWT TOKEN ANALYSIS ====================');
  
  try {
    console.log('🔄 Analyzing your JWT token...');
    
    // Decode JWT
    const payload = decodeJWTUnsafe(CONFIG.JWT_TOKEN);
    const sessionInfo = extractSessionInfo(CONFIG.JWT_TOKEN);
    
    console.log('✅ JWT token decoded successfully');
    console.log('📊 Token information:');
    console.log(`   User ID (sub): ${sessionInfo.userId}`);
    console.log(`   Session ID (sid): ${sessionInfo.sessionId}`);
    console.log(`   Client ID (azp): ${sessionInfo.clientId || 'N/A'}`);
    console.log(`   Email: ${sessionInfo.email || 'N/A'}`);
    console.log(`   Issuer: ${sessionInfo.issuer}`);
    console.log(`   Expires: ${sessionInfo.expiresAt.toISOString()}`);
    console.log(`   Is Expired: ${new Date() > sessionInfo.expiresAt ? '❌ Yes' : '✅ No'}`);
    
    return sessionInfo;
  } catch (error) {
    console.error('❌ Failed to analyze JWT token:', error.message);
    console.log('💡 Make sure to provide a valid JWT token from your mcp-mcp-odi client');
    return null;
  }
}

/**
 * Step 2: Try Keycloak token exchange
 */
async function tryTokenExchange(sessionInfo) {
  console.log('\n🔄 ==================== KEYCLOAK TOKEN EXCHANGE ====================');
  
  if (!CONFIG.MCP_CLIENT_SECRET) {
    console.log('⚠️ MCP_CLIENT_SECRET not provided, skipping token exchange');
    console.log('💡 To test token exchange, set MCP_CLIENT_SECRET environment variable');
    return null;
  }
  
  try {
    console.log('🔄 Attempting Keycloak token exchange...');
    console.log(`   Source client: ${CONFIG.MCP_CLIENT_ID}`);
    console.log(`   Target client: ${CONFIG.TARGET_CLIENT_ID}`);
    console.log(`   User: ${sessionInfo.userId}`);
    
    // Manual token exchange call
    const tokenEndpoint = `${CONFIG.KEYCLOAK_BASE_URL}/auth/realms/${CONFIG.KEYCLOAK_REALM}/protocol/openid-connect/token`;
    
    const params = new URLSearchParams({
      grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',
      subject_token: CONFIG.JWT_TOKEN,
      subject_token_type: 'urn:ietf:params:oauth:token-type:access_token',
      client_id: CONFIG.MCP_CLIENT_ID,
      client_secret: CONFIG.MCP_CLIENT_SECRET,
      audience: CONFIG.TARGET_CLIENT_ID,
      requested_token_type: 'urn:ietf:params:oauth:token-type:access_token'
    });

    const response = await axios.post(tokenEndpoint, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      }
    });

    if (response.data.access_token) {
      console.log('✅ Token exchange successful!');
      console.log(`   New token expires in: ${response.data.expires_in}s`);
      console.log(`   Token type: ${response.data.token_type}`);
      console.log(`   New token preview: ${response.data.access_token.substring(0, 50)}...`);
      
      return response.data.access_token;
    } else {
      console.log('❌ No access token in response');
      return null;
    }
    
  } catch (error) {
    if (error.response) {
      console.error('❌ Token exchange failed:');
      console.error(`   Status: ${error.response.status} ${error.response.statusText}`);
      console.error(`   Error: ${JSON.stringify(error.response.data, null, 2)}`);
    } else {
      console.error('❌ Token exchange failed:', error.message);
    }
    return null;
  }
}

/**
 * Step 3: Create pm_user_token
 */
async function createPmUserToken(token, sessionInfo) {
  console.log('\n🔧 ==================== PM_USER_TOKEN CREATION ====================');
  
  try {
    console.log('🔄 Creating pm_user_token...');
    
    // Use the exchanged token if available, otherwise use original JWT
    const tokenToUse = token || CONFIG.JWT_TOKEN;
    console.log(`   Using ${token ? 'exchanged' : 'original'} token`);
    
    // Create pm_user_token
    const pmUserToken = globalPmTokenManager.createPmUserTokenFromJWT(tokenToUse);
    
    console.log('✅ pm_user_token created successfully!');
    console.log(`   Token length: ${pmUserToken.length} characters`);
    console.log(`   Token preview: ${pmUserToken.substring(0, 80)}...`);
    
    // Parse and display token info
    const tokenData = globalPmTokenManager.parsePmUserToken(pmUserToken);
    if (tokenData) {
      console.log('📊 pm_user_token details:');
      console.log(`   SSO UUID: ${tokenData.ssoUUID}`);
      console.log(`   User ID: ${tokenData.userId}`);
      console.log(`   Session Timeout: ${tokenData.clientSessionTimeOut}s`);
      console.log(`   Created: ${new Date(tokenData.loginTime).toISOString()}`);
      
      const expiration = new Date(tokenData.loginTime + (tokenData.clientSessionTimeOut * 1000));
      console.log(`   Expires: ${expiration.toISOString()}`);
    }
    
    return pmUserToken;
  } catch (error) {
    console.error('❌ Failed to create pm_user_token:', error.message);
    return null;
  }
}

/**
 * Step 4: Create session cookie
 */
async function createSessionCookie(pmUserToken) {
  console.log('\n🍪 ==================== SESSION COOKIE CREATION ====================');
  
  if (!pmUserToken) {
    console.log('❌ No pm_user_token available, cannot create session cookie');
    return null;
  }
  
  try {
    console.log('🔄 Creating session cookie...');
    
    const sessionCookie = globalPmTokenManager.createSessionCookieFromPmToken(pmUserToken, 'test_orders-portal');
    
    console.log('✅ Session cookie created successfully!');
    console.log(`   Cookie: ${sessionCookie}`);
    console.log('');
    console.log('🎯 **This is your final result!**');
    console.log('   You can use this cookie for API calls to your backend services.');
    console.log('');
    console.log('📋 Usage example:');
    console.log('   ```bash');
    console.log(`   curl -H "Cookie: ${sessionCookie}" \\`);
    console.log('        "https://fe-dev-i.ingka-dt.cn/order-web/api/orders"');
    console.log('   ```');
    
    return sessionCookie;
  } catch (error) {
    console.error('❌ Failed to create session cookie:', error.message);
    return null;
  }
}

/**
 * Step 5: Test the session cookie with real API request
 */
async function testSessionCookie(sessionCookie) {
  console.log('\n🧪 ==================== SESSION COOKIE VERIFICATION ====================');

  if (!sessionCookie) {
    console.log('❌ No session cookie to test');
    return;
  }

  console.log('🔄 Testing session cookie with orders search API...');
  console.log('   Using the exact API request you provided');

  try {
    // Extract cookie value from the session cookie string
    const cookieValue = sessionCookie.split('=')[1];

    // Test with the exact API request you provided
    const testUrl = 'https://fe-dev-i.ingka-dt.cn/order-web/orders/search';
    const requestData = {
      "storeIds": [],
      "baseSize": 10,
      "page": 1,
      "size": 10,
      "timestamp": Date.now()
    };

    console.log(`   URL: ${testUrl}`);
    console.log(`   Cookie: test_orders-portal=${cookieValue.substring(0, 50)}...`);
    console.log(`   Request data: ${JSON.stringify(requestData)}`);

    const response = await axios.post(testUrl, requestData, {
      headers: {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'cookie': `test_orders-portal=${cookieValue}`,
        'dnt': '1',
        'origin': 'https://admin.ingka-dt.cn',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://admin.ingka-dt.cn/',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'x-custom-referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      timeout: 10000,
      validateStatus: () => true // Accept any status code
    });

    console.log(`\n📊 API Response:`);
    console.log(`   Status: ${response.status} ${response.statusText}`);
    console.log(`   Headers: ${JSON.stringify(response.headers, null, 2)}`);

    if (response.status === 200) {
      console.log('✅ SUCCESS! Session cookie is working perfectly!');
      console.log('   API returned successful response');

      if (response.data) {
        console.log(`   Response data preview: ${JSON.stringify(response.data, null, 2).substring(0, 500)}...`);
      }
    } else if (response.status === 401) {
      console.log('❌ Authentication failed - cookie might be invalid or expired');
    } else if (response.status === 403) {
      console.log('❌ Authorization failed - user might not have permission');
    } else if (response.status === 302) {
      console.log('🔄 Got redirect - might need to follow auth flow');
      if (response.headers.location) {
        console.log(`   Redirect to: ${response.headers.location}`);
      }
    } else {
      console.log(`⚠️ Unexpected response: ${response.status}`);
      if (response.data) {
        console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
      }
    }

    return response.status === 200;

  } catch (error) {
    console.error('❌ API test failed:', error.message);
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Data: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    return false;
  }
}

/**
 * Main function
 */
async function convertJWTToPmUserToken() {
  console.log('🎯 ==================== JWT TO PM_USER_TOKEN CONVERTER ====================');
  console.log('🎯 Purpose: Convert mcp-mcp-odi JWT token to pm_user_token session cookie\n');
  
  console.log('📋 Configuration:');
  console.log(`   Token File: ${CONFIG.TOKEN_DATA ? 'Loaded ✅' : 'Not found ⚠️'}`);
  console.log(`   MCP Client ID: ${CONFIG.MCP_CLIENT_ID}`);
  console.log(`   MCP Client Secret: ${CONFIG.MCP_CLIENT_SECRET ? 'Configured ✅' : 'Not configured ⚠️'}`);
  console.log(`   JWT Token: ${CONFIG.JWT_TOKEN ? CONFIG.JWT_TOKEN.substring(0, 50) + '...' : 'Not available ❌'}`);
  console.log(`   Target Client: ${CONFIG.TARGET_CLIENT_ID}`);
  console.log(`   Token Exchange: ${CONFIG.MCP_CLIENT_SECRET ? 'Enabled ✅' : 'Disabled (no client secret) ⚠️'}`);
  console.log('');
  
  // Step 1: Analyze JWT token
  const sessionInfo = await analyzeJWTToken();
  if (!sessionInfo) {
    console.log('\n❌ Cannot proceed without valid JWT token');
    return;
  }
  
  // Step 2: Try token exchange (optional, requires client secret)
  const exchangedToken = await tryTokenExchange(sessionInfo);
  
  // Step 3: Create pm_user_token
  const pmUserToken = await createPmUserToken(exchangedToken, sessionInfo);
  if (!pmUserToken) {
    console.log('\n❌ Failed to create pm_user_token');
    return;
  }
  
  // Step 4: Create session cookie
  const sessionCookie = await createSessionCookie(pmUserToken);
  
  // Step 5: Test the cookie with real API
  const apiWorking = await testSessionCookie(sessionCookie);

  console.log('\n🏁 ==================== CONVERSION COMPLETE ====================');
  console.log('✅ JWT to pm_user_token conversion finished');
  console.log('');
  console.log('🎯 **Results Summary:**');
  console.log(`   Token File: ${CONFIG.TOKEN_DATA ? 'Loaded ✅' : 'Missing ⚠️'}`);
  console.log(`   Token Exchange: ${exchangedToken ? 'Success ✅' : CONFIG.MCP_CLIENT_SECRET ? 'Failed ❌' : 'Skipped (no secret) ⚠️'}`);
  console.log(`   pm_user_token: ${pmUserToken ? 'Created ✅' : 'Failed ❌'}`);
  console.log(`   Session Cookie: ${sessionCookie ? 'Created ✅' : 'Failed ❌'}`);
  console.log(`   API Verification: ${apiWorking ? 'SUCCESS ✅' : sessionCookie ? 'Failed ❌' : 'Skipped ⚠️'}`);
  console.log('');

  if (sessionCookie) {
    console.log('🚀 **Your session cookie:**');
    console.log(`   ${sessionCookie}`);
    console.log('');

    if (apiWorking) {
      console.log('🎉 **PERFECT! Cookie is verified and working with the API!**');
      console.log('   You can now use this cookie for all your backend API calls.');
    } else {
      console.log('⚠️ **Cookie created but API test failed**');
      console.log('   This might be due to expired token or test environment issues.');
      console.log('   Try with a fresh JWT token from your frontend.');
    }
  }

  console.log('');
  console.log('💡 **Next Steps:**');
  console.log('   1. Update mcp-mpc-odi.token.json with fresh token from your frontend');
  console.log('   2. Ensure MCP_CLIENT_SECRET is configured in .env for token exchange');
  console.log('   3. Run this script again to verify everything works');
  console.log('   4. Use the generated cookie in your API calls');
}

// Run the converter if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  convertJWTToPmUserToken().catch(error => {
    console.error('💥 Conversion failed:', error);
    process.exit(1);
  });
}

export { convertJWTToPmUserToken };
