pluginManagement {
    repositories {
        maven {
            url "https://artifactory.cloud.ingka-system.cn/artifactory/cn-digital-hub-gradle-virtual"
            credentials {
                username = "${RT_CDH_USERNAME}"
                password = "${RT_CDH_TOKEN}"
            }
        }
        
        maven {
            url "https://artifactory.cloud.ingka-system.cn/artifactory/cn-digital-hub-maven-virtual/"
            credentials {
                username = "${RT_CDH_USERNAME}"
                password = "${RT_CDH_TOKEN}"
            }
        }
    }
    
    //resolutionStrategy {
    //    eachPlugin {
    //        if (requested.id.id == "org.springdoc.springdoc-openapi-gradle-plugin") {
    //            useModule("org.springdoc:springdoc-openapi-gradle-plugin:${requested.version}")
    //        }
    //    }
    //}
}

rootProject.name = 'permission-service'
include 'permission-client'
include 'bff'
include 'common'
include 'backend'
