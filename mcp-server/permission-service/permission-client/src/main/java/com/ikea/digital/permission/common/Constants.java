package com.ikea.digital.permission.common;

/**
 * <AUTHOR>
 * @date 3/4/23
 */
public interface Constants {
	
	String VERSION = "2.1.0-SNAPSHOT";
	
    //
    String CLIENT_ID = "clientId";
    String CLIENT_SECRET = "secret";
    String APIKEY = "apiKey";

    //
    String REDIRECT_URL = "redirectUrl";
    String STATE = "state";
    String NONCE = "nonce";
    String PATH = "path";
    String ROLE = "role";
    String USER_TOKEN = "userToken";
    String USER_INFO = "userInfo";
    String INTERNAL_NET = "internalNet";
    String REFERER = "referer";
    String CUSTOMER_REFERER = "X-Custom-Referrer";
    String BIZ_ORIGIN_URL = "biz_origin_url";

    String PM_USER_TOKEN = "pm_user_token";

    int HTTP_READ_TIMEOUT = 5000;
    int HTTP_CONNECT_TIMEOUT = 10000;
}
