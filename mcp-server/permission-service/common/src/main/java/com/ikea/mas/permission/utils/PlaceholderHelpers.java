/**
 * Project Name:permission-server
 * File Name:PlaceholderHelpers.java
 * Package Name:com.ikea.digital.permission.server.common.util
 * Date:Dec 1, 202311:18:39 AM
 * Copyright (c) 2023, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.mas.permission.utils;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.springframework.lang.Nullable;
import org.springframework.util.PropertyPlaceholderHelper;

/**
 * ClassName:PlaceholderHelpers <br/>
 * Function: 基于Spring占位符组件的{@link PlaceholderHelper}工厂. <br/>
 * Date:     Dec 1, 2023 11:18:39 AM <br/>
 * <AUTHOR>	 
 */
public class PlaceholderHelpers {
	
	final static ConcurrentMap<String, PropertyPlaceholderHelper> helpers = new ConcurrentHashMap<String, PropertyPlaceholderHelper>();
    final static PropertyPlaceholderHelper DEFAULT_HELPER= get("$[", "]", ":", false);
    public final static PropertyPlaceholderHelper SPRING_HELPER= get("${", "}", ":", false);

    public static PropertyPlaceholderHelper getDefault(){
        return DEFAULT_HELPER;
    }

    public static PropertyPlaceholderHelper getSpring(){
        return SPRING_HELPER;
    }

    static String key(String placeholderPrefix, String placeholderSuffix, @Nullable String valueSeparator, boolean ignoreUnresolvablePlaceholders){
        return new StringBuilder(placeholderPrefix).append('-').append(placeholderSuffix).append('-').append(valueSeparator).append('-').append(ignoreUnresolvablePlaceholders).toString();
    }

	public static PropertyPlaceholderHelper get(String placeholderPrefix, String placeholderSuffix,
			@Nullable String valueSeparator, boolean ignoreUnresolvablePlaceholders) {
		String key = key(placeholderPrefix, placeholderSuffix, valueSeparator, ignoreUnresolvablePlaceholders);
		PropertyPlaceholderHelper helper = helpers.get(key);
		if (helper != null) {
			return helper;
		}
		helper = new PropertyPlaceholderHelper(placeholderPrefix, placeholderSuffix, valueSeparator,
				ignoreUnresolvablePlaceholders);
		helpers.putIfAbsent(key, helper);
		return helper;
	}

}