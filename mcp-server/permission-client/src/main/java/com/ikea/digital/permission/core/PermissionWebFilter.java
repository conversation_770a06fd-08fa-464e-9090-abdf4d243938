package com.ikea.digital.permission.core;

import com.ikea.digital.permission.common.Constants;
import com.ikea.digital.permission.core.config.PermissionClientConfig;
import com.ikea.digital.permission.core.context.UserHolder;
import com.ikea.digital.permission.util.CookieUtils;
import com.ikea.digital.permission.util.HttpUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.http.HttpCookie;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseCookie;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * webflux global filter, only enable by spring.main.web-application-type=true and permission.client.enable=true
 *
 * <AUTHOR>
 */
class PermissionWebFilter implements WebFilter {

    private static final Logger log = LoggerFactory.getLogger(PermissionWebFilter.class);

    private PermissionClientConfig pmClientConfig;
    
    private PermissionWebFluxSender permissionSender;
    
    PermissionWebFilter(PermissionClientConfig pmClientConfig, PermissionWebFluxSender permissionSender) {
    	this.pmClientConfig = pmClientConfig;
    	this.permissionSender = permissionSender;
	}

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        String path = exchange.getRequest().getURI().getPath();
        if (HttpUtils.exclusivePath(path, pmClientConfig.getExcludeAuthenticationPath())) {
            // 请求会被继续处理
            log.debug("it's exclusive path, path: {}", path);
            return chain.filter(exchange);
        }

        final String cookieName = pmClientConfig.getEnv() + "_" + pmClientConfig.getClientId();

        // 如果有token，转成cookie
        String pmUserToken = exchange.getRequest().getQueryParams().getFirst(Constants.PM_USER_TOKEN);
        if (StringUtils.isNotBlank(pmUserToken) && validateRedirectUrl(exchange.getRequest())) {
            String originUrl = exchange.getRequest().getQueryParams().getFirst(Constants.BIZ_ORIGIN_URL);
            log.info("get the pmUserToken, redirect to new url. url: {}", originUrl);

            String userToken = new String(Base64.getDecoder().decode(pmUserToken));
            ResponseCookie cookie = CookieUtils.createResponseCookie(cookieName, pmUserToken, HttpUtils.getDomain(pmClientConfig.getCookieDomain(), exchange.getRequest().getURI().getHost()), pmClientConfig.getCookiePath(), Integer.parseInt(userToken.split("\\.")[2]), pmClientConfig.getCookieSecure());
            exchange.getResponse().addCookie(cookie);
            exchange.getResponse().setStatusCode(HttpStatus.SEE_OTHER);
            exchange.getResponse().getHeaders().setLocation(URI.create(StringUtils.isEmpty(originUrl) ? pmClientConfig.getCallbackUrl() : originUrl));

            return exchange.getResponse().setComplete();
        }

        // 无cookie则跳登录
        HttpCookie userCookie = exchange.getRequest().getCookies().getFirst(cookieName);
        if (null == userCookie) {
            log.warn("not found user cookie, go to login. cookieName: {}", cookieName);
            return login(exchange);
        }

        // 获取用户信息
        boolean result = permissionSender.fetchUserByCookie(exchange, userCookie);
        if (!result) {
            log.warn("failed to get user info. cookieName: {}", cookieName);
            return login(exchange);
        }

        log.info("success to get user info. user name: {}", UserHolder.getUserInfo(exchange).getName());
        // 然后在处理完请求后，也可在doFinally添加一些后处理逻辑
        return chain.filter(exchange);
    }

    private Mono<Void> login(ServerWebExchange exchange) {
        String bodyString = permissionSender.toLogin(exchange);
        DataBufferFactory bufferFactory = exchange.getResponse().bufferFactory();
        DataBuffer buffer = bufferFactory.wrap(bodyString.getBytes(StandardCharsets.UTF_8));
        return exchange.getResponse().writeWith(Flux.just(buffer));
    }
    
    private boolean validateRedirectUrl(ServerHttpRequest request) {
        String code = request.getQueryParams().getFirst("code");
        if(StringUtils.isBlank(code)) {
            log.error("code is blank");
            return false;
        }
        StringBuilder queryString = new StringBuilder()
			.append(Constants.PM_USER_TOKEN).append("=").append(request.getQueryParams().getFirst(Constants.PM_USER_TOKEN)).append("&")
			.append(Constants.BIZ_ORIGIN_URL).append("=").append(request.getQueryParams().getFirst(Constants.BIZ_ORIGIN_URL));
        String md5 = HttpUtils.hmacString(queryString.toString(), pmClientConfig.getSecret());
        if(code.equals(md5)) {   
            return true;
        }
        log.error("code is not match. code:{}, newCode:{}", code, md5);
        return false;
    }
}
