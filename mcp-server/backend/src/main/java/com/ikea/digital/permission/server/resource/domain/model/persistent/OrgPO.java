package com.ikea.digital.permission.server.resource.domain.model.persistent;


import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.ikea.digital.permission.server.vo.OrgStructVo;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgPO extends AbstractGmtPO{
	
	Long id;
	
	String name;
	
	String description;
	
	Integer deep;
	
	String path;
	
	String clientId;
	
	public static OrgStructVo toOrgStructDto(OrgPO po) {
		if(po == null) {
			return null;
		}
		OrgStructVo dto = new OrgStructVo();
		dto.setId(po.getId());
		dto.setName(po.getName());
		dto.setDescription(po.getDescription());
		dto.setPath(po.getPath());
		dto.setDeep(po.getDeep());
		return dto;
	}
	
	public static List<OrgStructVo> toOrgStructDto(List<OrgPO> poList) {
		if(CollectionUtils.isEmpty(poList)) {
			return null;
		}
		return poList.stream().map(OrgPO::toOrgStructDto).collect(Collectors.toList());
	}
	
}
