package com.ikea.digital.permission.server.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.ikea.digital.permission.core.config.PermissionClientConfig;
import com.ikea.digital.permission.core.context.UserHolder;
import com.ikea.digital.permission.server.common.BkdConstants;
import com.ikea.digital.permission.server.common.OptTypeEnum;
import com.ikea.digital.permission.server.common.util.JacksonUtils;
import com.ikea.digital.permission.server.common.util.KeycloakManager;
import com.ikea.digital.permission.server.dto.UserAssignRoleDto;
import com.ikea.digital.permission.server.dto.UserCreateOrUpdateDto;
import com.ikea.digital.permission.server.dto.UserGrantGroupDto;
import com.ikea.digital.permission.server.dto.UserGrantRoleDto.RoleStoreDto;
import com.ikea.digital.permission.server.dto.UserSearchDto;
import com.ikea.digital.permission.server.exception.ResourceException;
import com.ikea.digital.permission.server.repository.ClientRepository;
import com.ikea.digital.permission.server.repository.LogRepository;
import com.ikea.digital.permission.server.repository.UserRepository;
import com.ikea.digital.permission.server.repository.UserRoleRepository;
import com.ikea.digital.permission.server.resource.domain.model.Role;
import com.ikea.digital.permission.server.resource.domain.model.User;
import com.ikea.digital.permission.server.resource.domain.model.UserRole;
import com.ikea.digital.permission.server.resource.domain.model.persistent.LogPO;

@Service
public class UserService {
	
	@Autowired
	KeycloakManager keycloakManager;
	
	@Autowired
	PermissionClientConfig pmClientConfig;
	
	@Autowired
	private UserRoleRepository userRoleRepository;
	
	@Autowired
	private UserRepository userRepository;
	
	@Autowired
	private LogRepository logRepository;
	
	@Autowired
	private ClientRepository clientRepository;
	
	public void createUser(UserCreateOrUpdateDto dto) {
		List<User> users = keycloakManager.searchUserByUsername(dto.getEmail(),0,1);
		if(CollectionUtils.isNotEmpty(users)) {
			throw new ResourceException(dto.getEmail() + "[已存在！]");
		}
		keycloakManager.createUser(dto.getName(),dto.getEmail(),dto.getEnable());
		
		logRepository.insert(new LogPO(OptTypeEnum.ADD_USER.getCode(), dto.getEmail(), JacksonUtils.object2JsonString(dto), 
				null, null));
	}

	public void updateUser(UserCreateOrUpdateDto dto) {
		User user = keycloakManager.getUserSimpleInfoById(dto.getId());
		if(user == null) {
			throw new ResourceException(dto.getName() + "[不存在！]");
		}
		keycloakManager.updateUser(dto.getId(), dto.getName(),  dto.getEnable());
		
		logRepository.insert(new LogPO(OptTypeEnum.EDIT_USER.getCode(), user.getEmail(),JacksonUtils.object2JsonString(dto), 
				JacksonUtils.object2JsonString(user), null));
	}

	public void deleteUser(String userId) {
		
		keycloakManager.deleteUser(userId);
		
		userRoleRepository.deleteByUserId(userId);
		
		Map<String, String> cmd = new HashedMap<>();
		cmd.put("id", userId);
		
		logRepository.insert(new LogPO(OptTypeEnum.DELETE_USER.getCode(), userId, JacksonUtils.object2JsonString(cmd), 
				null, null));
	}
	
	public List<User> searchUser(UserSearchDto dto){
		List<User> users = new ArrayList<>();
		Integer first = (dto.getPageIndex() - 1) * dto.getPageSize();
		if(StringUtils.isNotBlank(dto.getRoleName())) {
			String clientPkId = clientRepository.fetchClientPkIdByClientId(dto.getClientId());
			users = keycloakManager.searchUserByRoleName(clientPkId, dto.getRoleName(), first, dto.getPageSize());
			return users;
		}
		
		users = keycloakManager.searchUserByUsername(dto.getUserName(),(dto.getPageIndex() - 1) * dto.getPageSize(), dto.getPageSize());
		return users;
	}
	
	public int searchUserTotal(String clientId, String userName, String roleName){
		if(StringUtils.isNotBlank(roleName)) {
			return searchUserTotalByClientIdAndRoleName(clientId, roleName);
		}
		return keycloakManager.searchUserCountByUsername(userName);
	}
	
	public User fetchUserById(String userId){
		return userRepository.fetchUserById(userId);
	}
	
	public void assignGroup(UserGrantGroupDto dto) {
		List<String> oldGroupIds = keycloakManager.leaveGroup(dto.getUserId());
		keycloakManager.joinGroup(dto.getUserId(), dto.getGroupIds());
		logRepository.insert(new LogPO(OptTypeEnum.ASSIGN_GROUP.getCode(), dto.getUserId(), JacksonUtils.object2JsonString(dto), 
				JacksonUtils.object2JsonString(oldGroupIds), null));
	}

	public void assignRole(UserAssignRoleDto bo) {
		
		// 权限验证
		bo.validate();
		
		String clientPkId = keycloakManager.getClientPkId(bo.getClientId());
		List<UserRole> list = userRoleRepository.getByClientIdAnsUserId(bo.getClientId(), bo.getUserId());
        List<UserRole> userRoleOldData = userRoleRepository.getByClientIdAnsUserId(bo.getUserId(), bo.getClientId());

		// 新增
		if(CollectionUtils.isNotEmpty(bo.getAddedList())) {
			appendRoleStore(bo, list, clientPkId);
		}
		
		// 删除
		if(CollectionUtils.isNotEmpty(bo.getRemovedList())) {
			removeRoleStore(bo, list, clientPkId);
		}
		
        List<UserRole> userRoleNewData = userRoleRepository.getByClientIdAnsUserId(bo.getClientId(), bo.getUserId());
        logRepository.insert(new LogPO(OptTypeEnum.ASSIGN_ROLE.getCode(), bo.getUserId(), JacksonUtils.object2JsonString(bo),
				JacksonUtils.object2JsonString(userRoleOldData), JacksonUtils.object2JsonString(userRoleNewData)));
	}
	
	private void appendRoleStore(UserAssignRoleDto bo, List<UserRole> userRoleStoreList, String clientPkId) {
		
		String modifier = UserHolder.getUserInfo() == null ? "" : UserHolder.getUserInfo().getEmail();
		
		User user = userRepository.fetchUserById(bo.getUserId());
		String clientId = bo.getClientId();
		
		List<String> kcRoles = getUserClientKcRoleNames(user, clientId);
		
		UserRole entity;
		boolean exist;
		List<UserRole> addList = new ArrayList<>();
		List<String> kcAddedList = new ArrayList<>();
		for (RoleStoreDto dto : bo.getAddedList()) {
			exist = false;
			entity = new UserRole();
			if(CollectionUtils.isEmpty(kcRoles) || !kcRoles.contains(dto.getName())) {
				kcAddedList.add(dto.getName());
			}
			// 已有role,只需要修改对应的store列表
			for (UserRole roleStorePo : userRoleStoreList) {
				if(dto.getName().equals(roleStorePo.getRoleName())) {
					exist = true;
					entity.setId(roleStorePo.getId());
					if(BkdConstants.SysRole.contains(dto.getName())) {
						entity.setStoreIds(JacksonUtils.object2JsonString(Collections.singletonList(BkdConstants.DEFAULT_STORE)));
					} else if(kcAddedList.contains(dto.getName())) {
						entity.setStoreIds(JacksonUtils.object2JsonString(dto.getStoreIds()));
					} else {
						entity.setStoreIds(appendStoreIdsJson(roleStorePo.getStoreIds(), dto.getStoreIds()));
					}
					entity.setModifier(modifier);
					entity.setModifyGmt(new Date());
					userRoleRepository.updateByPrimaryKeySelective(entity);
				}
			}
			
			// 没有role, 需要先分配权限，再记录role-store列表
			if(!exist) {
				entity.init();
				entity.setClientId(bo.getClientId());
				entity.setUserId(bo.getUserId());
				entity.setRoleName(dto.getName());
				entity.setStoreIds(JacksonUtils.object2JsonString(dto.getStoreIds()));
				addList.add(entity);
			}
		}
		
		if(CollectionUtils.isNotEmpty(addList)) {
			userRoleRepository.insertList(addList);
		}
		
		// keycloak新增的role
		if(CollectionUtils.isNotEmpty(kcAddedList)) {
			keycloakManager.appendUserClientRole(bo.getUserId(), clientPkId, kcAddedList);
		}
		
	}
	
	private List<String> getUserClientKcRoleNames(User user, String clientId) {
		Map<String, List<Role>> clientRoleMap = user.getClientRoleMap();
		if(MapUtils.isEmpty(clientRoleMap)) {
			return Collections.emptyList();
		}
		
		if(!clientRoleMap.containsKey(clientId)) {
			return Collections.emptyList();
		}
		
		List<Role> kcRoles = clientRoleMap.get(clientId);
		if(CollectionUtils.isEmpty(kcRoles)) {
			return Collections.emptyList();
		}
		
		return kcRoles.stream().map(Role::getName).collect(Collectors.toList());
	}
	
	private void removeRoleStore(UserAssignRoleDto bo, List<UserRole> userRoleStoreList, String clientPkId) {
		String modifier = UserHolder.getUserInfo() == null ? "" : UserHolder.getUserInfo().getEmail();

		UserRole entity;
		boolean exist;
		List<String> removeList = new ArrayList<>();
		for (RoleStoreDto dto : bo.getRemovedList()) {
			exist = false;
			// 已有role,只需要修改对应的store列表
			for (UserRole roleStorePo : userRoleStoreList) {
				if(dto.getName().equals(roleStorePo.getRoleName()) && !BkdConstants.SysRole.contains(dto.getName())) {
					// 删除store后门店列表为空=删除权限
					List<String> storeList = removeStoreIdsJson(roleStorePo.getStoreIds(), dto.getStoreIds());
					if(CollectionUtils.isNotEmpty(storeList)) {
						exist = true;
						entity = new UserRole();
						entity.setId(roleStorePo.getId());
						entity.setStoreIds(JacksonUtils.object2JsonString(storeList));
						entity.setModifyGmt(new Date());
						entity.setModifier(modifier);
						userRoleRepository.updateByPrimaryKeySelective(entity);
					}
				}
			}
			
			// 没有role, 需要删除权限
			if(!exist) {
				removeList.add(dto.getName());
			}
		}
		
		if(CollectionUtils.isNotEmpty(removeList)) {
			userRoleRepository.deleteSelective(bo.getClientId(), bo.getUserId(), removeList);
			keycloakManager.removeUserClientRole(bo.getUserId(), clientPkId, removeList);
		}
	}
	
	private String appendStoreIdsJson(String storeIds, List<String> addedStoreIds) {
		List<String> originStoreIdList = JacksonUtils.readValue(storeIds, new TypeReference<List<String>>() {});
		for (String storeId : addedStoreIds) {
			if(!originStoreIdList.contains(storeId)) {
				originStoreIdList.add(storeId);
			}
		}
		return JacksonUtils.object2JsonString(originStoreIdList);
	}
	
	private List<String> removeStoreIdsJson(String storeIds, List<String> removeStoreIds) {
		List<String> originStoreIdList = JacksonUtils.readValue(storeIds, new TypeReference<List<String>>() {});
		originStoreIdList.removeAll(removeStoreIds);
		return originStoreIdList;
	}
	
	private int searchUserTotalByClientIdAndRoleName(String clientId, String roleName) {
		int total=0,first = 0,max=20;
		List<User> users = new ArrayList<>();
		String clientPkId = clientRepository.fetchClientPkIdByClientId(clientId);
		for(;;) {
			users = keycloakManager.searchUserByRoleName(clientPkId, roleName, first, max);
			if(CollectionUtils.isEmpty(users)) {
				break;
			}
			total+=users.size();
			first+=max;
		}
		return total;
	}
	
	public void assignClientManager(String userId, String clientId,List<String> roleNames,List<String> storeIds) {
		// 0、keycloak中，移除并新增
		String clientPkId = keycloakManager.getClientPkId(clientId);
		keycloakManager.assignUserClientAllRole(userId, clientPkId, roleNames);
		
		// 1、本地表中移除并新增加
		userRoleRepository.deleteByClientIdAndUserId(clientId, userId);
		
		String creator = UserHolder.getUserInfo() == null ? "" : UserHolder.getUserInfo().getEmail();
		List<UserRole> userRolePoList = roleNames.stream().map(roleName -> {
			UserRole userRolePo = new UserRole();
			userRolePo.init();
			userRolePo.setClientId(clientId);
			userRolePo.setRoleName(roleName);
			userRolePo.setStoreIds(JacksonUtils.object2JsonString(storeIds));
			userRolePo.setUserId(userId);
			userRolePo.setCreator(creator);
			return userRolePo;
		}).collect(Collectors.toList());
		
		if(CollectionUtils.isNotEmpty(userRolePoList)) {
			userRoleRepository.insertList(userRolePoList);
		}
	}
	
	
	public void appendRole(String clientId, List<RoleStoreDto> roleStores) {
		RoleStoreDto appendRoleStoreDto = roleStores.get(0);
		String userId = UserHolder.getUserInfo().getUserId();
		// 0、删除老的
		UserRole criteria = new UserRole();
		criteria.setUserId(userId);
		criteria.setClientId(clientId);
		criteria.setRoleName(appendRoleStoreDto.getName());
		userRoleRepository.deleteSelective(criteria);
		
		// 1、接入新记录				
		UserRole userRolePo = new UserRole();
		userRolePo.init();
		userRolePo.setClientId(clientId);
		userRolePo.setRoleName(appendRoleStoreDto.getName());
		userRolePo.setUserId(userId);
		userRolePo.setStoreIds(JacksonUtils.object2JsonString(appendRoleStoreDto.getStoreIds()));
		userRolePo.setCreator(UserHolder.getUserInfo() == null ? "" : UserHolder.getUserInfo().getEmail());
		userRoleRepository.insert(userRolePo);
		
		String clientPkId = keycloakManager.getClientPkId(clientId);
		keycloakManager.appendUserClientRole(userId, clientPkId, Collections.singletonList(appendRoleStoreDto.getName()));
	}
	
	public List<User> searchUser(UserSearchDto dto, String storeId){
		if(StringUtils.isBlank(storeId)) {
			return searchUser(dto);
		}
		Integer first = (dto.getPageIndex() - 1) * dto.getPageSize();
		List<UserRole> userRoles = userRoleRepository.selectByStoreIdAndRole(storeId, dto.getClientId(), 
				dto.getRoleName(), first, dto.getPageSize());
		
		if(CollectionUtils.isEmpty(userRoles)) return Collections.emptyList();
		
		return userRoles.stream().map(ur -> keycloakManager.getUserSimpleInfoById(ur.getUserId())).collect(Collectors.toList());
	}
	
}
