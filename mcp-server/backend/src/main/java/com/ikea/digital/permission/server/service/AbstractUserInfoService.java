/**
 * Project Name:permission-server
 * File Name:AbstractUserInfoService.java
 * Package Name:com.ikea.digital.permission.server.resource.service
 * Date:Feb 26, 20243:48:46 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.ikea.digital.permission.core.config.PermissionClientConfig;
import com.ikea.digital.permission.server.common.BkdConstants;
import com.ikea.digital.permission.server.common.util.ApplicationHolder;
import com.ikea.digital.permission.server.repository.UserRepository;
import com.ikea.digital.permission.server.repository.UserRoleRepository;
import com.ikea.digital.permission.server.resource.domain.model.Role;
import com.ikea.digital.permission.server.resource.domain.model.User;
import com.ikea.digital.permission.server.resource.domain.model.UserRole;
import com.ikea.digital.permission.server.validator.AuthValidator;
import com.ikea.digital.permission.server.vo.ResourceVo;
import com.ikea.digital.permission.server.vo.UserDetailVo;
import com.ikea.mas.permission.common.PmToken;

/**
 * ClassName:AbstractUserInfoService <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Feb 26, 2024 3:48:46 PM <br/>
 * <AUTHOR>	 
 */
public abstract class AbstractUserInfoService {
	
	@Autowired
	protected UserRepository userRepository;
	
	@Autowired
	protected UserRoleRepository userRoleRepository;
	
	@Autowired
	protected PermissionClientConfig pmClientConfig;
	
	@Autowired
	protected UserCacheService userCacheService;
	
	@Autowired
	protected AuthValidator authValidator;
	
	@Autowired
	protected RoleService roleService;
	
	@Autowired
	protected MenuService menuService;
	
	public UserDetailVo fetchUserByUserToken(String userToken) {
		// check token
		String clientId = ApplicationHolder.get().equals(BkdConstants.ADMIN_PORTAL)? pmClientConfig.getClientId() : ApplicationHolder.get();
		if(!authValidator.checkToken(userToken, clientId)) {
			return null;
		}
		
		PmToken pmToken = PmToken.from(userToken);
		
		// 刷新token缓存时间
        userCacheService.refreshSsoTime(pmToken.getSsoUUID(), clientId);
        
        // 从缓存中获取用户信息
        String userId = pmToken.getUserId();
        
        UserDetailVo userDetail = loadUserInfoFromCache(userToken, userId, clientId);
		if(Objects.nonNull(userDetail)) {
			return userDetail;
		}
		
		// 从keycloak中获取用户信息
		User user = userRepository.fetchUserById(userId);
		userDetail = new UserDetailVo(user);
		
		// 处理用户权限信息
		fillUserDetailInfo(userDetail, user.getAttrMap(), clientId);
		
		cacheUserInfo(userDetail, clientId);
		
		return userDetail;
	}

	protected void cacheUserInfo(UserDetailVo userDetail, String clientId) {
		userCacheService.cacheUser(userDetail, clientId);
	}

	protected void fillUserDetailInfo(UserDetailVo user, Map<String,List<String>> attrMap, String clientId) {
		fillUserAttr(user, attrMap);
		
		Map<String, List<UserRole>> userRoleStoreMap = userRoleRepository.getUserRoleStoreMap(user.getUserId());
		
		fillUserRolesAndStores(user, userRoleStoreMap);
	}
	
	protected abstract void fillUserRolesAndStores(UserDetailVo user, Map<String, List<UserRole>> userRoleStoreMap);

	protected void fillUserAttr(UserDetailVo user, Map<String,List<String>> attrMap) {
		if(MapUtils.isNotEmpty(attrMap)) {
			List<String> networkId = attrMap.get("networkId");
			if(CollectionUtils.isNotEmpty(networkId)) {
				user.setNetworkId(networkId.get(0));
			}
			List<String> avatar = attrMap.get("picture");
			if(CollectionUtils.isNotEmpty(avatar)) {
				user.setAvatar(avatar.get(0));
			}
		}
	}
	
	protected void fillMenu(UserDetailVo user, String clientId) {
		List<String> clientRoles = user.getRoleMap().get(clientId);
		if(CollectionUtils.isEmpty(clientRoles)) {
			return;
		}
		Map<String,List<String>> menuStoreMap = new HashMap<>();
		List<String> menuIds = new ArrayList<>();
		// 查询所有role 
		List<Role> roles = roleService.getRoles(clientId);
		if(CollectionUtils.isEmpty(roles)) {
			return;
		}
		Map<String, Role> rolesMap = roles.stream().collect(Collectors.toMap(Role::getName, role -> role, (r1, r2) -> r1));
		for(String roleName : clientRoles) {
			if(!rolesMap.containsKey(roleName)) {
				continue;
			}
			Role role = rolesMap.get(roleName);
			List<String> roleMenuIds = role.getAttrMap().get("menuIds");
			if(CollectionUtils.isEmpty(roleMenuIds)) {
				continue;
			}
			menuIds.addAll(roleMenuIds);
			
			Map<String, List<String>> roleStoreMap = user.getRoleStoreMap();
			if(MapUtils.isEmpty(roleStoreMap)) {
				continue;
			}
			List<String> roleStoreIds = roleStoreMap.get(roleName);
			roleMenuIds.forEach(roleMenuId -> {
				List<String> storeIds = menuStoreMap.computeIfAbsent(roleMenuId, k -> new ArrayList<>());
				if(CollectionUtils.isNotEmpty(roleStoreIds)) {
					storeIds.addAll(roleStoreIds);
				}
			});
			
		}
		if(CollectionUtils.isEmpty(menuIds)) {
			return;
		}
		List<Long> distinctMenuIds = menuIds.stream().map(Long::valueOf).distinct().collect(Collectors.toList());
		List<ResourceVo> menuDtoList = menuService.buildResourceTree(distinctMenuIds); 
		menuDtoList.forEach(menu -> {
			fillResourceStoreIds(menu, menuStoreMap);
		});
		user.setResourceMap(Collections.singletonMap(clientId, menuDtoList));
	}
	
	private void fillResourceStoreIds(ResourceVo resourceDto,Map<String,List<String>> menuStoreMap) {
		List<String> menuStoreIds = menuStoreMap.get(String.valueOf(resourceDto.getId()));
		resourceDto.setStoreIds(menuStoreIds == null ? Collections.emptyList() : menuStoreIds.stream().distinct().collect(Collectors.toList()));
		List<ResourceVo> subRs = resourceDto.getSubResource();
		if(CollectionUtils.isNotEmpty(subRs)) {
			subRs.forEach(rs -> {
				fillResourceStoreIds(rs, menuStoreMap);
			});
		}
	}

	protected UserDetailVo loadUserInfoFromCache(String userToken, String userId, String clientId) {
		return userCacheService.getUser(userToken, userId, clientId);
	}
	
	public boolean supportClient(String clientName) {
		return false;
	}

}

