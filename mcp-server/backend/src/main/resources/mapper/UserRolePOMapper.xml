<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ikea.digital.permission.server.resource.domain.mapper.UserRolePOMapper">
  <resultMap id="BaseResultMap" type="com.ikea.digital.permission.server.resource.domain.model.UserRole">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="role_name" jdbcType="VARCHAR" property="roleName" />
    <result column="store_ids" jdbcType="VARCHAR" property="storeIds" />
    <result column="create_gmt" jdbcType="TIMESTAMP" property="createGmt" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="modify_gmt" jdbcType="TIMESTAMP" property="modifyGmt" />
    <result column="client_id" jdbcType="VARCHAR" property="clientId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, role_name, store_ids, create_gmt, creator, modifier, modify_gmt, client_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_role
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from user_role
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.ikea.digital.permission.server.resource.domain.model.UserRole"
    useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into user_role (user_id, role_name, 
      store_ids, create_gmt, creator, 
      modifier, modify_gmt, client_id
      )
    values (#{userId,jdbcType=VARCHAR}, #{roleName,jdbcType=VARCHAR}, 
      #{storeIds,jdbcType=VARCHAR}, #{createGmt,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{modifyGmt,jdbcType=TIMESTAMP}, #{clientId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ikea.digital.permission.server.resource.domain.model.UserRole"
    useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into user_role
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="roleName != null">
        role_name,
      </if>
      <if test="storeIds != null">
        store_ids,
      </if>
      <if test="createGmt != null">
        create_gmt,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="modifyGmt != null">
        modify_gmt,
      </if>
      <if test="clientId != null">
        client_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="roleName != null">
        #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="storeIds != null">
        #{storeIds,jdbcType=VARCHAR},
      </if>
      <if test="createGmt != null">
        #{createGmt,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyGmt != null">
        #{modifyGmt,jdbcType=TIMESTAMP},
      </if>
      <if test="clientId != null">
        #{clientId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ikea.digital.permission.server.resource.domain.model.UserRole">
    update user_role
    <set>
      <if test="userId != null and userId != ''">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="roleName != null and roleName != ''">
        role_name = #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="storeIds != null and storeIds != ''">
        store_ids = #{storeIds,jdbcType=VARCHAR},
      </if>
      <if test="createGmt != null">
        create_gmt = #{createGmt,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null and modifier != ''">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyGmt != null">
        modify_gmt = #{modifyGmt,jdbcType=TIMESTAMP},
      </if>
      <if test="clientId != null and clientId != ''">
        client_id = #{clientId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ikea.digital.permission.server.resource.domain.model.UserRole">
    update user_role
    set user_id = #{userId,jdbcType=VARCHAR},
      role_name = #{roleName,jdbcType=VARCHAR},
      store_ids = #{storeIds,jdbcType=VARCHAR},
      create_gmt = #{createGmt,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      modify_gmt = #{modifyGmt,jdbcType=TIMESTAMP},
      client_id = #{clientId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="selectSelective" parameterType="com.ikea.digital.permission.server.resource.domain.model.UserRole" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_role
    <where>
      <if test="userId != null and userId != ''">
        and user_id = #{userId,jdbcType=VARCHAR}
      </if>
      <if test="roleName != null and roleName != ''">
        and role_name = #{roleName,jdbcType=VARCHAR}
      </if>
      <if test="storeIds != null">
        and store_ids = #{storeIds,jdbcType=VARCHAR}
      </if>
      <if test="clientId != null and clientId != ''">
        and client_id = #{clientId,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
  
  <delete id="deleteSelective" parameterType="com.ikea.digital.permission.server.resource.domain.model.UserRole">
    delete from user_role
    <trim prefix="where" prefixOverrides="and">
      <if test="userId != null and userId != ''">
        and user_id = #{userId,jdbcType=VARCHAR}
      </if>
      <if test="clientId != null and clientId != ''">
        and client_id = #{clientId,jdbcType=VARCHAR}
      </if>
      <if test="roleName != null and roleName != ''">
        and role_name = #{roleName,jdbcType=VARCHAR}
      </if>
      <if test="storeIds != null">
        and store_ids = #{storeIds,jdbcType=VARCHAR}
      </if>
    </trim>
  </delete>
  
  <delete id="deleteSelectiveWithRoles" >
    delete from user_role
    <trim prefix="where" prefixOverrides="and">
      <if test="userId != null and userId != ''">
        and user_id = #{userId,jdbcType=VARCHAR}
      </if>
      <if test="clientId != null and clientId != ''">
        and client_id = #{clientId,jdbcType=VARCHAR}
      </if>
      <if test="roles != null">
        and role_name in 
        <foreach collection="roles" item = "role" separator="," open="(" close=")">
            #{role, jdbcType=VARCHAR}
        </foreach>
      </if>
    </trim>
  </delete>
  
  <insert id="insertList" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into user_role (user_id, role_name, store_ids, create_gmt, creator, 
        modifier, modify_gmt, client_id) values 
    <foreach collection = "users" item = "userRole" separator = ",">
        (#{userRole.userId}, #{userRole.roleName}, 
        #{userRole.storeIds}, #{userRole.createGmt,jdbcType=TIMESTAMP}, #{userRole.creator}, 
         #{userRole.modifier}, #{userRole.modifyGmt,jdbcType=TIMESTAMP}, #{userRole.clientId}
        )
    </foreach>
  </insert>
  
  <select id="selectByStoreId" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List" />
    FROM user_role
    <where>
        <if test="clientId != null and clientId != ''">
            and client_id = #{clientId,jdbcType=VARCHAR}
        </if>
        <if test="storeId != null and storeId != ''">
            and store_ids LIKE CONCAT('%"', #{storeId,jdbcType=VARCHAR}, '"%')
        </if>
    </where>
    ORDER BY create_gmt ASC
    LIMIT 20
  </select>
  
  <select id="selectByRoleAndStore" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List" />
    FROM user_role
    <where>
        <if test="clientId != null and clientId != ''">
            and client_id = #{clientId,jdbcType=VARCHAR}
        </if>
        <if test="roleName != null and roleName != ''">
            and role_name = #{roleName,jdbcType=VARCHAR}
        </if>
        <if test="storeId != null and storeId != ''">
            and store_ids LIKE CONCAT('%"', #{storeId,jdbcType=VARCHAR}, '"%')
        </if>
    </where>
    ORDER BY id ASC
    <if test="start != null and size != null">
        limit #{size} offset #{start}
    </if>
  </select>
  
  <update id="updateStoreIdsBatch"  parameterType="java.util.List">  
    <foreach collection="list" item="item" index="index" open="" close="" separator=";">
        update user_role
        set store_ids = #{item.storeIds}
        where id = #{item.id}
    </foreach>      
</update>
</mapper>