#!/usr/bin/env node

/**
 * 🧪 JWT Cookies Test Script
 *
 * Purpose:
 * 1. Test JWT-based cookie acquisition
 * 2. Verify JWT token decoding and session extraction
 * 3. Test different JWT-to-cookie exchange methods
 * 4. Validate caching and performance
 */

import { globalJWTCookieManager } from '../dist/auth/jwt-cookie-manager.js';
import jwt from 'jsonwebtoken';

/**
 * Sample JWT tokens for testing (you can replace with real tokens)
 */
const SAMPLE_JWT_TOKENS = {
  // This is a sample structure - replace with real JWT from your frontend
  valid: 'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICIwYTQ4ZGU0Yy1jMDU4LTQ1YWUtYjkxZS03MGRkODRjMDQ4MDMifQ.eyJleHAiOjE3NTQyMTM1NTksImlhdCI6MTc1Mzk1NDM1OSwianRpIjoiZTBmMDQ2YmItZGJmNS00ZTljLTkxYWQtZDM2Njc2MmFiZjlmIiwiaXNzIjoiaHR0cHM6Ly9rZXljbG9hay5pbmdrYS1kdC5jbi9hdXRoL3JlYWxtcy9tYXN0ZXIiLCJzdWIiOiIzZDJlMjY1Yy1mODk1LTRlMzItYjNiZS01YTEzOWZhODU2YzEiLCJ0eXAiOiJTZXJpYWxpemVkLUlEIiwic2lkIjoiN2FiNDJiZDktYmFjMi00ODBkLWIzNzUtN2JhNThkZTIzMjM5Iiwic3RhdGVfY2hlY2tlciI6Ikp5bF9jV2RZLW51MnU5NG5VdzZ2TzZvMkVYN2twakwzeDBlVzBDeUdCRlUifQ.invalid_signature',
  invalid: 'invalid.jwt.token',
  expired: null // Will be generated
};

/**
 * Test JWT token decoding
 */
async function testJWTDecoding() {
  console.log('🔍 ==================== JWT DECODING TEST ====================');
  
  try {
    console.log('🔄 Testing JWT token decoding...');
    
    // Test with sample token (decode without verification)
    const decoded = jwt.decode(SAMPLE_JWT_TOKENS.valid);
    
    if (decoded && typeof decoded === 'object') {
      console.log('✅ JWT decoding successful');
      console.log('   Payload structure:');
      console.log(`     Subject (sub): ${decoded.sub || 'N/A'}`);
      console.log(`     Session (sid): ${decoded.sid || 'N/A'}`);
      console.log(`     Issuer (iss): ${decoded.iss || 'N/A'}`);
      console.log(`     Client ID (azp): ${decoded.azp || decoded.client_id || 'N/A'}`);
      console.log(`     Expires (exp): ${decoded.exp ? new Date(decoded.exp * 1000).toISOString() : 'N/A'}`);
      console.log(`     Scopes: ${decoded.scope || 'N/A'}`);
      
      return decoded;
    } else {
      console.log('❌ JWT decoding failed - invalid structure');
      return null;
    }
  } catch (error) {
    console.error('❌ JWT decoding test failed:', error.message);
    return null;
  }
}

/**
 * Test JWT cookie acquisition with sample token
 */
async function testJWTCookieAcquisition() {
  console.log('\n🍪 ==================== JWT COOKIE ACQUISITION TEST ====================');
  
  try {
    console.log('🔄 Testing JWT-based cookie acquisition...');
    console.log('   Note: This will likely fail with sample token, but tests the flow');
    
    const startTime = Date.now();
    const cookie = await globalJWTCookieManager.getSessionCookieFromJWT(SAMPLE_JWT_TOKENS.valid);
    const duration = Date.now() - startTime;
    
    if (cookie) {
      console.log(`✅ JWT cookie acquisition successful! (${duration}ms)`);
      console.log(`   Cookie: ${cookie.substring(0, 50)}...`);
      return cookie;
    } else {
      console.log(`⚠️ JWT cookie acquisition returned null (${duration}ms)`);
      console.log('   This is expected with sample/invalid tokens');
      return null;
    }
  } catch (error) {
    console.error('❌ JWT cookie acquisition failed:', error.message);
    console.log('   This is expected with sample/invalid tokens');
    return null;
  }
}

/**
 * Test JWT cookie caching
 */
async function testJWTCookieCaching() {
  console.log('\n💾 ==================== JWT COOKIE CACHING TEST ====================');
  
  try {
    console.log('🔄 Testing JWT cookie caching...');
    
    // First call
    const startTime1 = Date.now();
    const cookie1 = await globalJWTCookieManager.getSessionCookieFromJWT(SAMPLE_JWT_TOKENS.valid);
    const duration1 = Date.now() - startTime1;
    
    // Second call (should be cached)
    const startTime2 = Date.now();
    const cookie2 = await globalJWTCookieManager.getSessionCookieFromJWT(SAMPLE_JWT_TOKENS.valid);
    const duration2 = Date.now() - startTime2;
    
    console.log(`   First call: ${duration1}ms`);
    console.log(`   Second call: ${duration2}ms`);
    
    if (cookie1 === cookie2) {
      console.log('✅ Caching working correctly - same cookie returned');
    } else {
      console.log('⚠️ Caching behavior - different results (may be expected with failures)');
    }
    
    // Test cache stats
    const stats = globalJWTCookieManager.getCacheStats();
    console.log('📊 Cache Statistics:');
    console.log(`   Total cached entries: ${stats.totalCached}`);
    stats.entries.forEach((entry, index) => {
      console.log(`   Entry ${index + 1}:`);
      console.log(`     User: ${entry.sub}`);
      console.log(`     Session: ${entry.sid}`);
      console.log(`     Created: ${new Date(entry.createdAt).toISOString()}`);
      console.log(`     Expires: ${new Date(entry.expiresAt).toISOString()}`);
    });
    
  } catch (error) {
    console.error('❌ JWT caching test failed:', error.message);
  }
}

/**
 * Test with invalid JWT tokens
 */
async function testInvalidJWTHandling() {
  console.log('\n❌ ==================== INVALID JWT HANDLING TEST ====================');
  
  const testCases = [
    { name: 'Invalid JWT format', token: SAMPLE_JWT_TOKENS.invalid },
    { name: 'Empty token', token: '' },
    { name: 'Null token', token: null },
    { name: 'Malformed JWT', token: 'not.a.jwt' }
  ];
  
  for (const testCase of testCases) {
    try {
      console.log(`🔄 Testing: ${testCase.name}`);
      
      if (testCase.token) {
        const result = await globalJWTCookieManager.getSessionCookieFromJWT(testCase.token);
        if (result === null) {
          console.log(`   ✅ Correctly handled invalid token - returned null`);
        } else {
          console.log(`   ⚠️ Unexpected result: ${result}`);
        }
      } else {
        console.log(`   ⚠️ Skipping null token test`);
      }
    } catch (error) {
      console.log(`   ✅ Correctly threw error: ${error.message}`);
    }
  }
}

/**
 * Test cache clearing
 */
async function testCacheClearing() {
  console.log('\n🗑️ ==================== CACHE CLEARING TEST ====================');
  
  try {
    console.log('🔄 Testing cache clearing...');
    
    // Clear cache
    globalJWTCookieManager.clearCache();
    
    // Check stats
    const stats = globalJWTCookieManager.getCacheStats();
    if (stats.totalCached === 0) {
      console.log('✅ Cache cleared successfully');
    } else {
      console.log('❌ Cache clearing failed');
    }
    
    // Test acquisition after clear
    console.log('🔄 Testing acquisition after cache clear...');
    const startTime = Date.now();
    const cookie = await globalJWTCookieManager.getSessionCookieFromJWT(SAMPLE_JWT_TOKENS.valid);
    const duration = Date.now() - startTime;
    
    console.log(`   Acquisition after clear: ${duration}ms`);
    if (cookie) {
      console.log('✅ Acquisition works after cache clear');
    } else {
      console.log('⚠️ No cookie acquired (expected with sample token)');
    }
    
  } catch (error) {
    console.error('❌ Cache clearing test failed:', error.message);
  }
}

/**
 * Demonstrate usage with real JWT token
 */
async function demonstrateRealUsage() {
  console.log('\n💡 ==================== REAL USAGE DEMONSTRATION ====================');
  
  console.log('📝 To use JWT-based cookie acquisition with real tokens:');
  console.log('');
  console.log('1. **Get JWT token from your frontend:**');
  console.log('   ```javascript');
  console.log('   // In your frontend application');
  console.log('   const token = await keycloak.token; // or however you get the token');
  console.log('   ```');
  console.log('');
  console.log('2. **Use in MCP server:**');
  console.log('   ```javascript');
  console.log('   import { globalJWTCookieManager } from "./auth/jwt-cookie-manager.js";');
  console.log('   ');
  console.log('   // Get session cookie from JWT');
  console.log('   const cookie = await globalJWTCookieManager.getSessionCookieFromJWT(jwtToken);');
  console.log('   ');
  console.log('   // Use cookie for API calls');
  console.log('   const response = await fetch("/api/endpoint", {');
  console.log('     headers: { "Cookie": cookie }');
  console.log('   });');
  console.log('   ```');
  console.log('');
  console.log('3. **Automatic integration:**');
  console.log('   The service adapter automatically tries JWT-based cookies when OAuth2 tokens are available.');
  console.log('   Priority: OAuth2 token → JWT cookies → Dynamic cookies → Static cookies');
  console.log('');
  console.log('4. **Benefits:**');
  console.log('   ✅ No need to manually extract Keycloak session cookies');
  console.log('   ✅ Works with any valid JWT token from frontend');
  console.log('   ✅ Automatic caching and refresh');
  console.log('   ✅ Graceful fallback to other auth methods');
  console.log('');
  console.log('5. **Test with real token:**');
  console.log('   Replace SAMPLE_JWT_TOKENS.valid with your real JWT token and run this test again.');
}

/**
 * Main test function
 */
async function runJWTCookiesTest() {
  console.log('🧪 ==================== JWT COOKIES TEST SUITE ====================');
  console.log('🎯 Purpose: Test JWT-based cookie acquisition and management\n');

  // Test 1: JWT decoding
  const decodedPayload = await testJWTDecoding();

  // Test 2: JWT cookie acquisition
  const cookie = await testJWTCookieAcquisition();

  // Test 3: JWT cookie caching
  await testJWTCookieCaching();

  // Test 4: Invalid JWT handling
  await testInvalidJWTHandling();

  // Test 5: Cache clearing
  await testCacheClearing();

  // Test 6: Usage demonstration
  await demonstrateRealUsage();

  console.log('\n🏁 ==================== TEST SUITE COMPLETE ====================');
  console.log('✅ JWT cookies test suite finished');
  console.log('💡 To test with real JWT tokens, replace the sample token and run again');
  console.log('🔗 Integration: JWT cookie manager is automatically used in service adapter');
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runJWTCookiesTest().catch(error => {
    console.error('💥 Test suite crashed:', error);
    process.exit(1);
  });
}

export { runJWTCookiesTest };
