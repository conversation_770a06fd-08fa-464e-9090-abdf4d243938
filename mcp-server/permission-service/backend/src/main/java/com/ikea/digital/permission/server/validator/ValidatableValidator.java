package com.ikea.digital.permission.server.validator;

import java.util.Objects;

import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

@Component
public class ValidatableValidator implements Validator { 

    @Override
    public boolean supports(@NonNull Class<?> clazz) {
        return Validatable.class.equals(clazz) || Validatable.class.isAssignableFrom(clazz);
    }

    @Override
    public void validate(@NonNull Object target, Errors errors) {
        if (Objects.nonNull(target)) {
            Validatable validatable = (Validatable) target;
            validatable.validate();
        }
    }
}
