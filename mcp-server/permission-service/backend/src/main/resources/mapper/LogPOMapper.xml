<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ikea.digital.permission.server.resource.domain.mapper.LogPOMapper">
  <resultMap id="BaseResultMap" type="com.ikea.digital.permission.server.resource.domain.model.persistent.LogPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="opt_type" jdbcType="SMALLINT" property="optType" />
    <result column="opt_obj" jdbcType="VARCHAR" property="optObj" />
    <result column="opt_cmd" jdbcType="VARCHAR" property="optCmd" />
    <result column="old_data" jdbcType="VARCHAR" property="oldData" />
    <result column="new_data" jdbcType="VARCHAR" property="newData" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_gmt" jdbcType="TIMESTAMP" property="createGmt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, opt_type, opt_obj, opt_cmd, old_data, new_data, creator, create_gmt
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from operation_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from operation_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.ikea.digital.permission.server.resource.domain.model.persistent.LogPO"
    useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into operation_log (opt_type, opt_obj, 
      opt_cmd, old_data, new_data, 
      creator, create_gmt)
    values (#{optType,jdbcType=SMALLINT}, #{optObj,jdbcType=VARCHAR}, 
      #{optCmd,jdbcType=VARCHAR}, #{oldData,jdbcType=VARCHAR}, #{newData,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{createGmt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.ikea.digital.permission.server.resource.domain.model.persistent.LogPO"
    useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into operation_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="optType != null">
        opt_type,
      </if>
      <if test="optObj != null">
        opt_obj,
      </if>
      <if test="optCmd != null">
        opt_cmd,
      </if>
      <if test="oldData != null">
        old_data,
      </if>
      <if test="newData != null">
        new_data,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createGmt != null">
        create_gmt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="optType != null">
        #{optType,jdbcType=SMALLINT},
      </if>
      <if test="optObj != null">
        #{optObj,jdbcType=VARCHAR},
      </if>
      <if test="optCmd != null">
        #{optCmd,jdbcType=VARCHAR},
      </if>
      <if test="oldData != null">
        #{oldData,jdbcType=VARCHAR},
      </if>
      <if test="newData != null">
        #{newData,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createGmt != null">
        #{createGmt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ikea.digital.permission.server.resource.domain.model.persistent.LogPO">
    update operation_log
    <set>
      <if test="optType != null">
        opt_type = #{optType,jdbcType=SMALLINT},
      </if>
      <if test="optObj != null">
        opt_obj = #{optObj,jdbcType=VARCHAR},
      </if>
      <if test="optCmd != null">
        opt_cmd = #{optCmd,jdbcType=VARCHAR},
      </if>
      <if test="oldData != null">
        old_data = #{oldData,jdbcType=VARCHAR},
      </if>
      <if test="newData != null">
        new_data = #{newData,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createGmt != null">
        create_gmt = #{createGmt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ikea.digital.permission.server.resource.domain.model.persistent.LogPO">
    update operation_log
    set opt_type = #{optType,jdbcType=SMALLINT},
      opt_obj = #{optObj,jdbcType=VARCHAR},
      opt_cmd = #{optCmd,jdbcType=VARCHAR},
      old_data = #{oldData,jdbcType=VARCHAR},
      new_data = #{newData,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_gmt = #{createGmt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>