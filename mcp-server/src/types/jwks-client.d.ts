declare module 'jwks-client' {
  interface JwksClientOptions {
    jwksUri: string;
    cache?: boolean;
    cacheMaxEntries?: number;
    cacheMaxAge?: number;
    rateLimit?: boolean;
    jwksRequestsPerMinute?: number;
    timeout?: number;
    strictSsl?: boolean;
    requestHeaders?: Record<string, string>;
    requestAgentOptions?: any;
    getKeysInterceptor?: () => any;
  }

  interface SigningKey {
    kid: string;
    publicKey?: string;
    rsaPublicKey?: string;
    getPublicKey(): string;
  }

  interface JwksClient {
    getSigningKey(kid: string, callback: (err: Error | null, key?: SigningKey) => void): void;
    getSigningKeys(callback: (err: Error | null, keys?: SigningKey[]) => void): void;
  }

  function jwksClient(options: JwksClientOptions): JwksClient;
  export = jwksClient;
}
