/**
 * Project Name:permission-server
 * File Name:UserRoleRepository.java
 * Package Name:com.ikea.digital.permission.server.repository
 * Date:Jan 30, 202411:10:54 AM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.fasterxml.jackson.core.type.TypeReference;
import com.ikea.digital.permission.core.context.UserHolder;
import com.ikea.digital.permission.server.common.BkdConstants;
import com.ikea.digital.permission.server.common.util.KeycloakManager;
import com.ikea.digital.permission.server.dto.UserGrantRoleDto.RoleStoreDto;
import com.ikea.digital.permission.server.resource.domain.mapper.UserRolePOMapper;
import com.ikea.digital.permission.server.resource.domain.model.Role;
import com.ikea.digital.permission.server.resource.domain.model.User;
import com.ikea.digital.permission.server.resource.domain.model.UserRole;
import com.ikea.mas.permission.utils.JacksonUtils;

/**
 * ClassName:UserRoleRepository <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Date:     Jan 30, 2024 11:10:54 AM <br/>
 * <AUTHOR>	 
 */
@Repository
public class UserRoleRepository {
	
	@Autowired
	private UserRolePOMapper mapper;
	
	@Autowired
	private KeycloakManager keycloakManager;
	
	public int deleteByUserId(String userId) {
		Assert.hasText(userId, "userId cannot be blank");
		UserRole userRole = new UserRole();
		userRole.setUserId(userId);
		return mapper.deleteSelective(userRole);
	}
	
	public int deleteByClientIdAndRoleName(String clientId, String roleName) {
		Assert.hasText(clientId, "clientId cannot be blank");
		Assert.hasText(roleName, "roleName cannot be blank");
		UserRole userRole = new UserRole();
		userRole.setClientId(clientId);
		userRole.setRoleName(roleName);
		return mapper.deleteSelective(userRole);
	}
	
	public int deleteByClientIdAndUserId(String clientId, String userId) {
		Assert.hasText(clientId, "clientId cannot be blank");
		Assert.hasText(userId, "userId cannot be blank");
		UserRole userRole = new UserRole();
		userRole.setClientId(clientId);
		userRole.setUserId(userId);
		return mapper.deleteSelective(userRole);
	}
	
	public int deleteByClientIdAndUserIdAndRoleName(String clientId, String userId, String roleName) {
		Assert.hasText(clientId, "clientId cannot be blank");
		Assert.hasText(userId, "userId cannot be blank");
		Assert.hasText(roleName, "roleName cannot be blank");
		UserRole userRole = new UserRole();
		userRole.setClientId(clientId);
		userRole.setRoleName(roleName);
		userRole.setUserId(userId);
		return mapper.deleteSelective(userRole);
	}
	
	public int deleteSelective(String clientId, String userId, List<String> roles) {
		Assert.notEmpty(roles, "roles cant not be empty");
		return mapper.deleteSelectiveWithRoles(clientId, userId, roles);
	}
	
	public int deleteSelective(UserRole userRole) {
		Assert.notNull(userRole, "userRole cannot be null");
		return mapper.deleteSelective(userRole);
	}
	
	public List<UserRole> getByClientIdAnsUserId(String clientId, String userId) {
		Assert.hasText(clientId, "clientId cannot be blank");
		Assert.hasText(userId, "userId cannot be blank");
		UserRole userRole = new UserRole();
		userRole.setClientId(clientId);
		userRole.setUserId(userId);
		return mapper.selectSelective(userRole);
	}
	
	public List<UserRole> getByUserId(String userId) {
		Assert.hasText(userId, "userId cannot be blank");
		UserRole userRole = new UserRole();
		userRole.setUserId(userId);
		return mapper.selectSelective(userRole);
	}
	
	public int updateByPrimaryKeySelective(UserRole user) {
		return mapper.updateByPrimaryKeySelective(user);
	}
	
	public int insertList(List<UserRole> userRoles) {
		Assert.notEmpty(userRoles, "userRolePO list can not be empty");
		return mapper.insertList(userRoles);
	}
	
	public long insert(UserRole userRole) {
		return mapper.insert(userRole);
	}
	
	public List<UserRole> selectByStoreId(String storeId, String clientId) {
		return mapper.selectByStoreId(storeId, clientId);
	}
	
	public List<UserRole> selectByStoreIdAndRole(String storeId, String clientId, 
			String roleName, Integer start, Integer size) {
		return mapper.selectByRoleAndStore(storeId, clientId, roleName, start, size);
	}
	
	public int updateStoreIdsBatch(List<UserRole> list) {
		return mapper.updateStoreIdsBatch(list);
	}
	
	public int removeUserClientRole(String userId, String clientId, String clientPkId, List<String> removeList) {
		keycloakManager.removeUserClientRole(userId, clientPkId, removeList);
		return deleteSelective(clientId, userId, removeList);
	}
	
	public void appendRoleStore(User user, String clientId, String clientPkId, List<RoleStoreDto> addedList) {
		String modifier = UserHolder.getUserInfo() == null ? "" : UserHolder.getUserInfo().getEmail();
		List<String> kcRoles = getUserClientKcRoleNames(user, clientId);
		
		// 用户在permission service中现有的roleStore列表
		List<UserRole> userRoleStoreList = getByClientIdAnsUserId(clientId, user.getUserId());
					
		UserRole entity;
		boolean exist;
		List<UserRole> addList = new ArrayList<>();
		List<String> kcAddedList = new ArrayList<>();
		for (RoleStoreDto dto : addedList) {
			exist = false;
			entity = new UserRole();
			if(CollectionUtils.isEmpty(kcRoles) || !kcRoles.contains(dto.getName())) {
				kcAddedList.add(dto.getName());
			}
			
			// 已有role,只需要修改对应的store列表
			for (UserRole roleStorePo : userRoleStoreList) {
				if(dto.getName().equals(roleStorePo.getRoleName())) {
					exist = true;
					entity.setId(roleStorePo.getId());
					if(BkdConstants.SysRole.contains(dto.getName())) {
						entity.setStoreIds(JacksonUtils.object2JsonString(Collections.singletonList(BkdConstants.DEFAULT_STORE)));
					} else if(kcAddedList.contains(dto.getName())) {
						entity.setStoreIds(JacksonUtils.object2JsonString(dto.getStoreIds()));
					} else {
						entity.setStoreIds(appendStoreIdsJson(roleStorePo.getStoreIds(), dto.getStoreIds()));
					}
					entity.setModifier(modifier);
					entity.setModifyGmt(new Date());
					updateByPrimaryKeySelective(entity);
				}
			}
			
			// 没有role, 需要先分配权限，再记录role-store列表
			if(!exist) {
				entity.init();
				entity.setClientId(clientId);
				entity.setUserId(user.getUserId());
				entity.setRoleName(dto.getName());
				entity.setStoreIds(JacksonUtils.object2JsonString(dto.getStoreIds()));
				addList.add(entity);
			}
		}
		
		if(CollectionUtils.isNotEmpty(addList)) {
			insertList(addList);
		}
		
		// keycloak新增的role
		if(CollectionUtils.isNotEmpty(kcAddedList)) {
			keycloakManager.appendUserClientRole(user.getUserId(), clientPkId, kcAddedList);
		}
	}
	
	/**
	 * 获取用户在keycloak中拥有的client下的所有角色名称.<br/>
	 * @param user
	 * @param clientId
	 * @return List of role name
	 */
	private List<String> getUserClientKcRoleNames(User user, String clientId) {
		Map<String, List<Role>> clientRoleMap = user.getClientRoleMap();
		if(MapUtils.isEmpty(clientRoleMap)) {
			return Collections.emptyList();
		}
		
		if(!clientRoleMap.containsKey(clientId)) {
			return Collections.emptyList();
		}
		
		List<Role> kcRoles = clientRoleMap.get(clientId);
		if(CollectionUtils.isEmpty(kcRoles)) {
			return Collections.emptyList();
		}
		
		return kcRoles.stream().map(Role::getName).collect(Collectors.toList());
	}
	
	private String appendStoreIdsJson(String storeIds, List<String> addedStoreIds) {
		List<String> originStoreIdList = JacksonUtils.readValue(storeIds, new TypeReference<List<String>>() {});
		for (String storeId : addedStoreIds) {
			if(!originStoreIdList.contains(storeId)) {
				originStoreIdList.add(storeId);
			}
		}
		return JacksonUtils.object2JsonString(originStoreIdList);
	}
	
	public void removeRoleStore(User user, String clientId, String clientPkId, List<RoleStoreDto> removedList) {
		String modifier = UserHolder.getUserInfo() == null ? "" : UserHolder.getUserInfo().getEmail();

		List<UserRole> userRoleStoreList = getByClientIdAnsUserId(clientId, user.getUserId());
		
		UserRole entity;
		boolean exist;
		List<String> removeList = new ArrayList<>();
		for (RoleStoreDto dto : removedList) {
			exist = false;
			// 已有role,只需要修改对应的store列表
			for (UserRole roleStorePo : userRoleStoreList) {
				if(dto.getName().equals(roleStorePo.getRoleName()) && !BkdConstants.SysRole.contains(dto.getName())) {
					// 删除store后门店列表为空=删除权限
					List<String> storeList = removeStoreIdsJson(roleStorePo.getStoreIds(), dto.getStoreIds());
					if(CollectionUtils.isNotEmpty(storeList)) {
						exist = true;
						entity = new UserRole();
						entity.setId(roleStorePo.getId());
						entity.setStoreIds(JacksonUtils.object2JsonString(storeList));
						entity.setModifyGmt(new Date());
						entity.setModifier(modifier);
						updateByPrimaryKeySelective(entity);
					}
				}
			}
			
			// 没有role, 需要删除权限
			if(!exist) {
				removeList.add(dto.getName());
			}
		}
		
		if(CollectionUtils.isNotEmpty(removeList)) {
			removeUserClientRole(user.getUserId(), clientId, clientPkId, removeList);
		}
	}
	
	private List<String> removeStoreIdsJson(String storeIds, List<String> removeStoreIds) {
		List<String> originStoreIdList = JacksonUtils.readValue(storeIds, new TypeReference<List<String>>() {});
		originStoreIdList.removeAll(removeStoreIds);
		return originStoreIdList;
	}
	
	public void appendRole(String clientId, List<RoleStoreDto> roleStores) {
		RoleStoreDto appendRoleStoreDto = roleStores.get(0);
		String userId = UserHolder.getUserInfo().getUserId();
		// 0、删除老的
		UserRole criteria = new UserRole();
		criteria.setUserId(userId);
		criteria.setClientId(clientId);
		criteria.setRoleName(appendRoleStoreDto.getName());
		this.deleteSelective(criteria);
		
		// 1、接入新记录				
		UserRole userRolePo = new UserRole();
		userRolePo.init();
		userRolePo.setClientId(clientId);
		userRolePo.setRoleName(appendRoleStoreDto.getName());
		userRolePo.setUserId(userId);
		userRolePo.setStoreIds(JacksonUtils.object2JsonString(appendRoleStoreDto.getStoreIds()));
		userRolePo.setCreator(UserHolder.getUserInfo() == null ? "" : UserHolder.getUserInfo().getEmail());
		this.insert(userRolePo);
		
		String clientPkId = keycloakManager.getClientPkId(clientId);
		keycloakManager.appendUserClientRole(userId, clientPkId, Collections.singletonList(appendRoleStoreDto.getName()));
	}
	
	public Map<String, List<UserRole>> getUserRoleStoreMap(String userId) {
		List<UserRole> userRoles = this.getByUserId(userId);
        return userRoles.stream().collect(Collectors.groupingBy(UserRole::getClientId));
	}
}