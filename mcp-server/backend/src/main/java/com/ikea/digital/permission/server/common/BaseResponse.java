package com.ikea.digital.permission.server.common;

import lombok.Data;

@Data
public class BaseResponse<T> {

	private Integer code; 
	
    private String message;
    
    private T data;

    public static <T> BaseResponse<T> success() {
        return success(null);
    }

    public static <T> BaseResponse<T> success(T object) {
        BaseResponse<T> result = new BaseResponse<>();
        result.setCode(RespCodeEnum.SUCCESS.getCode());
        result.setMessage(RespCodeEnum.SUCCESS.getMsg());
        result.setData(object);
        return result;
    }

    public static <T> BaseResponse<T> error(RespCodeEnum codeEnum) {
        return error(codeEnum.getCode(), codeEnum.getMsg(), null);
    }

    public static <T> BaseResponse<T> error(RespCodeEnum codeEnum, T data) {
        return error(codeEnum.getCode(), codeEnum.getMsg(), data);
    }

    public static <T> BaseResponse<T> error(Integer code, String msg, T data) {
        BaseResponse<T> result = new BaseResponse<>();
        result.setCode(code);
        result.setMessage(msg);
        result.setData(data);
        return result;
    }
    
}
