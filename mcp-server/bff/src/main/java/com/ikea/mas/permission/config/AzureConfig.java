package com.ikea.mas.permission.config;

import java.util.Arrays;
import java.util.List;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.azure.identity.ClientSecretCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.ikea.mas.permission.config.prop.AzureProperties;
import com.microsoft.graph.authentication.TokenCredentialAuthProvider;
import com.microsoft.graph.requests.GraphServiceClient;

import okhttp3.Request;

@EnableConfigurationProperties(AzureProperties.class)
@Configuration
public class AzureConfig {

	final static List<String> scopes = Arrays.asList("https://graph.microsoft.com/.default");
	
	@Bean
	public GraphServiceClient<Request> graphClient(AzureProperties azureConfig) throws Exception {
		ClientSecretCredential credential = new ClientSecretCredentialBuilder().clientId(azureConfig.getClientId()).tenantId(azureConfig.getTenantId()).clientSecret(azureConfig.getSecretKey()).build();

		if (null == scopes || null == credential) {
		    throw new RuntimeException("Unexpected error");
		}
		TokenCredentialAuthProvider authProvider = new TokenCredentialAuthProvider(scopes, credential);

		return GraphServiceClient.builder().authenticationProvider(authProvider).buildClient();
	}
	
}
