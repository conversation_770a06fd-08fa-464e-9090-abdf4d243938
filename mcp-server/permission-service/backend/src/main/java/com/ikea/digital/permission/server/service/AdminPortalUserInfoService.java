/**
 * Project Name:permission-server
 * File Name:AdminPortalUserInfoService.java
 * Package Name:com.ikea.digital.permission.server.resource.service
 * Date:Feb 26, 20244:42:52 PM
 * Copyright (c) 2024, theodore.ni All Rights Reserved.
 *
 */
package com.ikea.digital.permission.server.service;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.ikea.digital.permission.server.common.BkdConstants;
import com.ikea.digital.permission.server.resource.domain.model.UserRole;
import com.ikea.digital.permission.server.vo.UserDetailVo;

/**
 * ClassName:AdminPortalUserInfoService <br/>
 * Function: admin portal authLib 获取用户信息, 只返回用户Group、Role等基本信息. <br/>
 * Date:     Feb 26, 2024 4:42:52 PM <br/>
 * <AUTHOR>	 
 */
@Service
public class AdminPortalUserInfoService extends AbstractUserInfoService {
	
	@Override
	protected void fillUserDetailInfo(UserDetailVo user, Map<String, List<String>> attrMap, String clientId) {
		fillUserAttr(user, attrMap);
		return;
	}

	@Override
	protected void fillUserRolesAndStores(UserDetailVo user, Map<String, List<UserRole>> userRoleStoreMap) {
	}
	
	@Override
	public boolean supportClient(String clientId) {
		return BkdConstants.ADMIN_PORTAL.equals(clientId);
	}

}