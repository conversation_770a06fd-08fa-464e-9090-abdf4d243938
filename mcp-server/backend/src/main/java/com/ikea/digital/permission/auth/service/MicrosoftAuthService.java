package com.ikea.digital.permission.auth.service;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.ikea.digital.permission.auth.dto.AzureUserDto;
import com.ikea.digital.permission.server.feign.AzureGraphClient;
import com.ikea.mas.permission.utils.RedisHelper;

import feign.FeignException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class MicrosoftAuthService {

	@Autowired
	private AzureGraphClient azureGraphClient;
	
	@Autowired
	private RedisHelper redisHelper;
	
	private static final String BEARER_TOKEN_PREFIX = "Bearer ";
	
	private static final String AZURE_USER_FIELDS = "displayName,userPrincipalName,jobTitle,"
			+ "officeLocation,department,onPremisesSamAccountName,employeeId";
	
	public String createTokenWithCode(String code) {
		String azureToken = redisHelper.get(code);
		if(StringUtils.isBlank(azureToken)) {
			log.error("code:{} invalid!", code);
			return StringUtils.EMPTY;
		}
		return azureToken;
	}
	
	public Map<String, String> fetchUserInfo(String accessToken) {
		AzureUserDto azureUser = getAzureUserInfo(accessToken);
		if(Objects.isNull(azureUser)) {
			return Collections.emptyMap();
		}
//		log.info("user from azure " + JacksonUtils.object2JsonString(node));
		
		Map<String, String> map = new HashMap<>();
		map.put("name", azureUser.getName());
		map.put("preferred_username", azureUser.getEmail());
		map.put("email", azureUser.getEmail());
		map.put("sub", azureUser.getSub());
		map.put("OnPremisesSamAccountName", azureUser.getOnPremisesSamAccountName());
		return map;
	}
	
	public AzureUserDto getAzureUserInfo(String accessToken) {
		String bearerToken = StringUtils.substring(accessToken, BEARER_TOKEN_PREFIX.length());
        DecodedJWT jwt = JWT.decode(bearerToken);
        String email = jwt.getClaim("unique_name").asString();
        
        try {
	        ResponseEntity<AzureUserDto> azureResponse = azureGraphClient.getUserDetail(accessToken, email, AZURE_USER_FIELDS);
	        if (azureResponse.getStatusCode().equals(HttpStatus.OK)) {
	        	AzureUserDto azureUser = new AzureUserDto();
	        	azureUser = azureResponse.getBody();
	        	azureUser.setSub(jwt.getClaim("xms_st").as(JwtSub.class).getSub());
	            azureUser.setEmail(email);
	            azureUser.setName(jwt.getClaim("name").asString());
	            return azureUser;
	        } else {
	        	log.error("query:{} from azure response:{}", email, azureResponse.getStatusCode().value());
	        }
        } catch (FeignException e) {
			log.error("query:{} error, {}", email, e.getMessage());
		} catch (Exception e) {
			log.error("query:{} error.", email, e);
		}
        
        return null;
    }
	
	@Data
	public static class JwtSub {
		String sub;
	}

}
